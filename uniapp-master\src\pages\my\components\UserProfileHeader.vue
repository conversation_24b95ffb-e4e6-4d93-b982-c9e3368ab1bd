<template>
  <view
    class="bg-gradient-to-r from-indigo-600 to-violet-600 relative overflow-hidden"
  >
    <!-- 装饰元素 -->
    <view class="absolute inset-0 overflow-hidden">
      <view
        class="absolute -right-10 -top-10 w-40 h-40 rounded-full bg-white/10 backdrop-blur-md"
      ></view>
      <view
        class="absolute -left-5 top-1/4 w-20 h-20 rounded-full bg-white/10 backdrop-blur-md"
      ></view>
    </view>

    <!-- 个人信息卡片 -->
    <view class="relative px-4 py-6" :style="{ paddingTop: `${topSafeHeight + 24}px` }">
      <!-- 未登录状态 -->
      <view v-if="!isLoggedIn" class="flex items-center justify-between">
        <view class="flex items-center">
          <!-- 小程序中头像容器 -->
          <view
            class="w-16 h-16 overflow-hidden"
            style="border-radius: 50%; background-color: rgba(229, 231, 235, 1)"
          >
            <view class="w-full h-full flex items-center justify-center">
              <text class="i-carbon-user text-2xl text-gray-400"></text>
            </view>
          </view>
          <view class="ml-4">
            <text class="text-lg font-medium text-white">未登录</text>
            <text class="text-xs text-indigo-100 opacity-80 block mt-1">登录体验更多功能</text>
          </view>
        </view>
        <view class="px-5 py-2 bg-white/20 backdrop-blur-sm rounded-full" @click="toLogin">
          <text class="text-white text-sm font-medium">立即登录</text>
        </view>
      </view>

      <!-- 已登录状态 -->
      <view v-else class="flex items-center justify-between">
        <view class="flex items-center">
          <!-- 小程序中头像容器 -->
          <view class="w-16 h-16 overflow-hidden relative" style="border-radius: 50%">
            <!-- 边框效果 -->
            <view
              class="absolute inset-0 rounded-full"
              style="border: 2px solid rgba(255, 255, 255, 0.3)"
            ></view>
            <!-- 头像按钮，无论是否有头像都可以点击更换 -->
            <button
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
              class="w-full h-full flex items-center justify-center"
            >
              <!-- 如果没有头像，显示默认提示 -->
              <view v-if="!userInfo.avatar" class="w-full h-full flex items-center justify-center bg-gray-200">
                <text class="text-gray-500 text-sm">设置头像</text>
              </view>
              <!-- 如果有头像，显示头像 -->
            <image
                v-else
              class="w-full h-full"
                :src="userInfo.avatar"
              mode="aspectFill"
            ></image>
            </button>
          </view>
          <view class="ml-4">
            <text class="text-lg font-medium text-white">
              {{ userInfo.name || '用户' }}
            </text>
            <text class="text-xs text-indigo-100 opacity-80 block mt-1">{{ userInfo.phone || '未绑定手机号' }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface UserInfo {
  id?: number
  name?: string
  phone?: string
  avatar?: string | null
  token?: string
}

interface UserStats {
  favorites: number
  following: number
  followers: number
  history: number
}

const props = defineProps<{
  userInfo: UserInfo
  isLoggedIn: boolean
  userStats: UserStats
  topSafeHeight: number
}>()

const emit = defineEmits(['login', 'editProfile', 'updateAvatar'])

const toLogin = () => {
  emit('login')
}

const editProfile = () => {
  emit('editProfile')
}

// 处理头像选择
const onChooseAvatar = (e: any) => {
  const { avatarUrl } = e.detail
  emit('updateAvatar', avatarUrl)
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'UserProfileHeader',
})
</script>

<style>
/* 移除按钮的默认样式 */
button {
  padding: 0;
  margin: 0;
  background: none;
  border: none;
  outline: none;
}
button::after {
  border: none;
}
</style>
