<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认配置，将会合并到各模块中
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        /*
         * 指定 API 调用返回结果的类型：array(default)/object/raw/collection
         */
        'response_type' => 'array',
        
        /*
         * 使用 Laravel 的缓存系统
         */
        'use_laravel_cache' => true,
        
        /*
         * 日志配置
         */
        'log' => [
            'default' => 'single',
            'channels' => [
                'single' => [
                    'driver' => 'single',
                    'path' => storage_path('logs/wechat.log'),
                    'level' => 'debug',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 小程序
    |--------------------------------------------------------------------------
    */
    'mini_program' => [
        'default' => [
        'app_id'  => env('WECHAT_MINI_PROGRAM_APPID', ''),
        'secret'  => env('WECHAT_MINI_PROGRAM_SECRET', ''),

            /**
             * 接口请求相关配置，超时时间等，具体可用参数请参考：
             * https://github.com/symfony/symfony/blob/5.3/src/Symfony/Contracts/HttpClient/HttpClientInterface.php
             */
            'http' => [
                'timeout' => 5.0,
                'retry' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信支付
    |--------------------------------------------------------------------------
    */
    'payment' => [
        'default' => [
            'app_id'      => env('WECHAT_MINI_PROGRAM_APPID', ''),
            'mch_id'      => env('WECHAT_PAYMENT_MCH_ID', ''),
            // EasyWeChat 6.x 需要的参数
            'secret_key'  => env('WECHAT_PAYMENT_V3_KEY', ''),
            'private_key' => file_get_contents(storage_path('wechat/apiclient_key.pem')),
            'certificate' => file_get_contents(storage_path('wechat/apiclient_cert.pem')),
            'serial_no'   => env('WECHAT_PAYMENT_SERIAL_NO', ''),
            // 证书文件路径（如果使用文件方式）
            'cert_path'   => env('WECHAT_PAYMENT_CERT_PATH', ''),
            'key_path'    => env('WECHAT_PAYMENT_KEY_PATH', ''),
            'notify_url'  => env('WECHAT_PAYMENT_NOTIFY_URL', env('APP_URL') . '/api/payment/notify'),
        ],
    ],
];
