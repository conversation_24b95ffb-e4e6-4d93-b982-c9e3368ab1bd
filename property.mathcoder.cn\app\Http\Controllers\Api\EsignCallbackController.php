<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Services\EsignService;

class EsignCallbackController extends Controller
{
    public function handle(Request $request, EsignService $service)
    {
        // e签宝回调所有数据
        $payload = $request->all();
        $service->handleCallback($payload);

        // 必须返回 success 告诉 e签宝收到
        return response('success');
    }
}
