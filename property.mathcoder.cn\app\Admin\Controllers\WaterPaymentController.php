<?php

namespace App\Admin\Controllers;

use App\Models\WaterPayment;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;

class WaterPaymentController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(WaterPayment::with(['user']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('payment_no', '缴费单号')->copyable();
            $grid->column('user.name', '用户姓名');
            $grid->column('user.phone', '用户手机');
            $grid->column('water_meter_no', '水表号');
            $grid->column('amount', '缴费金额')->display(function ($amount) {
                return '¥' . number_format($amount, 2);
            })->sortable();
            
            $grid->column('payment_method', '支付方式')->display(function ($method) {
                return WaterPayment::getPaymentMethodOptions()[$method] ?? '未知';
            });
            
            $grid->column('status', '支付状态')->display(function ($status) {
                $options = WaterPayment::getStatusOptions();
                $colors = [
                    'pending' => 'warning',
                    'paid' => 'success',
                    'failed' => 'danger',
                    'refunded' => 'info'
                ];
                $text = $options[$status] ?? '未知';
                $color = $colors[$status] ?? 'secondary';
                return "<span class='label label-{$color}'>{$text}</span>";
            });
            
            $grid->column('transaction_no', '支付流水号');
            $grid->column('paid_at', '支付时间')->sortable();
            $grid->column('created_at', '创建时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '用户')->select(User::pluck('name', 'id'));
                $filter->like('payment_no', '缴费单号');
                $filter->like('water_meter_no', '水表号');
                $filter->equal('status', '支付状态')->select(WaterPayment::getStatusOptions());
                $filter->equal('payment_method', '支付方式')->select(WaterPayment::getPaymentMethodOptions());
                $filter->between('amount', '缴费金额');
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 如果是待支付状态，可以手动标记为已支付
                if ($this->status === 'pending') {
                    $actions->append('<a href="javascript:void(0);" class="btn btn-xs btn-success mark-paid" data-id="'.$this->id.'">标记已付</a>');
                }
            });

            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="javascript:void(0);" class="btn btn-sm btn-primary" onclick="exportData()">导出数据</a>');
            });

            // 默认按创建时间倒序
            $grid->model()->orderBy('created_at', 'desc');

            // 添加JavaScript
            \Dcat\Admin\Admin::script($this->script());
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, WaterPayment::with(['user']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('payment_no', '缴费单号');
            $show->field('user.name', '用户姓名');
            $show->field('user.phone', '用户手机');
            $show->field('water_meter_no', '水表号');
            $show->field('amount', '缴费金额')->as(function ($amount) {
                return '¥' . number_format($amount, 2);
            });
            $show->field('payment_method', '支付方式')->as(function ($method) {
                return WaterPayment::getPaymentMethodOptions()[$method] ?? '未知';
            });
            $show->field('status', '支付状态')->as(function ($status) {
                return WaterPayment::getStatusOptions()[$status] ?? '未知';
            });
            $show->field('transaction_no', '支付流水号');
            $show->field('paid_at', '支付时间');
            $show->field('remark', '备注');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(WaterPayment::class, function (Form $form) {
            $form->display('id', 'ID');
            
            $form->select('user_id', '用户')
                ->options(User::pluck('name', 'id'))
                ->required();
                
            $form->text('payment_no', '缴费单号')
                ->default(WaterPayment::generatePaymentNo())
                ->required();
                
            $form->text('water_meter_no', '水表号')->required();
            
            $form->currency('amount', '缴费金额')
                ->symbol('¥')
                ->required();
                
            $form->select('payment_method', '支付方式')
                ->options(WaterPayment::getPaymentMethodOptions())
                ->default('wechat')
                ->required();
                
            $form->select('status', '支付状态')
                ->options(WaterPayment::getStatusOptions())
                ->default('pending')
                ->required();
                
            $form->text('transaction_no', '支付流水号');
            
            $form->datetime('paid_at', '支付时间');
            
            $form->textarea('remark', '备注');

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }

    /**
     * 手动标记为已支付
     */
    public function markPaid($id)
    {
        $payment = WaterPayment::findOrFail($id);

        if ($payment->status !== 'pending') {
            return response()->json([
                'status' => false,
                'message' => '只能标记待支付的记录'
            ]);
        }

        $payment->update([
            'status' => 'paid',
            'paid_at' => now(),
            'transaction_no' => 'MANUAL_' . date('YmdHis'),
            'remark' => ($payment->remark ? $payment->remark . ' | ' : '') . '管理员手动标记已支付'
        ]);

        return response()->json([
            'status' => true,
            'message' => '标记成功'
        ]);
    }

    /**
     * 页面JavaScript
     */
    protected function script()
    {
        return <<<JS
        $(document).on('click', '.mark-paid', function() {
            var id = $(this).data('id');
            var that = $(this);

            Dcat.confirm('确定要标记为已支付吗？', '', function() {
                $.post('/admin/water-payment/' + id + '/mark-paid', {
                    _token: Dcat.token
                }, function(result) {
                    if (result.status) {
                        Dcat.success(result.message);
                        Dcat.reload();
                    } else {
                        Dcat.error(result.message);
                    }
                });
            });
        });

        function exportData() {
            Dcat.info('导出功能开发中...');
        }
JS;
    }
}
