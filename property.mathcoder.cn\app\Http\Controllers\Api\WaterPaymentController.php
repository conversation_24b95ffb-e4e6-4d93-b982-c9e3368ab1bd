<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WaterPayment;
use App\Models\ActiveAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use EasyWeChat\MiniApp\Application;
use App\Models\ContractSign;

class WaterPaymentController extends Controller
{
    /**
     * 检查水表号是否签署过合同
     */
    private function checkContractStatus($waterMeterNo, $userId)
    {
        $contract = ContractSign::where('water_meter_number', $waterMeterNo)
            ->where('user_id', $userId)
            ->where('status', 'signed') // 只检查有效合同
            ->first();

        return $contract !== null;
    }

    /**
     * 验证合同状态，如果没有合同则阻止操作
     */
    private function validateContract($waterMeterNo, $userId)
    {
        if (!$this->checkContractStatus($waterMeterNo, $userId)) {
            return response()->json([
                'code' => 403,
                'message' => '该水表号未签署合同，无法进行充值缴费操作'
            ]);
        }

        return null; // 返回null表示验证通过
    }

    /**
     * 获取用户缴费记录列表
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $query = WaterPayment::where('user_id', $user->id);

        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // 账户筛选（通过水表号）
        if ($request->has('account_id') && $request->account_id !== '') {
            // 根据account_id获取对应的水表号
            $account = ActiveAccount::where('user_id', $user->id)
                ->where('id', $request->account_id)
                ->first();

            if ($account && $account->water_meter_number) {
                $query->where('water_meter_no', $account->water_meter_number);
            }
        }

        $payments = $query->orderBy('created_at', 'desc')
            ->paginate(10);

        // 为每条记录添加地址等信息
        $payments->getCollection()->transform(function ($payment) use ($user) {
            // 根据水表号获取账户信息
            $account = ActiveAccount::where('user_id', $user->id)
                ->where('water_meter_number', $payment->water_meter_no)
                ->first();

            if ($account) {
                $payment->address = $account->community . ' ' . $account->building_number;
                $payment->user_name = $account->name;
                $payment->phone = $account->phone;
            }

            return $payment;
        });

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $payments
        ]);
    }

    /**
     * 创建缴费订单
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'water_meter_no' => 'required|string',
            'amount' => 'required|numeric|min:0.01'
        ]);

        $user = $request->user();

        // 验证水表号是否属于当前用户
        $account = ActiveAccount::where('user_id', $user->id)
            ->where('water_meter_number', $data['water_meter_no'])
            ->first();

        if (!$account) {
            return response()->json([
                'code' => 400,
                'message' => '水表号不存在或不属于您'
            ]);
        }

        // 创建缴费记录
        $payment = WaterPayment::create([
            'user_id' => $user->id,
            'payment_no' => WaterPayment::generatePaymentNo(),
            'water_meter_no' => $data['water_meter_no'],
            'amount' => $data['amount'],
            'payment_method' => 'wechat',
            'status' => 'pending'
        ]);

        return response()->json([
            'code' => 0,
            'message' => '缴费订单创建成功',
            'data' => $payment
        ]);
    }

    /**
     * 获取缴费详情
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();

        $payment = WaterPayment::where('user_id', $user->id)
            ->where('id', $id)
            ->first();

        if (!$payment) {
            return response()->json([
                'code' => 404,
                'message' => '缴费记录不存在'
            ]);
        }

        // 根据水表号获取账户信息
        $account = ActiveAccount::where('user_id', $user->id)
            ->where('water_meter_number', $payment->water_meter_no)
            ->first();

        if ($account) {
            $payment->address = $account->community . ' ' . $account->building_number;
            $payment->user_name = $account->name;
            $payment->phone = $account->phone;
        }

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $payment
        ]);
    }

    /**
     * 发起微信支付
     */
    public function pay(Request $request, $id)
    {
        $user = $request->user();
        
        $payment = WaterPayment::where('user_id', $user->id)
            ->where('id', $id)
            ->where('status', 'pending')
            ->first();

        if (!$payment) {
            return response()->json([
                'code' => 404,
                'message' => '缴费记录不存在或已支付'
            ]);
        }

        try {
            // 获取微信支付配置
            $config = config('wechat.payment.default');
            $app = new \EasyWeChat\Pay\Application($config);

            // 准备支付参数（APIv3格式）
            $orderData = [
                'appid' => $config['app_id'],
                'mchid' => $config['mch_id'],
                'description' => '水费缴纳-' . $payment->water_meter_no,
                'out_trade_no' => $payment->payment_no,
                'amount' => [
                    'total' => (int)($payment->amount * 100), // 确保是整数
                    'currency' => 'CNY',
                ],
                'payer' => [
                    'openid' => $user->openid,
                ],
                'notify_url' => $config['notify_url'],
            ];

            Log::info('微信支付请求参数', $orderData);

            // 统一下单（APIv3）
            try {
                $result = $app->getClient()->postJson('v3/pay/transactions/jsapi', $orderData);
                $response = $result->toArray();
            } catch (\Exception $e) {
                Log::error('微信支付API调用失败', [
                    'error' => $e->getMessage(),
                    'orderData' => $orderData
                ]);
                return response()->json([
                    'code' => 500,
                    'message' => '支付下单失败：' . $e->getMessage()
                ]);
            }

            if (isset($response['prepay_id'])) {
                // 手动构建小程序支付参数（避免EasyWeChat的时间戳bug）
                $timeStamp = (string)time();
                $nonceStr = \Illuminate\Support\Str::random(32);
                $package = 'prepay_id=' . $response['prepay_id'];

                // 构建签名消息
                $message = $config['app_id'] . "\n" . $timeStamp . "\n" . $nonceStr . "\n" . $package . "\n";

                // 使用私钥生成签名
                $privateKey = file_get_contents(storage_path('wechat/apiclient_key.pem'));
                openssl_sign($message, $signature, $privateKey, 'sha256WithRSAEncryption');
                $paySign = base64_encode($signature);

                $paymentParams = [
                    'appId' => $config['app_id'],
                    'timeStamp' => $timeStamp,
                    'nonceStr' => $nonceStr,
                    'package' => $package,
                    'signType' => 'RSA',
                    'paySign' => $paySign
                ];

                Log::info('手动生成的支付参数', $paymentParams);

                return response()->json([
                    'code' => 0,
                    'message' => '支付参数获取成功',
                    'data' => [
                        'payment_id' => $payment->id,
                        'payment_params' => $paymentParams
                    ]
                ]);
            } else {
                Log::error('微信支付下单失败', $response);
                return response()->json([
                    'code' => 500,
                    'message' => '支付下单失败：' . ($response['message'] ?? '未知错误')
                ]);
            }

        } catch (\Exception $e) {
            Log::error('微信支付异常：' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '支付服务异常，请稍后重试'
            ]);
        }
    }

    /**
     * 微信支付回调
     */
    public function notify(Request $request)
    {
        try {
            $config = config('wechat.payment.default');
            $app = new \EasyWeChat\Pay\Application($config);

            $server = $app->getServer();

            $server->handlePaid(function ($message, $fail) {
                Log::info('微信支付回调', $message->toArray());

                // 根据订单号查找缴费记录
                $payment = WaterPayment::where('payment_no', $message['out_trade_no'])->first();

                if (!$payment) {
                    Log::error('支付回调：找不到订单', ['out_trade_no' => $message['out_trade_no']]);
                    return $fail('订单不存在');
                }

                // 检查支付状态
                if ($payment->status === 'paid') {
                    return true; // 已经处理过了
                }

                // 更新支付状态（APIv3格式）
                if ($message['trade_state'] === 'SUCCESS') {
                    $payment->update([
                        'status' => 'paid',
                        'transaction_no' => $message['transaction_id'],
                        'paid_at' => now(),
                        'remark' => ($payment->remark ? $payment->remark . ' | ' : '') . '微信支付成功'
                    ]);

                    Log::info('支付成功', ['payment_id' => $payment->id, 'transaction_id' => $message['transaction_id']]);
                    return true;
                } else {
                    $payment->update([
                        'status' => 'failed',
                        'remark' => ($payment->remark ? $payment->remark . ' | ' : '') . '支付失败：' . ($message['trade_state_desc'] ?? '未知错误')
                    ]);

                    Log::error('支付失败', ['payment_id' => $payment->id, 'error' => $message['trade_state_desc'] ?? '未知错误']);
                    return $fail('支付失败');
                }
            });

            return $server->serve();
            
        } catch (\Exception $e) {
            Log::error('支付回调处理异常：' . $e->getMessage());
            return response('FAIL');
        }
    }

    /**
     * 查询支付状态
     */
    public function checkStatus(Request $request, $id)
    {
        $user = $request->user();
        
        $payment = WaterPayment::where('user_id', $user->id)
            ->where('id', $id)
            ->first();

        if (!$payment) {
            return response()->json([
                'code' => 404,
                'message' => '缴费记录不存在'
            ]);
        }

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'status' => $payment->status,
                'paid_at' => $payment->paid_at,
                'transaction_no' => $payment->transaction_no
            ]
        ]);
    }

    /**
     * 检查水表号合同状态（支付前置检查接口）
     */
    public function checkContract(Request $request, $water_meter_no)
    {
        $user = $request->user();

        // 检查水表号是否属于当前用户
        $account = ActiveAccount::where('user_id', $user->id)
            ->where('water_meter_number', $water_meter_no)
            ->first();

        if (!$account) {
            return response()->json([
                'code' => 404,
                'message' => '水表号不存在或不属于您',
                'data' => [
                    'has_contract' => false
                ]
            ]);
        }

        // 检查合同状态
        $hasContract = $this->checkContractStatus($water_meter_no, $user->id);

        if ($hasContract) {
            return response()->json([
                'code' => 0,
                'message' => '合同检查通过',
                'data' => [
                    'has_contract' => true,
                    'water_meter_no' => $water_meter_no,
                    'account_info' => [
                        'name' => $account->name,
                        'phone' => $account->phone,
                        'address' => $account->community . ' ' . $account->building_number
                    ]
                ]
            ]);
        } else {
            return response()->json([
                'code' => 403,
                'message' => '该水表号未签署合同，无法进行充值缴费操作',
                'data' => [
                    'has_contract' => false,
                    'water_meter_no' => $water_meter_no
                ]
            ]);
        }
    }
}
