<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Imagick;

class DocumentController extends ApiController
{
    /**
     * 将文档转换为图片
     */
    public function convertToImages(Request $request)
    {
        try {
            $filePath = $request->input('file_path');
            $contractId = $request->input('contract_id');

            if (!$filePath) {
                return $this->error('文件路径不能为空');
            }

            // 检查文件是否存在
            // 文件路径格式：files/xxx.pdf，实际存储在 storage/app/public/files/xxx.pdf
            $fullPath = storage_path('app/public/' . $filePath);
            if (!file_exists($fullPath)) {
                return $this->error('文件不存在: ' . $fullPath);
            }

            // 获取文件扩展名
            $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

            // 生成缓存目录
            $cacheDir = 'document_images/' . md5($filePath);
            $cachePath = storage_path('app/public/' . $cacheDir);

            // 检查是否已经转换过
            if (is_dir($cachePath)) {
                $existingImages = glob($cachePath . '/*.jpg');
                if (!empty($existingImages)) {
                    $imageUrls = [];
                    foreach ($existingImages as $imagePath) {
                        $filename = basename($imagePath);
                        $imageUrls[] = url('storage/' . $cacheDir . '/' . $filename);
                    }
                    sort($imageUrls); // 确保页面顺序正确
                    return $this->success(['pages' => $imageUrls]);
                }
            }

            // 创建缓存目录
            if (!is_dir($cachePath)) {
                mkdir($cachePath, 0755, true);
            }

            $imageUrls = [];

            // 根据文件类型进行转换
            switch ($extension) {
                case 'pdf':
                    $imageUrls = $this->convertPdfToImages($fullPath, $cachePath, $cacheDir);
                    break;
                default:
                    // 其他格式暂时使用占位图片
                    $placeholderImage = $this->createPlaceholderImage($cachePath, $cacheDir, $extension);
                    if ($placeholderImage) {
                        $imageUrls[] = $placeholderImage;
                    }
                    break;
            }

            if (empty($imageUrls)) {
                return $this->error('文档转换失败');
            }

            return $this->success(['pages' => $imageUrls]);

        } catch (\Exception $e) {
            Log::error('文档转换失败: ' . $e->getMessage());
            return $this->error('文档转换失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建占位图片
     */
    private function createPlaceholderImage($cachePath, $cacheDir, $extension)
    {
        try {
            $filename = 'placeholder.jpg';
            $imagePath = $cachePath . '/' . $filename;

            // 创建一个简单的占位图片
            $width = 800;
            $height = 1000;
            $image = imagecreate($width, $height);

            // 设置颜色
            $white = imagecolorallocate($image, 255, 255, 255);
            $black = imagecolorallocate($image, 0, 0, 0);
            $gray = imagecolorallocate($image, 128, 128, 128);

            // 填充背景
            imagefill($image, 0, 0, $white);

            // 添加边框
            imagerectangle($image, 0, 0, $width-1, $height-1, $gray);

            // 添加文字
            $text1 = strtoupper($extension) . ' Document';
            $text2 = 'Document preview';
            $text3 = 'Click sign button to proceed';

            // 使用内置字体
            imagestring($image, 5, ($width - strlen($text1) * 10) / 2, $height / 2 - 60, $text1, $black);
            imagestring($image, 3, ($width - strlen($text2) * 8) / 2, $height / 2 - 20, $text2, $gray);
            imagestring($image, 3, ($width - strlen($text3) * 8) / 2, $height / 2 + 20, $text3, $gray);

            // 保存图片
            imagejpeg($image, $imagePath, 85);
            imagedestroy($image);

            return url('storage/' . $cacheDir . '/' . $filename);

        } catch (\Exception $e) {
            Log::error('创建占位图片失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 转换PDF为图片
     */
    private function convertPdfToImages($pdfPath, $cachePath, $cacheDir)
    {
        try {
            // 方案1：使用Imagick扩展
            if (extension_loaded('imagick')) {
                return $this->convertPdfWithImagick($pdfPath, $cachePath, $cacheDir);
            }

            // 方案2：使用pdf2pic命令行工具
            if ($this->commandExists('pdftoppm')) {
                return $this->convertPdfWithPdftoppm($pdfPath, $cachePath, $cacheDir);
            }

            // 方案3：使用ghostscript
            if ($this->commandExists('gs')) {
                return $this->convertPdfWithGhostscript($pdfPath, $cachePath, $cacheDir);
            }

            // 如果都不可用，返回占位图片
            Log::warning('没有可用的PDF转换工具');
            $placeholderImage = $this->createPlaceholderImage($cachePath, $cacheDir, 'pdf');
            return $placeholderImage ? [$placeholderImage] : [];

        } catch (\Exception $e) {
            Log::error('PDF转换失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 使用Imagick转换PDF
     */
    private function convertPdfWithImagick($pdfPath, $cachePath, $cacheDir)
    {
        $imagick = new \Imagick();
        $imagick->setResolution(150, 150);
        $imagick->readImage($pdfPath);

        $imageUrls = [];
        $pageCount = $imagick->getNumberImages();

        for ($i = 0; $i < $pageCount; $i++) {
            $imagick->setIteratorIndex($i);
            $imagick->setImageFormat('jpg');
            $imagick->setImageCompressionQuality(85);

            $filename = sprintf('page_%03d.jpg', $i + 1);
            $imagePath = $cachePath . '/' . $filename;

            $imagick->writeImage($imagePath);
            $imageUrls[] = url('storage/' . $cacheDir . '/' . $filename);
        }

        $imagick->clear();
        $imagick->destroy();

        return $imageUrls;
    }

    /**
     * 检查命令是否存在
     */
    private function commandExists($command)
    {
        $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ['pipe', 'r'],
                1 => ['pipe', 'w'],
                2 => ['pipe', 'w']
            ],
            $pipes
        );

        if ($process !== false) {
            $result = stream_get_contents($pipes[1]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            proc_close($process);
            return !empty(trim($result));
        }

        return false;
    }

}
