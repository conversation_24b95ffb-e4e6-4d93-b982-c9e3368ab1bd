tinymce.addI18n('hu_HU',{
"Redo": "Ism\u00e9t",
"Undo": "<PERSON><PERSON><PERSON>von\u00e1s",
"Cut": "Kiv\u00e1g\u00e1s",
"Copy": "M\u00e1sol\u00e1s",
"Paste": "Beilleszt\u00e9s",
"Select all": "Minden kijel\u00f6l\u00e9se",
"New document": "\u00daj dokumentum",
"Ok": "Rendben",
"Cancel": "M\u00e9gse",
"Visual aids": "Vizu\u00e1lis seg\u00e9deszk\u00f6z\u00f6k",
"Bold": "F\u00e9lk\u00f6v\u00e9r",
"Italic": "D\u0151lt",
"Underline": "Al\u00e1h\u00fazott",
"Strikethrough": "\u00c1th\u00fazott",
"Superscript": "Fels\u0151 index",
"Subscript": "Als\u00f3 index",
"Clear formatting": "Form\u00e1z\u00e1s t\u00f6rl\u00e9se",
"Align left": "Balra igaz\u00edt",
"Align center": "K\u00f6z\u00e9pre igaz\u00edt",
"Align right": "Jobbra igaz\u00edt",
"Justify": "Sorkiz\u00e1rt",
"Bullet list": "Listajeles lista",
"Numbered list": "Sz\u00e1mozott lista",
"Decrease indent": "Beh\u00faz\u00e1s cs\u00f6kkent\u00e9se",
"Increase indent": "Beh\u00faz\u00e1s n\u00f6vel\u00e9se",
"Close": "Bez\u00e1r",
"Formats": "Form\u00e1tumok",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "A b\u00f6ng\u00e9sz\u0151d nem t\u00e1mogatja a k\u00f6zvetlen hozz\u00e1f\u00e9r\u00e9st a v\u00e1g\u00f3laphoz. K\u00e9rlek, haszn\u00e1ld a Ctrl+X\/C\/V billenty\u0171ket.",
"Headers": "C\u00edmsorok",
"Header 1": "C\u00edmsor 1",
"Header 2": "C\u00edmsor 2",
"Header 3": "C\u00edmsor 3",
"Header 4": "C\u00edmsor 4",
"Header 5": "C\u00edmsor 5",
"Header 6": "C\u00edmsor 6",
"Headings": "Fejl\u00e9cek",
"Heading 1": "1. fejl\u00e9c",
"Heading 2": "2. fejl\u00e9c",
"Heading 3": "3. fejl\u00e9c",
"Heading 4": "4. fejl\u00e9c",
"Heading 5": "5. fejl\u00e9c",
"Heading 6": "6. fejl\u00e9c",
"Preformatted": "El\u0151form\u00e1zott",
"Div": "Div",
"Pre": "Pre",
"Code": "K\u00f3d",
"Paragraph": "Bekezd\u00e9s",
"Blockquote": "Id\u00e9zetblokk",
"Inline": "Foly\u00f3 sz\u00f6veg",
"Blocks": "Blokkok",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Beilleszt\u00e9s mostant\u00f3l egyszer\u0171 sz\u00f6veg m\u00f3dban. A tartalmak mostant\u00f3l egyszer\u0171 sz\u00f6vegk\u00e9nt lesznek beillesztve, am\u00edg nem kapcsolod ki ezt az opci\u00f3t.",
"Fonts": "Bet\u0171t\u00edpusok",
"Font Sizes": "Bet\u0171m\u00e9retek",
"Class": "Oszt\u00e1ly",
"Browse for an image": "K\u00e9p keres\u00e9se tall\u00f3z\u00e1ssal",
"OR": "VAGY",
"Drop an image here": "H\u00fazz ide egy k\u00e9pet",
"Upload": "Felt\u00f6lt\u00e9s",
"Block": "Blokk",
"Align": "Igaz\u00edt\u00e1s",
"Default": "Alap\u00e9rtelmezett",
"Circle": "K\u00f6r",
"Disc": "Pont",
"Square": "N\u00e9gyzet",
"Lower Alpha": "Kisbet\u0171",
"Lower Greek": "Kis g\u00f6r\u00f6g sz\u00e1m",
"Lower Roman": "Kis r\u00f3mai sz\u00e1m",
"Upper Alpha": "Nagybet\u0171",
"Upper Roman": "Nagy r\u00f3mai sz\u00e1m",
"Anchor...": "Horgony...",
"Name": "N\u00e9v",
"Id": "Azonos\u00edt\u00f3",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Az azonos\u00edt\u00f3nak bet\u0171vel kell kezd\u0151dnie, azut\u00e1n csak bet\u0171ket, sz\u00e1mokat, gondolatjeleket, pontokat, kett\u0151spontokat vagy al\u00e1h\u00faz\u00e1st tartalmazhat.",
"You have unsaved changes are you sure you want to navigate away?": "Nem mentett m\u00f3dos\u00edt\u00e1said vannak, biztos hogy el akarsz navig\u00e1lni?",
"Restore last draft": "Utols\u00f3 piszkozat vissza\u00e1ll\u00edt\u00e1sa",
"Special character...": "Speci\u00e1lis karakter...",
"Source code": "Forr\u00e1sk\u00f3d",
"Insert\/Edit code sample": "K\u00f3dminta besz\u00far\u00e1sa\/szerkeszt\u00e9se",
"Language": "Nyelv",
"Code sample...": "K\u00f3dminta...",
"Color Picker": "Sz\u00ednv\u00e1laszt\u00f3",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "Balr\u00f3l jobbra",
"Right to left": "Jobbr\u00f3l balra",
"Emoticons...": "Hangulatjelek...",
"Metadata and Document Properties": "Metaadatok \u00e9s a dokumentum tulajdons\u00e1gai",
"Title": "C\u00edm",
"Keywords": "Kulcsszavak",
"Description": "Le\u00edr\u00e1s",
"Robots": "Robotok",
"Author": "Szerz\u0151",
"Encoding": "K\u00f3dol\u00e1s",
"Fullscreen": "Teljes k\u00e9perny\u0151",
"Action": "M\u0171velet",
"Shortcut": "Parancsikon",
"Help": "S\u00fag\u00f3",
"Address": "C\u00edm",
"Focus to menubar": "F\u00f3kusz a men\u00fcre",
"Focus to toolbar": "F\u00f3kusz az eszk\u00f6zt\u00e1rra",
"Focus to element path": "F\u00f3kusz az elemek \u00fatvonal\u00e1ra",
"Focus to contextual toolbar": "F\u00f3kusz a k\u00f6rnyezetf\u00fcgg\u0151 eszk\u00f6zt\u00e1rra",
"Insert link (if link plugin activated)": "Hivatkoz\u00e1s besz\u00far\u00e1sa (ha a hivatkoz\u00e1s b\u0151v\u00edtm\u00e9ny enged\u00e9lyezett)",
"Save (if save plugin activated)": "Ment\u00e9s (ha a ment\u00e9s b\u0151v\u00edtm\u00e9ny enged\u00e9lyezett)",
"Find (if searchreplace plugin activated)": "Keres\u00e9s (ha a keres\u00e9s \u00e9s csere b\u0151v\u00edtm\u00e9ny enged\u00e9lyezett)",
"Plugins installed ({0}):": "Telep\u00edtett b\u0151v\u00edtm\u00e9nyek ({0}):",
"Premium plugins:": "Pr\u00e9mium b\u0151v\u00edtm\u00e9nyek:",
"Learn more...": "Tudj meg t\u00f6bbet...",
"You are using {0}": "Haszn\u00e1latban: {0}",
"Plugins": "Pluginek",
"Handy Shortcuts": "Hasznos linkek",
"Horizontal line": "V\u00edzszintes vonal",
"Insert\/edit image": "K\u00e9p beilleszt\u00e9se\/szerkeszt\u00e9se",
"Image description": "K\u00e9p le\u00edr\u00e1sa",
"Source": "Forr\u00e1s",
"Dimensions": "M\u00e9retek",
"Constrain proportions": "M\u00e9retar\u00e1ny",
"General": "\u00c1ltal\u00e1nos",
"Advanced": "Halad\u00f3",
"Style": "St\u00edlus",
"Vertical space": "Vertik\u00e1lis hely",
"Horizontal space": "Horizont\u00e1lis hely",
"Border": "Szeg\u00e9ly",
"Insert image": "K\u00e9p beilleszt\u00e9se",
"Image...": "K\u00e9p...",
"Image list": "K\u00e9p lista",
"Rotate counterclockwise": "Forgat\u00e1s az \u00f3ramutat\u00f3 j\u00e1r\u00e1s\u00e1val ellent\u00e9tesen",
"Rotate clockwise": "Forgat\u00e1s az \u00f3ramutat\u00f3 j\u00e1r\u00e1s\u00e1val megegyez\u0151en",
"Flip vertically": "F\u00fcgg\u0151leges t\u00fckr\u00f6z\u00e9s",
"Flip horizontally": "V\u00edzszintes t\u00fckr\u00f6z\u00e9s",
"Edit image": "K\u00e9p szerkeszt\u00e9se",
"Image options": "K\u00e9p be\u00e1ll\u00edt\u00e1sok",
"Zoom in": "Nagy\u00edt\u00e1s",
"Zoom out": "Kicsiny\u00edt\u00e9s",
"Crop": "K\u00e9p v\u00e1g\u00e1s",
"Resize": "\u00c1tm\u00e9retez\u00e9s",
"Orientation": "K\u00e9p t\u00e1jol\u00e1s",
"Brightness": "F\u00e9nyer\u0151",
"Sharpen": "\u00c9less\u00e9g",
"Contrast": "Kontraszt",
"Color levels": "Sz\u00ednszint",
"Gamma": "Gamma",
"Invert": "Inverz k\u00e9p",
"Apply": "Ment\u00e9s",
"Back": "Vissza",
"Insert date\/time": "D\u00e1tum\/id\u0151 beilleszt\u00e9se",
"Date\/time": "D\u00e1tum\/id\u0151",
"Insert\/Edit Link": "Hivatkoz\u00e1s beilleszt\u00e9se\/szerkeszt\u00e9se",
"Insert\/edit link": "Hivatkoz\u00e1s beilleszt\u00e9se\/szerkeszt\u00e9se",
"Text to display": "Megjelen\u0151 sz\u00f6veg",
"Url": "Url",
"Open link in...": "Hivatkoz\u00e1s megnyit\u00e1sa...",
"Current window": "Jelenlegi ablak",
"None": "Nincs",
"New window": "\u00daj ablak",
"Remove link": "Hivatkoz\u00e1s t\u00f6rl\u00e9se",
"Anchors": "Horgonyok",
"Link...": "Hivatkoz\u00e1s...",
"Paste or type a link": "Hivatkoz\u00e1s be\u00edr\u00e1sa vagy beilleszt\u00e9se",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "A megadott URL email c\u00edmnek t\u0171nik. Szeretn\u00e9d hozz\u00e1adni a sz\u00fcks\u00e9ges mailto: el\u0151tagot?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "A megadott URL k\u00fcls\u0151 c\u00edmnek t\u0171nik. Szeretn\u00e9d hozz\u00e1adni a sz\u00fcks\u00e9ges http:\/\/ el\u0151tagot?",
"Link list": "Hivatkoz\u00e1slista",
"Insert video": "Vide\u00f3 beilleszt\u00e9se",
"Insert\/edit video": "Vide\u00f3 beilleszt\u00e9se\/szerkeszt\u00e9se",
"Insert\/edit media": "M\u00e9dia besz\u00far\u00e1sa\/beilleszt\u00e9se",
"Alternative source": "Alternat\u00edv forr\u00e1s",
"Alternative source URL": "Alternat\u00edv forr\u00e1s URL",
"Media poster (Image URL)": "M\u00e9dia poszter (k\u00e9p URL)",
"Paste your embed code below:": "Illeszd be a be\u00e1gyaz\u00f3 k\u00f3dot alulra:",
"Embed": "Be\u00e1gyaz\u00e1s",
"Media...": "M\u00e9dia...",
"Nonbreaking space": "Nem t\u00f6rhet\u0151 sz\u00f3k\u00f6z",
"Page break": "Oldalt\u00f6r\u00e9s",
"Paste as text": "Beilleszt\u00e9s sz\u00f6vegk\u00e9nt",
"Preview": "El\u0151n\u00e9zet",
"Print...": "Nyomtat\u00e1s...",
"Save": "Ment\u00e9s",
"Find": "Keres\u00e9s",
"Replace with": "Csere erre",
"Replace": "Csere",
"Replace all": "Az \u00f6sszes cser\u00e9je",
"Previous": "El\u0151z\u0151",
"Next": "K\u00f6vetkez\u0151",
"Find and replace...": "Keres\u00e9s \u00e9s csere...",
"Could not find the specified string.": "A be\u00edrt kifejez\u00e9s nem tal\u00e1lhat\u00f3.",
"Match case": "Kis \u00e9s nagybet\u0171k megk\u00fcl\u00f6nb\u00f6ztet\u00e9se",
"Find whole words only": "Csak teljes szavak keres\u00e9se",
"Spell check": "Helyes\u00edr\u00e1s-ellen\u0151rz\u00e9s",
"Ignore": "Figyelmen k\u00edv\u00fcl hagy",
"Ignore all": "Mindent figyelmen k\u00edv\u00fcl hagy",
"Finish": "Befejez\u00e9s",
"Add to Dictionary": "Sz\u00f3t\u00e1rhoz ad",
"Insert table": "T\u00e1bl\u00e1zat beilleszt\u00e9se",
"Table properties": "T\u00e1bl\u00e1zat tulajdons\u00e1gok",
"Delete table": "T\u00e1bl\u00e1zat t\u00f6rl\u00e9se",
"Cell": "Cella",
"Row": "Sor",
"Column": "Oszlop",
"Cell properties": "Cella tulajdons\u00e1gok",
"Merge cells": "Cell\u00e1k egyes\u00edt\u00e9se",
"Split cell": "Cell\u00e1k sz\u00e9tv\u00e1laszt\u00e1sa",
"Insert row before": "Sor besz\u00far\u00e1sa el\u00e9",
"Insert row after": "Sor besz\u00far\u00e1sa m\u00f6g\u00e9",
"Delete row": "Sor t\u00f6rl\u00e9se",
"Row properties": "Sor tulajdons\u00e1gai",
"Cut row": "Sor kiv\u00e1g\u00e1sa",
"Copy row": "Sor m\u00e1sol\u00e1sa",
"Paste row before": "Sor beilleszt\u00e9se el\u00e9",
"Paste row after": "Sor beilleszt\u00e9se m\u00f6g\u00e9",
"Insert column before": "Oszlop besz\u00far\u00e1sa el\u00e9",
"Insert column after": "Oszlop besz\u00far\u00e1sa m\u00f6g\u00e9",
"Delete column": "Oszlop t\u00f6rl\u00e9se",
"Cols": "Oszlopok",
"Rows": "Sorok",
"Width": "Sz\u00e9less\u00e9g",
"Height": "Magass\u00e1g",
"Cell spacing": "Cell\u00e1k t\u00e1vols\u00e1ga",
"Cell padding": "Cella m\u00e9rete",
"Show caption": "C\u00edm megjelen\u00edt\u00e9se",
"Left": "Bal",
"Center": "K\u00f6z\u00e9p",
"Right": "Jobb",
"Cell type": "Cella t\u00edpusa",
"Scope": "Hat\u00f3k\u00f6r",
"Alignment": "Igaz\u00edt\u00e1s",
"H Align": "V\u00edzszintes igaz\u00edt\u00e1s",
"V Align": "F\u00fcgg\u0151leges igaz\u00edt\u00e1s",
"Top": "Fel\u00fcl",
"Middle": "K\u00f6z\u00e9pen",
"Bottom": "Alul",
"Header cell": "Fejl\u00e9c cella",
"Row group": "Sor csoport",
"Column group": "Oszlop csoport",
"Row type": "Sor t\u00edpus",
"Header": "Fejl\u00e9c",
"Body": "Sz\u00f6vegt\u00f6rzs",
"Footer": "L\u00e1bl\u00e9c",
"Border color": "Szeg\u00e9ly sz\u00edne",
"Insert template...": "Sablon besz\u00far\u00e1sa...",
"Templates": "Sablonok",
"Template": "Sablon",
"Text color": "Sz\u00f6veg sz\u00edne",
"Background color": "H\u00e1tt\u00e9r sz\u00edn",
"Custom...": "Egy\u00e9ni...",
"Custom color": "Egy\u00e9ni sz\u00edn",
"No color": "Nincs sz\u00edn",
"Remove color": "Sz\u00edn t\u00f6rl\u00e9se",
"Table of Contents": "Tartalomjegyz\u00e9k",
"Show blocks": "Blokkok mutat\u00e1sa",
"Show invisible characters": "L\u00e1thatatlan karakterek mutat\u00e1sa",
"Word count": "Szavak sz\u00e1ma",
"Count": "Sz\u00e1m",
"Document": "Dokumentum",
"Selection": "Kiv\u00e1laszt\u00e1s",
"Words": "Szavak",
"Words: {0}": "Szavak: {0}",
"{0} words": "{0} sz\u00f3",
"File": "F\u00e1jl",
"Edit": "Szerkeszt\u00e9s",
"Insert": "Beilleszt\u00e9s",
"View": "N\u00e9zet",
"Format": "Form\u00e1tum",
"Table": "T\u00e1bl\u00e1zat",
"Tools": "Eszk\u00f6z\u00f6k",
"Powered by {0}": "\u00dczemelteti: {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text ter\u00fclet. Nyomj ALT-F9-et a men\u00fch\u00f6z. Nyomj ALT-F10-et az eszk\u00f6zt\u00e1rhoz. Nyomj ALT-0-t a s\u00fag\u00f3hoz",
"Image title": "K\u00e9p c\u00edme",
"Border width": "Szeg\u00e9ly vastags\u00e1ga",
"Border style": "Szeg\u00e9ly st\u00edlusa",
"Error": "Hiba",
"Warn": "Figyelmeztet\u00e9s",
"Valid": "\u00c9rv\u00e9nyes",
"To open the popup, press Shift+Enter": "A felugr\u00f3 ablak megnyit\u00e1s\u00e1hoz nyomja meg a Shift+Enter billenty\u0171t",
"Rich Text Area. Press ALT-0 for help.": "Vizu\u00e1lis szerkeszt\u0151 ter\u00fclet. Nyomjon ALT-0-t a s\u00fag\u00f3hoz.",
"System Font": "Rendszer-bet\u0171t\u00edpus",
"Failed to upload image: {0}": "Nem siker\u00fclt felt\u00f6lteni a k\u00e9pet: {0}",
"Failed to load plugin: {0} from url {1}": "Nem siker\u00fclt bet\u00f6lteni a be\u00e9p\u00fcl\u0151 modult: {0} err\u0151l a webc\u00edmr\u0151l: {1}",
"Failed to load plugin url: {0}": "Nem siker\u00fclt bet\u00f6lteni a be\u00e9p\u00fcl\u0151 modul url-\u00e9t: {0}",
"Failed to initialize plugin: {0}": "Nem siker\u00fclt inicializ\u00e1lni a be\u00e9p\u00fcl\u0151 modult: {0}",
"example": "p\u00e9lda",
"Search": "Keres\u00e9s",
"All": "Minden",
"Currency": "P\u00e9nznem",
"Text": "Sz\u00f6veg",
"Quotations": "Id\u00e9z\u0151jelek",
"Mathematical": "Matematikai",
"Extended Latin": "B\u0151v\u00edtett latin",
"Symbols": "Szimb\u00f3lumok",
"Arrows": "Nyilak",
"User Defined": "Felhaszn\u00e1l\u00f3 \u00e1ltal meghat\u00e1rozott",
"dollar sign": "doll\u00e1r jel",
"currency sign": "valuta jel",
"euro-currency sign": "euro-valuta jel",
"colon sign": "kett\u0151spont",
"cruzeiro sign": "cruzeiro jel",
"french franc sign": "francia frank jel",
"lira sign": "l\u00edra jel",
"mill sign": "mill jel",
"naira sign": "naira jel",
"peseta sign": "peseta jel",
"rupee sign": "r\u00fapia jel",
"won sign": "won jel",
"new sheqel sign": "\u00faj shekel jel",
"dong sign": "dong jel",
"kip sign": "kip jel",
"tugrik sign": "tugrik jel",
"drachma sign": "drachma jel",
"german penny symbol": "n\u00e9met penny jel",
"peso sign": "peso jel",
"guarani sign": "guarani jel",
"austral sign": "austral jel",
"hryvnia sign": "hrivnya jel",
"cedi sign": "cedi jel",
"livre tournois sign": "livre tournois jel",
"spesmilo sign": "spesmilo jel",
"tenge sign": "tenge jel",
"indian rupee sign": "r\u00fapel jel",
"turkish lira sign": "t\u00f6r\u00f6k l\u00edra jel",
"nordic mark sign": "\u00e9szaki m\u00e1rka jel",
"manat sign": "manat jel",
"ruble sign": "rubel jel",
"yen character": "jen karakter",
"yuan character": "j\u00fcan karakter",
"yuan character, in hong kong and taiwan": "hongkongi \u00e9s tajvani j\u00fcan karakter",
"yen\/yuan character variant one": "jen\/j\u00fcan karaktervari\u00e1ns",
"Loading emoticons...": "Hangulatjelek bet\u00f6lt\u00e9se...",
"Could not load emoticons": "Nem siker\u00fclt a hangulatjelek bet\u00f6lt\u00e9se",
"People": "Emberek",
"Animals and Nature": "\u00c1llatok \u00e9s term\u00e9szet",
"Food and Drink": "\u00c9tel, ital",
"Activity": "Tev\u00e9kenys\u00e9gek",
"Travel and Places": "Utaz\u00e1s \u00e9s helyek",
"Objects": "T\u00e1rgyak",
"Flags": "Z\u00e1szl\u00f3k",
"Characters": "Karakterek",
"Characters (no spaces)": "Karakterek (sz\u00f3k\u00f6z\u00f6k n\u00e9lk\u00fcl)",
"{0} characters": "{0} karakter",
"Error: Form submit field collision.": "Hiba: \u00dctk\u00f6z\u00e9s t\u00f6rt\u00e9nt az \u0171rlap elk\u00fcld\u00e9sekor.",
"Error: No form element found.": "Hiba: Nem tal\u00e1lhat\u00f3 \u0171rlap elem.",
"Update": "Friss\u00edt\u00e9s",
"Color swatch": "Sz\u00ednpaletta",
"Turquoise": "T\u00fcrkiz",
"Green": "Z\u00f6ld",
"Blue": "K\u00e9k",
"Purple": "Lila",
"Navy Blue": "Tengerk\u00e9k",
"Dark Turquoise": "S\u00f6t\u00e9tt\u00fcrkiz",
"Dark Green": "S\u00f6t\u00e9tz\u00f6ld",
"Medium Blue": "Kir\u00e1lyk\u00e9k",
"Medium Purple": "K\u00f6z\u00e9plila",
"Midnight Blue": "\u00c9jf\u00e9lk\u00e9k",
"Yellow": "S\u00e1rga",
"Orange": "Narancss\u00e1rga",
"Red": "Piros",
"Light Gray": "Vil\u00e1gossz\u00fcrke",
"Gray": "Sz\u00fcrke",
"Dark Yellow": "S\u00f6t\u00e9ts\u00e1rga",
"Dark Orange": "S\u00f6t\u00e9t narancss\u00e1rga",
"Dark Red": "S\u00f6t\u00e9tv\u00f6r\u00f6s",
"Medium Gray": "K\u00f6z\u00e9psz\u00fcrke",
"Dark Gray": "S\u00f6t\u00e9tsz\u00fcrke",
"Light Green": "Vil\u00e1gosz\u00f6ld",
"Light Yellow": "Vil\u00e1goss\u00e1rga",
"Light Red": "Vil\u00e1gospiros",
"Light Purple": "Vil\u00e1goslila",
"Light Blue": "Vil\u00e1gosk\u00e9k",
"Dark Purple": "S\u00f6t\u00e9tlila",
"Dark Blue": "S\u00f6t\u00e9tk\u00e9k",
"Black": "Fekete",
"White": "Feh\u00e9r",
"Switch to or from fullscreen mode": "Teljes vagy norm\u00e1l k\u00e9perny\u0151s m\u00f3dra v\u00e1lt\u00e1s",
"Open help dialog": "S\u00fag\u00f3ablak megnyit\u00e1sa",
"history": "el\u0151zm\u00e9nyek",
"styles": "st\u00edlusok",
"formatting": "form\u00e1z\u00e1s",
"alignment": "igaz\u00edt\u00e1s",
"indentation": "beh\u00faz\u00e1s",
"permanent pen": "sz\u00f6vegkiemel\u0151",
"comments": "megjegyz\u00e9sek",
"Format Painter": "Form\u00e1tumm\u00e1sol\u00f3",
"Insert\/edit iframe": "iframe besz\u00far\u00e1sa\/szerkeszt\u00e9se",
"Capitalization": "Nagybet\u0171s \u00edr\u00e1s",
"lowercase": "kisbet\u0171s",
"UPPERCASE": "NAGYBET\u0170S",
"Title Case": "C\u00edm szerinti \u00edr\u00e1sm\u00f3d",
"Permanent Pen Properties": "Tart\u00f3s toll tulajdons\u00e1gai",
"Permanent pen properties...": "Tart\u00f3s toll tulajdons\u00e1gai...",
"Font": "Bet\u0171t\u00edpus",
"Size": "M\u00e9ret",
"More...": "Tov\u00e1bbiak...",
"Spellcheck Language": "Helyes\u00edr\u00e1s-ellen\u0151rz\u00e9s nyelve",
"Select...": "V\u00e1lasszon...",
"Preferences": "Preferenci\u00e1k",
"Yes": "Igen",
"No": "Nem",
"Keyboard Navigation": "Billenty\u0171zettel val\u00f3 navig\u00e1l\u00e1s",
"Version": "Verzi\u00f3",
"Anchor": "Horgony",
"Special character": "Speci\u00e1lis karakter",
"Code sample": "K\u00f3d p\u00e9lda",
"Color": "Sz\u00edn",
"Emoticons": "Vigyorok",
"Document properties": "Dokumentum tulajdons\u00e1gai",
"Image": "K\u00e9p",
"Insert link": "Hivatkoz\u00e1s beilleszt\u00e9se",
"Target": "C\u00e9l",
"Link": "Hivatkoz\u00e1s",
"Poster": "El\u0151n\u00e9zeti k\u00e9p",
"Media": "M\u00e9dia",
"Print": "Nyomtat\u00e1s",
"Prev": "El\u0151z\u0151",
"Find and replace": "Keres\u00e9s \u00e9s csere",
"Whole words": "Csak ha ez a teljes sz\u00f3",
"Spellcheck": "Helyes\u00edr\u00e1s ellen\u0151rz\u00e9s",
"Caption": "Felirat",
"Insert template": "Sablon beilleszt\u00e9se"
});