import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface IUserInfo {
  id?: number
  name?: string
  phone?: string
  avatar?: string | null
  token?: string
}

const initState: IUserInfo = {
  id: undefined,
  name: '',
  phone: '',
  avatar: null,
  token: undefined
}

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })

    const setUserInfo = (val: Partial<IUserInfo>) => {
      userInfo.value = { ...userInfo.value, ...val }
    }

    const setToken = (token: string) => {
      userInfo.value.token = token
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }

    const reset = () => {
      userInfo.value = { ...initState }
    }

    const isLoggedIn = computed(() => !!userInfo.value.token)

    return {
      userInfo,
      setUserInfo,
      setToken,
      clearUserInfo,
      isLoggedIn,
      reset,
    }
  },
  {
    persist: true,
  },
)
