<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '缴费详情'
  }
}
</route>

<template>
  <view class="detail-page">
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>
    
    <view v-else-if="payment" class="payment-detail">
      <!-- 状态卡片 -->
      <view class="status-card">
        <view class="status-icon" :class="payment.status">
          <text v-if="payment.status === 'paid'" class="icon">✓</text>
          <text v-else-if="payment.status === 'pending'" class="icon">⏰</text>
          <text v-else-if="payment.status === 'failed'" class="icon">✗</text>
          <text v-else class="icon">?</text>
        </view>
        <text class="status-text">{{ getStatusText(payment.status) }}</text>
        <text v-if="payment.paid_at" class="status-time">
          {{ formatDateTime(payment.paid_at) }}
        </text>
      </view>

      <!-- 金额信息 -->
      <view class="amount-card">
        <text class="amount-label">缴费金额</text>
        <text class="amount-value">¥{{ payment.amount }}</text>
      </view>

      <!-- 详细信息 -->
      <view class="info-card">
        <view class="info-item">
          <text class="info-label">缴费单号</text>
          <text class="info-value">{{ payment.payment_no }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">水表号</text>
          <text class="info-value">{{ payment.water_meter_no }}</text>
        </view>
        <view v-if="payment.address" class="info-item">
          <text class="info-label">用水地址</text>
          <text class="info-value">{{ payment.address }}</text>
        </view>
        <view v-if="payment.user_name" class="info-item">
          <text class="info-label">用户姓名</text>
          <text class="info-value">{{ payment.user_name }}</text>
        </view>
        <view v-if="payment.phone" class="info-item">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{ payment.phone }}</text>
        </view>
        <view v-if="payment.water_usage" class="info-item">
          <text class="info-label">用水量</text>
          <text class="info-value">{{ payment.water_usage }} 立方米</text>
        </view>
        <view v-if="payment.unit_price" class="info-item">
          <text class="info-label">单价</text>
          <text class="info-value">¥{{ payment.unit_price }}/立方米</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{ getPaymentMethodText(payment.payment_method) }}</text>
        </view>
        <view v-if="payment.transaction_no" class="info-item">
          <text class="info-label">交易流水号</text>
          <text class="info-value">{{ payment.transaction_no }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ formatDateTime(payment.created_at) }}</text>
        </view>
        <view v-if="payment.paid_at" class="info-item">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{ formatDateTime(payment.paid_at) }}</text>
        </view>
        <view v-if="payment.remark" class="info-item">
          <text class="info-label">备注</text>
          <text class="info-value">{{ payment.remark }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view v-if="payment.status === 'pending'" class="action-section">
        <button class="pay-btn" @tap="handlePay">立即支付</button>
      </view>
    </view>

    <view v-else class="error">
      <text>缴费记录不存在</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPaymentDetail, payOrder, wechatPay } from '@/api/payment'

const loading = ref(true)
const payment = ref(null)
const paymentId = ref('')

onLoad((options) => {
  if (options.id) {
    paymentId.value = options.id
  }
})

const fetchPaymentDetail = async () => {
  if (!paymentId.value) return

  try {
    loading.value = true
    const res = await getPaymentDetail(parseInt(paymentId.value))
    
    if (res.code === 0) {
      payment.value = res.data
    } else {
      uni.showToast({
        title: res.message || '获取详情失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('获取缴费详情失败:', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const handlePay = async () => {
  if (!payment.value) return

  try {
    uni.showLoading({ title: '准备支付...' })
    
    const payRes = await payOrder(payment.value.id)
    
    if (payRes.code !== 0) {
      uni.hideLoading()
      uni.showToast({
        title: payRes.message || '支付准备失败',
        icon: 'error'
      })
      return
    }

    uni.hideLoading()

    // 调起微信支付
    await wechatPay(payRes.data.payment_params)

    // 支付成功，刷新页面
    uni.showToast({
      title: '支付成功',
      icon: 'success'
    })
    
    setTimeout(() => {
      fetchPaymentDetail()
    }, 1500)

  } catch (error) {
    uni.hideLoading()
    console.error('支付失败:', error)
    
    if (error.errMsg && error.errMsg.includes('cancel')) {
      uni.showToast({
        title: '支付已取消',
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: '支付失败，请重试',
        icon: 'error'
      })
    }
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const getPaymentMethodText = (method: string) => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    bank_transfer: '云闪付',
    cash: '现金',
    other: '其他'
  }
  return methodMap[method] || '未知'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  fetchPaymentDetail()
})
</script>

<style scoped>
.detail-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
}

.loading,
.error {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  font-size: 60rpx;
  color: white;
}

.status-icon.paid {
  background: #34c759;
}

.status-icon.pending {
  background: #ff9500;
}

.status-icon.failed {
  background: #ff3b30;
}

.status-icon.refunded {
  background: #007aff;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.status-time {
  font-size: 28rpx;
  color: #666;
}

.amount-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 200rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.action-section {
  padding: 32rpx;
}

.pay-btn {
  width: 100%;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}
</style>
