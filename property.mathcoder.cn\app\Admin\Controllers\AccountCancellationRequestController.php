<?php

namespace App\Admin\Controllers;

use App\Models\AccountCancellationRequest;
use App\Models\ActiveAccount;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AccountCancellationRequestController extends AdminController
{
    
    /**
     * 设置标题
     */
    protected $title = '销户申请管理';
    
    protected function grid(): Grid
    {
        return Grid::make(new AccountCancellationRequest(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user.username', '申请用户');
            $grid->column('water_meter_number', '水表号');
            $grid->column('status', '状态')
                 ->using([
                     AccountCancellationRequest::STATUS_PENDING   => '待审核',
                     AccountCancellationRequest::STATUS_PROCESSED => '已处理',
                     AccountCancellationRequest::STATUS_REJECTED  => '已拒绝',
                 ])
                 ->badge([
                     AccountCancellationRequest::STATUS_PENDING   => 'default',
                     AccountCancellationRequest::STATUS_PROCESSED => 'success',
                     AccountCancellationRequest::STATUS_REJECTED  => 'danger',
                 ]);
            $grid->column('rejection_reason', '拒绝原因')->limit(30);
            $grid->column('created_at', '申请时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '申请用户')->select(User::pluck('name', 'id'));
                $filter->equal('status', '状态')->select([
                    AccountCancellationRequest::STATUS_PENDING   => '待审核',
                    AccountCancellationRequest::STATUS_PROCESSED => '已处理',
                    AccountCancellationRequest::STATUS_REJECTED  => '已拒绝',
                ]);
                $filter->like('water_meter_number', '水表号');
            });

            $grid->disableCreateButton();
        });
    }

    protected function form(): Form
    {
        return Form::make(new AccountCancellationRequest(), function (Form $form) {
            $form->display('id', 'ID');
            $form->display('user.name', '申请用户');
            $form->text('water_meter_number', '水表号')->required();
            
            $form->radio('status', '审核状态')
                 ->options([
                     AccountCancellationRequest::STATUS_PENDING   => '待审核',
                     AccountCancellationRequest::STATUS_PROCESSED => '已处理',
                     AccountCancellationRequest::STATUS_REJECTED  => '已拒绝',
                 ])
                 ->default(AccountCancellationRequest::STATUS_PENDING)
                 ->required();

            $form->textarea('rejection_reason', '拒绝原因')
                 ->when('status', AccountCancellationRequest::STATUS_REJECTED)
                 ->placeholder('请填写拒绝原因');

            $form->display('created_at', '申请时间');
            $form->display('updated_at', '更新时间');

            $form->saved(function (Form $form) {
                $model = $form->model();
                if ($model->status === AccountCancellationRequest::STATUS_PROCESSED) {
                    $model->approve();
                }
                if ($model->status === AccountCancellationRequest::STATUS_REJECTED) {
                    $model->reject($model->rejection_reason);
                }
            });
        });
    }
}
