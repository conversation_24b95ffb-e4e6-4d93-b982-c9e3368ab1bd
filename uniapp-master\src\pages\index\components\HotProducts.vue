<template>
  <view class="mt-2.5 p-4 bg-white rounded-xl">
    <view class="flex justify-between items-center mb-4">
      <view class="flex flex-col">
        <text class="text-base font-bold text-gray-800">{{ title }}</text>
        <text class="text-xs text-gray-500 mt-0.5">{{ subtitle }}</text>
      </view>
      <wd-button size="small" type="primary" plain round @click="onViewMore">查看更多</wd-button>
    </view>

    <scroll-view scroll-x class="w-full whitespace-nowrap" show-scrollbar="false">
      <view class="inline-flex pb-2">
        <view
          v-for="(product, index) in products"
          :key="index"
          class="w-[140px] mr-3 rounded-lg overflow-hidden bg-white shadow-sm flex-shrink-0"
          @click="handleProductClick(product)"
        >
          <image :src="product.imageUrl" mode="aspectFill" class="w-[140px] h-[140px] rounded-t-lg" />
          <view class="p-2.5">
            <text class="text-sm text-gray-800 overflow-hidden text-ellipsis whitespace-nowrap mb-1">{{ product.title }}</text>
            <view class="flex items-center mb-1">
              <text class="text-[15px] font-bold text-orange-500">¥{{ product.price.toFixed(2) }}</text>
            </view>
            <view class="flex items-center">
              <text class="text-[11px] text-gray-500">已售{{ product.salesCount }}件</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
interface Product {
  id: number
  title: string
  imageUrl: string
  price: number
  salesCount: number
}

const props = defineProps<{
  title: string
  subtitle: string
  products: Product[]
}>()

const emit = defineEmits(['click', 'viewMore'])

const handleProductClick = (product: Product) => {
  emit('click', product)
}

const onViewMore = () => {
  emit('viewMore')
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'HotProducts',
})
</script>


