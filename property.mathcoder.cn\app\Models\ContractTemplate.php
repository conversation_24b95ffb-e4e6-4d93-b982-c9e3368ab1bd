<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class ContractTemplate extends Model
{
    use HasDateTimeFormatter;
    // 对应表
    protected $table = 'contract_templates';

    // 批量赋值
    protected $fillable = [
        'name',
        'file_path',
        'file_type',
        'mime_type',
        'description',
        'status',
    ];

    // 自动维护时间戳
    public $timestamps = true;

    // 文件类型映射
    public static array $fileTypes = [
        'pdf'   => 'PDF 文档',
        'html'  => 'HTML 页面',
        'word'  => 'Word 文档',
        'other' => '其他',
    ];

    // 启用状态映射
    public static array $statuses = [
        0 => '禁用',
        1 => '启用',
    ];
}
