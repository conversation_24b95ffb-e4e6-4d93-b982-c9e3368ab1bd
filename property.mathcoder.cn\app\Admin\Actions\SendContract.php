<?php
namespace App\Admin\Actions;

use App\Models\ContractSign;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Actions\Response;
use Illuminate\Http\Request;

class SendContract extends RowAction
{
    /** 这个必须和前端 data-action 对应，随意取但要唯一 */
    protected $action = 'sendContract';

    /** 按钮文字 */
    protected $title = '发送给用户';

    /**
     * 只有 pending 状态时才渲染按钮
     */
    public function display(): bool
    {
        return $this->row->status === 'pending';
    }

    /**
     * 弹出的确认框文本
     */
    public function confirm()
    {
        return ['确定要发送给用户吗？'];
    }

    /**
     * 点击“确定”后会走到这里
     */
    public function handle(Request $request): Response
    {
        $sign = ContractSign::findOrFail($this->getKey());

        if ($sign->status !== 'pending') {
            return $this
                ->response()
                ->error('只有“待签署”状态才能发送');
        }

        $sign->status = 'sent';
        $sign->save();

        return $this
            ->response()
            ->success('已发送')
            ->refresh(); // 提交成功后刷新当前表格行
    }
}
