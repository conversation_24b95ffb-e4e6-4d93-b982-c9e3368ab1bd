# 水费缴费系统 - 合同检查API测试文档

## 新增API接口

### 1. 检查水表号合同状态

**接口地址：** `GET /api/water-payments/check-contract/{water_meter_no}`

**请求方式：** GET

**认证：** 需要Bearer Token

**路径参数：**
- `water_meter_no` (string): 水表号

**响应格式：**

#### 成功响应（有合同）
```json
{
  "code": 0,
  "message": "合同检查通过",
  "data": {
    "has_contract": true,
    "water_meter_no": "1111111",
    "account_info": {
      "name": "张三",
      "phone": "***********",
      "address": "某小区 1号楼"
    }
  }
}
```

#### 失败响应（无合同）
```json
{
  "code": 403,
  "message": "该水表号未签署合同，无法进行充值缴费操作",
  "data": {
    "has_contract": false,
    "water_meter_no": "1111111"
  }
}
```

#### 错误响应（水表号不存在）
```json
{
  "code": 404,
  "message": "水表号不存在或不属于您",
  "data": {
    "has_contract": false
  }
}
```

## 前端流程改动

### 新的支付流程

1. **用户输入金额，点击"立即缴费"**
2. **调用合同检查接口** `checkContract(waterMeterNo)`
3. **根据检查结果：**
   - 无合同：显示错误提示，阻止操作
   - 有合同：显示支付方式选择器
4. **用户选择支付方式：**
   - 微信支付：继续现有微信支付流程
   - 云闪付：显示"功能开发中"提示
5. **完成支付流程**

### 前端API调用示例

```typescript
// 检查合同状态
const contractRes = await checkContract(waterMeterNo)

if (contractRes.code !== 0) {
  // 显示错误提示
  uni.showModal({
    title: '合同检查失败',
    content: contractRes.message,
    showCancel: false
  })
  return
}

// 显示支付方式选择
const paymentMethod = await showPaymentMethodSelector()

if (paymentMethod === 'wechat') {
  // 继续微信支付流程
} else if (paymentMethod === 'unionpay') {
  // 云闪付（暂时搁置）
}
```

## 后端改动说明

### 1. 新增方法
- `checkContract()`: 独立的合同检查接口

### 2. 移除的逻辑
- 从 `store()` 方法中移除合同检查
- 从 `pay()` 方法中移除合同检查

### 3. 保留的私有方法
- `checkContractStatus()`: 检查合同状态
- `validateContract()`: 验证合同（仅在新接口中使用）

## 测试建议

1. **测试有合同的水表号**
2. **测试无合同的水表号**
3. **测试不存在的水表号**
4. **测试前端支付流程**
5. **验证微信支付仍然正常工作**
