<template>
  <view class="mt-2.5 p-4 bg-white rounded-xl">
    <view class="flex justify-between items-center mb-4">
      <text class="text-base font-bold text-gray-800">{{ title }}</text>
      <view class="flex items-center" v-if="showCountdown">
        <text class="text-xs text-gray-600 mr-1">距结束</text>
        <wd-count-down :time="countdown" format="HH:mm:ss" />
      </view>
    </view>

    <view class="flex flex-col gap-3">
      <view
        v-for="(promotion, index) in promotions"
        :key="index"
        class="flex bg-white rounded-lg overflow-hidden shadow-sm"
        @click="handlePromotionClick(promotion)"
      >
        <image :src="promotion.imageUrl" mode="aspectFill" class="w-[120px] h-[120px] flex-shrink-0" />
        <view class="flex-1 p-2.5 flex flex-col">
          <text class="text-sm text-gray-800 overflow-hidden text-ellipsis line-clamp-2 leading-snug mb-2">{{ promotion.title }}</text>
          <view class="flex items-center mb-2">
            <text class="text-lg font-bold text-orange-500 mr-1.5">¥{{ promotion.price.toFixed(2) }}</text>
            <text class="text-xs text-gray-400 line-through">¥{{ promotion.originalPrice.toFixed(2) }}</text>
          </view>
          <wd-progress
            :percentage="promotion.soldPercentage"
            stroke-width="4px"
            color="#ff5000"
            class="mt-2"
          />
          <view class="flex justify-between items-center mt-2">
            <text class="text-xs text-gray-600">已售{{ promotion.soldPercentage }}%</text>
            <wd-button size="small" type="danger" class="text-xs px-3">立即抢购</wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
interface Promotion {
  id: number
  title: string
  imageUrl: string
  price: number
  originalPrice: number
  soldPercentage: number
}

const props = defineProps<{
  title: string
  promotions: Promotion[]
  showCountdown?: boolean
  countdown?: number
}>()

const emit = defineEmits(['click'])

const handlePromotionClick = (promotion: Promotion) => {
  emit('click', promotion)
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'PromotionSection',
})
</script>


