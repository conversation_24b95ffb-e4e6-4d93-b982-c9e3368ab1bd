import { ref } from 'vue'
export const useAgree = () => {
  const isAgreed = ref(false) // 是否同意
  const isShowAgreement = ref(false) // 是否显示协议
  const agreementType = ref('agreement')
  const handleConfirmAgreement = () => {
    isAgreed.value = true
    isShowAgreement.value = false
  }

  const showAgreement = (type: string) => {
    isShowAgreement.value = true
    agreementType.value = type
  }

  const agree = ({ value }) => {
    isAgreed.value = value
  }
  const handleCloseShowAgreement = () => {
    isShowAgreement.value = false
  }

  const isNotAgreed = () => {
    if (!isAgreed.value) {
      uni.showToast({
        title: '请先勾选协议',
        icon: 'none',
      })
      return false
    }
    return true
  }

  return {
    isAgreed,
    isShowAgreement,
    agreementType,
    handleConfirmAgreement,
    showAgreement,
    agree,
    handleCloseShowAgreement,
    isNotAgreed,
  }
}
