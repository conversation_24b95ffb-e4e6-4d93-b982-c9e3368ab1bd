<?php

namespace App\Admin\Controllers;

use App\Models\AccountOpenRequest;
use App\Models\ActiveAccount;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AccountOpenRequestController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = '开户申请管理';
    
    protected function grid(): Grid
    {
        return Grid::make(new AccountOpenRequest(), function (Grid $grid) {
            $grid->model()->with(['user']);
            $grid->column('id')->sortable();
            $grid->column('user.name', '申请用户');
            $grid->column('community', '小区');
            $grid->column('building_number', '楼号');
            $grid->column('name', '姓名');
            $grid->column('phone', '电话');
            $grid->column('status', '状态')
                 ->using([
                     AccountOpenRequest::STATUS_PENDING  => '待审核',
                     AccountOpenRequest::STATUS_APPROVED => '已通过',
                     AccountOpenRequest::STATUS_REJECTED => '已拒绝',
                 ])
                 ->badge([
                     AccountOpenRequest::STATUS_PENDING  => 'default',
                     AccountOpenRequest::STATUS_APPROVED => 'success',
                     AccountOpenRequest::STATUS_REJECTED => 'danger',
                 ]);
            $grid->column('rejection_reason', '拒绝原因')->limit(30)->display(function($rejection_reason){
                if(empty($rejection_reason)||is_null($rejection_reason)){
                    return '暂无原因';
                }else{
                    return $rejection_reason;
                }
            });
            $grid->column('created_at', '申请时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '申请用户')->select(User::pluck('name', 'id'));
                $filter->equal('status', '状态')->select([
                    AccountOpenRequest::STATUS_PENDING  => '待审核',
                    AccountOpenRequest::STATUS_APPROVED => '已通过',
                    AccountOpenRequest::STATUS_REJECTED => '已拒绝',
                ]);
                $filter->like('community', '小区');
                $filter->like('name', '姓名');
                $filter->equal('phone', '电话');
            });

            // 只能通过接口创建，禁用手动创建
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
        });
    }

    protected function form(): Form
    {
        return Form::make(new AccountOpenRequest(), function (Form $form) {
            $form->display('id', 'ID');
            $form->display('user.username', '申请用户');
            $form->text('community', '小区')->required();
            $form->text('building_number', '楼号')->required();
            $form->text('name', '姓名')->required();
            $form->text('phone', '电话')->required();

            $form->radio('status', '审核状态')
                 ->options([
                     AccountOpenRequest::STATUS_PENDING  => '待审核',
                     AccountOpenRequest::STATUS_APPROVED => '已通过',
                     AccountOpenRequest::STATUS_REJECTED => '已拒绝',
                 ])
                 ->default(AccountOpenRequest::STATUS_PENDING)
                 ->required();

            // 审核通过时填写水表号
            $form->text('water_meter_number_for_approval', '水表号')
                 ->required()
                 ->when('status', AccountOpenRequest::STATUS_APPROVED)
                 ->help('审核通过后会同步至已开户表');

            $form->textarea('rejection_reason', '拒绝原因')
                 ->when('status', AccountOpenRequest::STATUS_REJECTED)
                 ->placeholder('请填写拒绝原因');

            $form->display('created_at', '申请时间');
            $form->display('updated_at', '更新时间');

            $form->saved(function (Form $form) {
                $request = $form->model();
                if ($request->status === AccountOpenRequest::STATUS_APPROVED) {
                    $request->approve($form->water_meter_number_for_approval);
                }
                if ($request->status === AccountOpenRequest::STATUS_REJECTED) {
                    $request->reject($request->rejection_reason);
                }
            });
        });
    }
}
