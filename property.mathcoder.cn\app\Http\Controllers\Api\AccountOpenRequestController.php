<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AccountOpenRequest;

class AccountOpenRequestController extends Controller
{
    /**
     * 列表：返回当前用户的所有申请
     */
    public function index(Request $request)
    {
        $list = AccountOpenRequest::withoutGlobalScope('pending')
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($list);
    }

    /**
     * 创建申请
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'community'       => 'required|string',
            'building_number' => 'required|string',
            'name'            => 'required|string',
            'phone'           => 'required|string',
        ]);

        $apply = AccountOpenRequest::create([
            'user_id'         => $request->user()->id,
            'community'       => $data['community'],
            'building_number' => $data['building_number'],
            'name'            => $data['name'],
            'phone'           => $data['phone'],
            'status'          => AccountOpenRequest::STATUS_PENDING,
        ]);

        return response()->json($apply, 201);
    }

    /**
     * 查看单条申请
     */
    public function show(Request $request, $id)
    {
        $apply = AccountOpenRequest::withoutGlobalScope('pending')
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        return response()->json($apply);
    }
}
