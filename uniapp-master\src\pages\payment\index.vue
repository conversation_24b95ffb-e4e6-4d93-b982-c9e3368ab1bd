<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '水费缴纳'
  }
}
</route>

<template>
  <view class="payment-page">
    <!-- 账户选择 -->
    <view class="account-section">
      <view class="section-header" @tap="toggleAccountList">
        <text class="section-title">选择缴费账户</text>
        <text
          v-if="accounts.length > 1"
          class="toggle-icon"
          :class="{ expanded: showAccountList }"
        >▼</text>
      </view>

      <view v-if="accounts.length === 0" class="no-account">
        <text class="no-account-text">暂无绑定账户</text>
        <button class="bind-account-btn" @tap="navigateTo('/pages/account/open/index')">
          去开户
        </button>
      </view>

      <!-- 当前选中的账户 -->
      <view v-else-if="selectedAccount && !showAccountList" class="selected-account">
        <view class="account-item active">
          <view class="account-info">
            <text class="account-name">{{ selectedAccount.community }} {{ selectedAccount.building_number }}</text>
            <text class="account-meter">水表号：{{ selectedAccount.water_meter_number }}</text>
          </view>
          <view class="account-check">
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>

      <!-- 账户列表 -->
      <view v-else-if="showAccountList" class="account-list">
        <view
          v-for="(account, index) in accounts"
          :key="account.id"
          class="account-item"
          :class="{
            active: selectedAccountIndex === index,
            disabled: !account.water_meter_number
          }"
          @tap="selectAccount(index)"
        >
          <view class="account-info">
            <text class="account-name">{{ account.community }} {{ account.building_number }}</text>
            <text
              class="account-meter"
              :class="{ 'no-meter': !account.water_meter_number }"
            >
              水表号：{{ account.water_meter_number || '暂无水表号' }}
            </text>
          </view>
          <view class="account-check">
            <text v-if="selectedAccountIndex === index" class="check-icon">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 缴费金额 -->
    <view v-if="selectedAccount" class="amount-section">
      <view class="section-title">缴费金额</view>
      <view class="amount-input-wrapper">
        <text class="currency-symbol">¥</text>
        <input 
          v-model="amount"
          class="amount-input"
          type="digit"
          placeholder="请输入缴费金额"
          @input="onAmountInput"
        />
      </view>
      <view class="quick-amounts">
        <view 
          v-for="quickAmount in quickAmounts"
          :key="quickAmount"
          class="quick-amount-btn"
          @tap="selectQuickAmount(quickAmount)"
        >
          {{ quickAmount }}元
        </view>
      </view>
    </view>



    <!-- 缴费按钮 -->
    <view v-if="selectedAccount" class="pay-section">
      <button 
        class="pay-btn"
        :class="{ disabled: !canPay }"
        :disabled="!canPay"
        @tap="handlePay"
      >
        立即缴费
      </button>
    </view>

    <!-- 缴费记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">最近缴费记录</text>
        <text class="view-all" @tap="navigateTo('/pages/payment/history')">查看全部</text>
      </view>
      <view v-if="recentPayments.length === 0" class="no-records">
        <text class="no-records-text">暂无缴费记录</text>
      </view>
      <view v-else class="payment-list">
        <view 
          v-for="payment in recentPayments" 
          :key="payment.id"
          class="payment-item"
          @tap="navigateTo(`/pages/payment/detail?id=${payment.id}`)"
        >
          <view class="payment-info">
            <text class="payment-meter">{{ payment.water_meter_no }}</text>
            <text class="payment-time">{{ formatTime(payment.created_at) }}</text>
          </view>
          <view class="payment-right">
            <text class="payment-amount">¥{{ payment.amount }}</text>
            <text class="payment-status" :class="payment.status">
              {{ getStatusText(payment.status) }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getBoundAccounts } from '@/api/account'
import { getPaymentList, createPayment, payOrder, wechatPay, checkContract } from '@/api/payment'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 响应式数据
const accounts = ref([])
const selectedAccountIndex = ref(-1)
const showAccountList = ref(false)
const amount = ref('')
const recentPayments = ref([])
const quickAmounts = [50, 100, 200, 500]

// 计算属性
const selectedAccount = computed(() => {
  return selectedAccountIndex.value >= 0 ? accounts.value[selectedAccountIndex.value] : null
})

const canPay = computed(() => {
  return selectedAccount.value && amount.value && parseFloat(amount.value) > 0
})

// 页面参数
let preSelectedWaterMeterNo = ''

onLoad((options) => {
  if (options.water_meter_no) {
    preSelectedWaterMeterNo = options.water_meter_no
  }
})

// 方法
const toggleAccountList = () => {
  if (accounts.value.length > 1) {
    showAccountList.value = !showAccountList.value
  }
}

const selectAccount = (index: number) => {
  const account = accounts.value[index]

  // 检查账户是否有水表号
  if (!account.water_meter_number) {
    uni.showToast({
      title: '该账户暂无水表号，无法选择',
      icon: 'none',
      duration: 2000
    })
    return
  }

  selectedAccountIndex.value = index
  showAccountList.value = false // 选择后收起列表
}

const selectQuickAmount = (quickAmount: number) => {
  amount.value = quickAmount.toString()
}

const onAmountInput = (e: any) => {
  let value = e.detail.value
  // 限制小数点后两位
  if (value.includes('.')) {
    const parts = value.split('.')
    if (parts[1] && parts[1].length > 2) {
      value = parts[0] + '.' + parts[1].substring(0, 2)
      amount.value = value
    }
  }
}

const handlePay = async () => {
  if (!canPay.value) return

  let hasShownError = false // 添加标志位防止重复显示错误

  const showError = (title, content) => {
    if (hasShownError) return // 如果已经显示过错误，直接返回
    hasShownError = true
    uni.hideLoading()
    uni.showModal({
      title,
      content,
      showCancel: false,
      confirmText: '我知道了'
    })
  }

  try {
    uni.showLoading({ title: '检查合同状态...' })

    // 1. 检查合同状态（前置检查）
    const contractRes = await checkContract(selectedAccount.value.water_meter_number)

    if (contractRes.code !== 0) {
      showError(
        '合同检查失败',
        contractRes.message || '无法进行缴费操作'
      )
      return
    }

    // 2. 创建缴费订单
    uni.showLoading({ title: '创建订单中...' })
    const createRes = await createPayment({
      water_meter_no: selectedAccount.value.water_meter_number,
      amount: parseFloat(amount.value)
    })

    if (createRes.code !== 0) {
      showError(
        createRes.code === 403 ? '提示' : '支付失败',
        createRes.message || '创建订单失败'
      )
      return
    }

    const paymentId = createRes.data.id

    // 3. 微信支付流程
    uni.showLoading({ title: '准备支付...' })
    const payRes = await payOrder(paymentId)

    if (payRes.code !== 0) {
      showError(
        payRes.code === 403 ? '提示' : '支付失败',
        payRes.message || '支付准备失败'
      )
      return
    }

    uni.hideLoading()

    // 调起微信支付
    await wechatPay(payRes.data.payment_params)

    // 支付成功
    uni.showToast({
      title: '支付成功',
      icon: 'success'
    })

    // 重置表单
    amount.value = ''

    // 刷新缴费记录
    fetchRecentPayments()

  } catch (error) {
    console.error('支付失败:', error)

    const errorMessage = error.message || error.errMsg || '支付失败，请重试'
    const isCancel = error.errMsg && error.errMsg.includes('cancel')

    showError(
      isCancel ? '提示' : '支付失败',
      isCancel ? '支付已取消' : errorMessage
    )
  }
}



const fetchAccounts = async () => {
  try {
    const res = await getBoundAccounts()
    if (res.code === 0 && res.data) {
      accounts.value = res.data

      // 如果有预选的水表号，找到对应的账户
      if (preSelectedWaterMeterNo && accounts.value.length > 0) {
        const preSelectedIndex = accounts.value.findIndex(
          account => account.water_meter_number === preSelectedWaterMeterNo
        )
        if (preSelectedIndex !== -1) {
          selectedAccountIndex.value = preSelectedIndex
        } else {
          // 如果预选的账户不存在，选择第一个有水表号的账户
          const firstValidIndex = accounts.value.findIndex(
            account => account.water_meter_number
          )
          selectedAccountIndex.value = firstValidIndex !== -1 ? firstValidIndex : -1
        }
      } else if (accounts.value.length > 0) {
        // 默认选择第一个有水表号的账户
        const firstValidIndex = accounts.value.findIndex(
          account => account.water_meter_number
        )
        selectedAccountIndex.value = firstValidIndex !== -1 ? firstValidIndex : -1
      }

      // 如果只有一个有效账户，不显示列表
      const validAccounts = accounts.value.filter(account => account.water_meter_number)
      if (validAccounts.length <= 1) {
        showAccountList.value = false
      } else {
        showAccountList.value = true
      }
    }
  } catch (error) {
    console.error('获取账户列表失败:', error)
  }
}

const fetchRecentPayments = async () => {
  try {
    const res = await getPaymentList({ page: 1 })
    if (res.code === 0 && res.data) {
      recentPayments.value = res.data.data.slice(0, 3) // 只显示最近3条
    }
  } catch (error) {
    console.error('获取缴费记录失败:', error)
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleDateString()
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

onMounted(() => {
  fetchAccounts()
  fetchRecentPayments()
})
</script>

<style scoped>
.payment-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
}

.account-section,
.amount-section,
.remark-section,
.history-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  cursor: pointer;
  user-select: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.no-account {
  text-align: center;
  padding: 60rpx 0;
}

.no-account-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 32rpx;
}

.bind-account-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.selected-account {
  margin-top: 8rpx;
}

.account-list {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.account-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.account-item.active {
  border-color: #007aff;
  background: #f0f8ff;
}

.account-item.disabled {
  opacity: 0.5;
  background: #f5f5f5;
  cursor: not-allowed;
}

.account-item.disabled:active {
  background: #f5f5f5;
}

.account-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.account-meter {
  font-size: 26rpx;
  color: #666;
}

.account-meter.no-meter {
  color: #ff4d4f;
}

.check-icon {
  color: #007aff;
  font-size: 32rpx;
  font-weight: bold;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.currency-symbol {
  font-size: 36rpx;
  color: #333;
  margin-right: 16rpx;
}

.amount-input {
  flex: 1;
  font-size: 36rpx;
  color: #333;
}

.quick-amounts {
  display: flex;
  gap: 16rpx;
}

.quick-amount-btn {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  text-align: center;
}



.pay-section {
  padding: 32rpx;
}

.pay-btn {
  width: 100%;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.pay-btn.disabled {
  background: #ccc;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  color: #007aff;
  font-size: 26rpx;
}

.no-records {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-meter {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.payment-time {
  font-size: 24rpx;
  color: #999;
}

.payment-amount {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  text-align: right;
  margin-bottom: 8rpx;
}

.payment-status {
  font-size: 24rpx;
  text-align: right;
}

.payment-status.pending {
  color: #ff9500;
}

.payment-status.paid {
  color: #34c759;
}

.payment-status.failed {
  color: #ff3b30;
}

.payment-status.refunded {
  color: #007aff;
}
</style>
