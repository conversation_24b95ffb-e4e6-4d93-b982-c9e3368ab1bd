import { http } from '@/utils/http'

// 获取缴费记录列表
export const getPaymentList = (params?: {
  page?: number
  status?: string
  account_id?: number
}) => {
  return http.get('/water-payments', params)
}

// 创建缴费订单
export const createPayment = (data: {
  water_meter_no: string
  amount: number
}) => {
  return http({
    url: '/water-payments',
    method: 'POST',
    data,
    hideErrorToast: true
  })
}

// 获取缴费详情
export const getPaymentDetail = (id: number) => {
  return http.get(`/water-payments/${id}`)
}

// 发起微信支付
export const payOrder = (id: number) => {
  return http({
    url: `/water-payments/${id}/pay`,
    method: 'POST',
    hideErrorToast: true
  })
}

// 查询支付状态
export const checkPaymentStatus = (id: number) => {
  return http.get(`/water-payments/${id}/status`)
}

// 检查水表号合同状态（支付前置检查）
export const checkContract = (waterMeterNo: string) => {
  return http({
    url: `/water-payments/check-contract/${waterMeterNo}`,
    method: 'GET',
    hideErrorToast: true
  })
}

// 微信支付
export const wechatPay = (paymentParams: any) => {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: 'wxpay',
      timeStamp: paymentParams.timeStamp, // 修复：使用正确的字段名
      nonceStr: paymentParams.nonceStr,
      package: paymentParams.package,
      signType: paymentParams.signType,
      paySign: paymentParams.paySign,
      success: (res) => {
        console.log('支付成功:', res)
        resolve(res)
      },
      fail: (err) => {
        console.error('支付失败:', err)
        reject(err)
      }
    })
  })
}
