<!doctype html>

<title>CodeMirror: DTD mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="dtd.js"></script>
<style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">DTD</a>
  </ul>
</div>

<article>
<h2>DTD mode</h2>
<form><textarea id="code" name="code"><?xml version="1.0" encoding="UTF-8"?>

<!ATTLIST title
  xmlns	CDATA	#FIXED	"http://docbook.org/ns/docbook"
  role	CDATA	#IMPLIED
  %db.common.attributes;
  %db.common.linking.attributes;
>

<!--
  Try: http://docbook.org/xml/5.0/dtd/docbook.dtd
-->

<!DOCTYPE xsl:stylesheet
  [
    <!ENTITY nbsp   "&amp;#160;">
    <!ENTITY copy   "&amp;#169;">
    <!ENTITY reg    "&amp;#174;">
    <!ENTITY trade  "&amp;#8482;">
    <!ENTITY mdash  "&amp;#8212;">
    <!ENTITY ldquo  "&amp;#8220;">
    <!ENTITY rdquo  "&amp;#8221;">
    <!ENTITY pound  "&amp;#163;">
    <!ENTITY yen    "&amp;#165;">
    <!ENTITY euro   "&amp;#8364;">
    <!ENTITY mathml "http://www.w3.org/1998/Math/MathML">
  ]
>

<!ELEMENT title (#PCDATA|inlinemediaobject|remark|superscript|subscript|xref|link|olink|anchor|biblioref|alt|annotation|indexterm|abbrev|acronym|date|emphasis|footnote|footnoteref|foreignphrase|phrase|quote|wordasword|firstterm|glossterm|coref|trademark|productnumber|productname|database|application|hardware|citation|citerefentry|citetitle|citebiblioid|author|person|personname|org|orgname|editor|jobtitle|replaceable|package|parameter|termdef|nonterminal|systemitem|option|optional|property|inlineequation|tag|markup|token|symbol|literal|code|constant|email|uri|guiicon|guibutton|guimenuitem|guimenu|guisubmenu|guilabel|menuchoice|mousebutton|keycombo|keycap|keycode|keysym|shortcut|accel|prompt|envar|filename|command|computeroutput|userinput|function|varname|returnvalue|type|classname|exceptionname|interfacename|methodname|modifier|initializer|ooclass|ooexception|oointerface|errorcode|errortext|errorname|errortype)*>

<!ENTITY % db.common.attributes "
  xml:id	ID	#IMPLIED
  version	CDATA	#IMPLIED
  xml:lang	CDATA	#IMPLIED
  xml:base	CDATA	#IMPLIED
  remap	CDATA	#IMPLIED
  xreflabel	CDATA	#IMPLIED
  revisionflag	(changed|added|deleted|off)	#IMPLIED
  dir	(ltr|rtl|lro|rlo)	#IMPLIED
  arch	CDATA	#IMPLIED
  audience	CDATA	#IMPLIED
  condition	CDATA	#IMPLIED
  conformance	CDATA	#IMPLIED
  os	CDATA	#IMPLIED
  revision	CDATA	#IMPLIED
  security	CDATA	#IMPLIED
  userlevel	CDATA	#IMPLIED
  vendor	CDATA	#IMPLIED
  wordsize	CDATA	#IMPLIED
  annotations	CDATA	#IMPLIED

"></textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: {name: "dtd", alignCDATA: true},
        lineNumbers: true,
        lineWrapping: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>application/xml-dtd</code>.</p>
  </article>
