<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CarouselController extends Controller
{
    /**
     * 获取轮播图列表
     */
    public function index()
    {
        $now = now();
        
        // 获取当前有效的轮播图
        $carousels = DB::table('ad_carousel')
            ->where('status', 'active')
            ->where(function ($query) use ($now) {
                $query->whereNull('start_at')
                    ->orWhere('start_at', '<=', $now);
            })
            ->where(function ($query) use ($now) {
                $query->whereNull('end_at')
                    ->orWhere('end_at', '>=', $now);
            })
            ->orderBy('sort_order', 'desc')
            ->select(['id', 'image_url', 'link_type', 'link_target', 'link_params'])
            ->get();

        // 处理图片URL和链接参数
        $carousels = $carousels->map(function ($carousel) {
            // 处理图片URL
            $carousel->image_url = $this->ensureAbsoluteUrl($carousel->image_url);
            
            // 处理链接参数
            if (!empty($carousel->link_params)) {
                try {
                    // 如果已经是数组则不需要解码
                    if (is_string($carousel->link_params)) {
                        $carousel->link_params = json_decode($carousel->link_params, true);
                    }
                } catch (\Exception $e) {
                    \Log::error('Banner link_params 解析失败: ' . $e->getMessage(), [
                        'banner_id' => $carousel->id,
                        'link_params' => $carousel->link_params
                    ]);
                    $carousel->link_params = null;
                }
            } else {
                $carousel->link_params = null;
            }

            // 确保link_type有效
            if (empty($carousel->link_type)) {
                $carousel->link_type = 'none';
            }

            // 确保link_target存在
            if (empty($carousel->link_target)) {
                $carousel->link_target = '';
            }

            return $carousel;
        });

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $carousels
        ]);
    }

    /**
     * 确保URL是完整的绝对URL
     */
    private function ensureAbsoluteUrl($url)
    {
        if (empty($url)) {
            return '';
        }

        // 如果已经是完整URL，直接返回
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }

        // 如果是相对路径，转换为完整URL
        if (strpos($url, '/') === 0) {
            // 移除开头的斜杠
            $url = ltrim($url, '/');
        }

        // 获取配置的域名
        $domain = config('app.url');
        
        // 确保domain末尾没有斜杠
        $domain = rtrim($domain, '/');

        // 返回完整URL
        return $domain . '/storage/' . $url;
    }
} 