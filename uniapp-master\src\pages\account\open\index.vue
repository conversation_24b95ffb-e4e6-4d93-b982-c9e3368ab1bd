<template>
  <view class="container">
    <view class="form-group">
      <view class="form-item">
        <text class="label">小区</text>
        <input 
          class="input" 
          type="text" 
          v-model="formData.community" 
          placeholder="请输入小区名称"
        />
      </view>
      
      <view class="form-item">
        <text class="label">楼号</text>
        <input 
          class="input" 
          type="text" 
          v-model="formData.building_number" 
          placeholder="请输入楼号"
        />
      </view>

      <view class="form-item">
        <text class="label">姓名</text>
        <input 
          class="input" 
          type="text" 
          v-model="formData.name" 
          placeholder="请输入姓名"
        />
      </view>

      <view class="form-item">
        <text class="label">手机号</text>
        <input 
          class="input" 
          type="number" 
          v-model="formData.phone" 
          placeholder="请输入手机号"
          maxlength="11"
        />
      </view>
    </view>

    <view class="tips">
      <view class="tip-item">
        <text class="dot">•</text>
        <text class="tip-text">请确保填写信息准确无误</text>
      </view>
      <view class="tip-item">
        <text class="dot">•</text>
        <text class="tip-text">审核通过后将为您分配水表号</text>
      </view>
      <view class="tip-item">
        <text class="dot">•</text>
        <text class="tip-text">如有疑问请联系客服</text>
      </view>
    </view>

    <button 
      class="submit-btn" 
      :disabled="!isFormValid || isSubmitting"
      @tap="handleSubmit"
    >
      {{ isSubmitting ? '提交中...' : '提交申请' }}
    </button>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { submitAccountOpenRequest } from '@/api/account'

const formData = ref({
  community: '',
  building_number: '',
  name: '',
  phone: ''
})

const isSubmitting = ref(false)

// 表单验证
const isFormValid = computed(() => {
  const { community, building_number, name, phone } = formData.value
  return (
    community.trim() !== '' &&
    building_number.trim() !== '' &&
    name.trim() !== '' &&
    /^1[3-9]\d{9}$/.test(phone)
  )
})

// 提交申请
const handleSubmit = async () => {
  if (!isFormValid.value || isSubmitting.value) return

  try {
    isSubmitting.value = true
    const res = await submitAccountOpenRequest(formData.value)
    
    if (res.code === 0) {
      uni.showToast({
        title: '申请提交成功',
        icon: 'success'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.message || '提交失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('提交开户申请失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  padding: 32rpx;
  background: #F5F7FA;
}

.form-group {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 0 32rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #F5F5F5;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 140rpx;
  font-size: 30rpx;
  color: #333;
}

.input {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.tips {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.dot {
  color: #1989FA;
  margin-right: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.submit-btn {
  position: fixed;
  left: 32rpx;
  right: 32rpx;
  bottom: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  background: #1989FA;
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 44rpx;
  text-align: center;
}

.submit-btn[disabled] {
  background: #CCCCCC;
  color: #FFFFFF;
}
</style> 