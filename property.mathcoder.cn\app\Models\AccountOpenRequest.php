<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class AccountOpenRequest extends Model
{
    use HasDateTimeFormatter;
    // 对应数据表
    protected $table = 'account_open_requests';

    // 可批量赋值字段
    protected $fillable = [
        'user_id',
        'community',
        'building_number',
        'name',
        'phone',
        'status',
        'remark',
        'rejection_reason',
    ];

    // 审核状态
    public const STATUS_PENDING  = 'pending';   // 待审核
    public const STATUS_APPROVED = 'approved';  // 已通过
    public const STATUS_REJECTED = 'rejected';  // 已拒绝

    /**
     * 默认只加载待审核记录
     */
    protected static function booted()
    {
        static::addGlobalScope('pending', function (Builder $builder) {
            $builder->where('status', self::STATUS_PENDING);
        });
    }

    /**
     * 关联申请用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 审核通过：更新状态并同步到已开户表
     *
     * @param  string  $waterMeterNumber
     * @return \App\Models\ActiveAccount
     */
    public function approve(string $waterMeterNumber): ActiveAccount
    {
        $this->update([
            'status'           => self::STATUS_APPROVED,
            'rejection_reason' => null,
        ]);

        return ActiveAccount::firstOrCreate(
            ['water_meter_number' => $waterMeterNumber],
            [
                'user_id'         => $this->user_id,
                'community'       => $this->community,
                'building_number' => $this->building_number,
                'name'            => $this->name,
                'phone'           => $this->phone,
                'opened_at'       => now(),
            ]
        );
    }

    /**
     * 审核拒绝：更新状态并记录原因
     *
     * @param  string  $reason
     * @return void
     */
    public function reject(string $reason): void
    {
        $this->update([
            'status'           => self::STATUS_REJECTED,
            'rejection_reason' => $reason,
        ]);
    }
}
