<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="container">
    <view class="content">
      <text class="title">水费缴纳</text>
      <text class="subtitle">便捷的水费管理平台</text>
      
      <view class="btn-wrapper">
        <button
          class="login-btn"
          open-type="getPhoneNumber"
          @getphonenumber="getPhoneNumber"
        >一键登录</button>
        </view>

      <view class="agreement">
            <checkbox-group @change="handleConfirmAgreement">
          <label>
            <checkbox :checked="isAgreed" @change="agree" style="transform: scale(0.7);" />
            <text class="agreement-text">阅读并同意</text>
            <text class="link" @click.stop="showAgreement('agreement')">《用户协议》</text>
            <text class="agreement-text">和</text>
            <text class="link" @click.stop="showAgreement('privacy')">《隐私政策》</text>
              </label>
            </checkbox-group>
      </view>
    </view>

    <wd-popup
      v-model="isShowAgreement"
      position="bottom"
      custom-style="height: 70vh;"
      @close="handleCloseShowAgreement"
    >
      <Agreement :type="agreementType" @confirm="handleConfirmAgreement" />
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { loginWithWechat } from '@/api/login'
import { useUserStore } from '@/store/user'
import Agreement from './components/agreement.vue'
import { toUserCenter } from '@/utils/toPath'
import { useAgree } from './useAgree'

const userStore = useUserStore()

if (userStore.isLoggedIn) {
  toUserCenter()
}

const {
  isAgreed,
  isShowAgreement,
  handleConfirmAgreement,
  showAgreement,
  handleCloseShowAgreement,
  agreementType,
  isNotAgreed,
  agree,
} = useAgree()

/**
 * 获取手机号并登录
 */
const getPhoneNumber = async (e: any) => {
  if (!isNotAgreed()) return
  
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    try {
  uni.showLoading({ title: '登录中...' })

      const loginRes = await uni.login({ provider: 'weixin' })
      
      // 将手机号数据作为 JSON 字符串传递
      const phoneCode = JSON.stringify({
        encryptedData: e.detail.encryptedData,
        iv: e.detail.iv
      })
      
      const loginResult = await uni.request({
        url: 'https://property.oneself.icu/api/wechat/login',
        method: 'POST',
        data: {
          code: loginRes.code,
          phoneCode: phoneCode
        },
        header: {
          'content-type': 'application/json'
        }
      })

      const response = loginResult.data
      if (response.code === 0 && response.data?.token) {
        // 先设置 token
        userStore.setToken(response.data.token)
        // 再设置用户信息
        userStore.setUserInfo({
          id: response.data.userInfo.id,
          name: response.data.userInfo.name,
          phone: response.data.userInfo.phone,
          avatar: response.data.userInfo.avatar
        })
        
          uni.showToast({
          title: '登录成功',
          icon: 'success',
        })
              setTimeout(() => {
                toUserCenter()
        }, 1500)
      } else {
        uni.showToast({
          title: response.message || '登录失败',
          icon: 'error',
          })
      }
    } catch (error) {
      console.error('登录失败:', error)
      uni.showToast({
        title: '登录失败',
        icon: 'error',
      })
    } finally {
      uni.hideLoading()
    }
  } else {
    uni.showToast({
      title: '获取手机号失败',
      icon: 'none',
    })
  }
}
</script>

<style>
page {
  background: linear-gradient(180deg, #E6F3FF 0%, #FFFFFF 100%);
}

.container {
  min-height: 100vh;
  padding: 0 40rpx;
  box-sizing: border-box;
}

.content {
  padding-top: 35vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 80rpx;
}

.btn-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.login-btn {
  width: 100% !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  background: #1989FA !important;
  color: #FFFFFF !important;
  font-size: 32rpx !important;
  border-radius: 44rpx !important;
  border: none !important;
  margin-bottom: 40rpx !important;
  text-align: center !important;
  padding: 0 !important;
}

.login-btn::after {
  border: none !important;
}

.agreement {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  width: 100%;
}

.agreement-text {
  color: #666;
  margin: 0 4rpx;
}

.link {
  color: #1989FA;
}

/* 重置微信小程序按钮默认样式 */
button {
  margin: 0;
  padding: 0;
}

button::after {
  border: none;
}
</style>
