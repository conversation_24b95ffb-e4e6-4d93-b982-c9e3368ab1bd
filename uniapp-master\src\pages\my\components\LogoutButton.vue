<template>
  <view v-if="isLoggedIn" class="mt-6 mb-10">
    <view
      class="w-full py-3 bg-white dark:bg-gray-800 rounded-xl flex items-center justify-center shadow-md"
      @click="handleLogout"
    >
      <text class="text-red-500 text-sm font-medium">退出登录</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
  isLoggedIn: boolean
}>()

const emit = defineEmits(['logout'])

const handleLogout = () => {
  emit('logout')
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'LogoutButton',
})
</script>
