<template>
  <view class="bg-white dark:bg-gray-900 flex flex-col">
    <!-- 标题 -->
    <view class="py-4 text-center border-b border-gray-100 dark:border-gray-800">
      <text class="text-lg font-medium text-gray-800 dark:text-white">{{ currentTitle }}</text>
    </view>

    <!-- 协议内容 -->
    <scroll-view scroll-y class="flex-1 px-4 py-3">
      <view v-if="type === 'agreement'">
        <view class="mb-6">
          <text class="text-base font-bold text-gray-800 dark:text-white mb-3 block">
            一、服务协议的范围
          </text>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-2">
            1.1 本协议是您与本应用之间关于使用本应用服务所订立的协议。
          </view>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            1.2 您在使用本应用服务时，须遵守本协议的所有条款。
          </view>
        </view>

        <view class="mb-6">
          <text class="text-base font-bold text-gray-800 dark:text-white mb-3 block">
            二、账号使用规则
          </text>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-2">
            2.1 您注册成功后，将获得一个账号及相应的密码。
          </view>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            2.2 您应妥善保管账号及密码信息，不得将账号转让或出借于他人。
          </view>
        </view>

        <view class="mb-6">
          <text class="text-base font-bold text-gray-800 dark:text-white mb-3 block">
            三、用户行为规范
          </text>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-2">
            3.1 您在使用本服务时必须遵守中华人民共和国相关法律法规。
          </view>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            3.2 您不得利用本服务从事违法违规活动。
          </view>
        </view>
      </view>

      <view v-else>
        <view class="mb-6">
          <text class="text-base font-bold text-gray-800 dark:text-white mb-3 block">
            一、信息收集
          </text>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-2">
            1.1 为了提供更好的服务，我们可能收集您的以下信息：
          </view>
          <view class="ml-4 space-y-1">
            <view class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
              <text class="i-carbon-dot-mark text-indigo-500 mr-2 mt-1 text-xs"></text>
              <text>账号信息（手机号、登录记录等）</text>
            </view>
            <view class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
              <text class="i-carbon-dot-mark text-indigo-500 mr-2 mt-1 text-xs"></text>
              <text>设备信息（设备型号、操作系统等）</text>
            </view>
            <view class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
              <text class="i-carbon-dot-mark text-indigo-500 mr-2 mt-1 text-xs"></text>
              <text>服务日志信息（登录时间、使用时长等）</text>
            </view>
          </view>
        </view>

        <view class="mb-6">
          <text class="text-base font-bold text-gray-800 dark:text-white mb-3 block">
            二、信息使用
          </text>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-2">
            2.1 我们可能将收集的信息用于以下用途：
          </view>
          <view class="ml-4 space-y-1">
            <view class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
              <text class="i-carbon-dot-mark text-indigo-500 mr-2 mt-1 text-xs"></text>
              <text>向您提供服务</text>
            </view>
            <view class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
              <text class="i-carbon-dot-mark text-indigo-500 mr-2 mt-1 text-xs"></text>
              <text>改进和优化服务体验</text>
            </view>
            <view class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
              <text class="i-carbon-dot-mark text-indigo-500 mr-2 mt-1 text-xs"></text>
              <text>保障账号安全</text>
            </view>
          </view>
        </view>

        <view class="mb-6">
          <text class="text-base font-bold text-gray-800 dark:text-white mb-3 block">
            三、信息保护
          </text>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-2">
            3.1 我们将采取合理措施保护您的个人信息安全。
          </view>
          <view class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            3.2 未经您同意，我们不会向第三方分享您的个人信息。
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 确认按钮 -->
    <view class="p-4 border-t border-gray-100 dark:border-gray-800">
      <button
        class="py-2 px-4 bg-indigo-600 text-white text-sm rounded-lg font-medium flex items-center justify-center mx-auto"
        @click="handleConfirm"
      >
        <text class="i-carbon-checkmark mr-1 text-xs"></text>
        <text>我已阅读并同意</text>
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'agreement', // 'agreement' 或 'privacy'
  },
})

const emit = defineEmits(['confirm'])

const currentTitle = computed(() => {
  return props.type === 'agreement' ? '用户协议' : '隐私政策'
})

const handleConfirm = () => {
  emit('confirm')
}
</script>

<!-- 添加额外的script标签提供默认导出 -->
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'Agreement',
})
</script>

<style>
.agreement-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #ffffff;
}

.agreement-header {
  padding: 40rpx 32rpx;
  text-align: center;
  border-bottom: 2rpx solid #f5f5f5;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.agreement-content {
  flex: 1;
  padding: 32rpx;
}

.agreement-section {
  padding-bottom: 40rpx;
}

.section {
  margin-bottom: 48rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.section-content {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
}

.section-list {
  padding-left: 16rpx;
}

.list-item {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
}

.agreement-footer {
  padding: 32rpx;
  border-top: 2rpx solid #f5f5f5;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  line-height: 88rpx;
  color: #ffffff;
  text-align: center;
  background-color: #07c160;
  border: none;
  border-radius: 8rpx;
}

.confirm-btn::after {
  border: none;
}
</style>
