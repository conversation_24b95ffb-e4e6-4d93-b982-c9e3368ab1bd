<route lang="json5" type="page">
{
  layout: 'tabbar',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="my-page">
    <!-- 用户信息头部 -->
    <UserProfileHeader
      :userInfo="userInfo"
      :isLoggedIn="isLoggedIn"
      :userStats="userStats"
      :topSafeHeight="topSafeHeight"
      @login="toLogin"
      @editProfile="editProfile"
      @updateAvatar="handleAvatarUpdate"
    />

    <!-- 功能区域 -->
    <view class="content-area">
      <!-- 快捷服务 -->
      <view class="service-section">
        <view class="section-title">
          <text class="title-text">快捷服务</text>
        </view>
        <view class="service-grid">
          <view
            v-for="service in quickServices"
            :key="service.id"
            class="service-item"
            @tap="handleServiceClick(service)"
          >
            <view class="service-icon" :style="{ background: service.bgColor }">
              <text class="icon" :style="{ color: service.iconColor }">{{ service.icon }}</text>
            </view>
            <text class="service-title">{{ service.title }}</text>
          </view>
        </view>
      </view>

      <!-- 我的账单 -->
      <view class="bill-section">
        <view class="section-title">
          <text class="title-text">我的账单</text>
          <text class="more-text" @tap="goToBillHistory">查看全部</text>
        </view>
        <view class="bill-stats">
          <view class="bill-item">
            <text class="bill-amount">¥{{ billStats.unpaid }}</text>
            <text class="bill-label">待缴费用</text>
          </view>
          <view class="bill-divider"></view>
          <view class="bill-item">
            <text class="bill-amount">¥{{ billStats.thisMonth }}</text>
            <text class="bill-label">本月账单</text>
          </view>
          <view class="bill-divider"></view>
          <view class="bill-item">
            <text class="bill-amount">{{ billStats.usage }}m³</text>
            <text class="bill-label">本月用量</text>
          </view>
        </view>
      </view>

      <!-- 功能列表 -->
      <view class="feature-section">
        <view class="feature-list">
          <view
            v-for="feature in mainFeatures"
            :key="feature.id"
            class="feature-item"
            @tap="handleFeatureClick(feature)"
          >
            <view class="feature-left">
              <view class="feature-icon" :style="{ background: feature.bgColor }">
                <text class="icon" :style="{ color: feature.iconColor }">{{ feature.icon }}</text>
              </view>
              <text class="feature-title">{{ feature.title }}</text>
            </view>
            <view class="feature-right">
              <text v-if="feature.badge" class="feature-badge">{{ feature.badge }}</text>
              <text class="feature-arrow">›</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view v-if="isLoggedIn" class="logout-section">
        <view class="logout-btn" @tap="handleLogout">
          <text class="logout-text">退出登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { logout, updateAvatar } from '@/api/login'
import UserProfileHeader from './components/UserProfileHeader.vue'
import OrderSection from './components/OrderSection.vue'
import FeatureSection from './components/FeatureSection.vue'
import LogoutButton from './components/LogoutButton.vue'
import { reactive, watch, computed } from 'vue'
import { storeToRefs } from 'pinia'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets, statusBarHeight } = uni.getSystemInfoSync()

// 计算顶部安全距离
const topSafeHeight = computed(() => {
  const statusBarHeightPx = statusBarHeight || 0

  // 尝试获取胶囊按钮信息（仅小程序环境）
  let capsuleHeight = 32 // 默认胶囊按钮高度
  try {
    // #ifdef MP
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
    if (menuButtonInfo) {
      // 胶囊按钮底部到状态栏顶部的距离 + 一些额外间距
      capsuleHeight = menuButtonInfo.bottom - statusBarHeightPx + 8
    }
    // #endif
  } catch (e) {
    console.log('获取胶囊按钮信息失败，使用默认值')
  }

  return statusBarHeightPx + capsuleHeight
})

// 用户信息
const userStore = useUserStore()
// 使用 storeToRefs 保持响应性
const { userInfo, isLoggedIn } = storeToRefs(userStore)

// 监听用户信息变化
watch(
  () => userInfo.value,
  (newUserInfo) => {
    console.log('用户信息变化:', newUserInfo)
  },
  { deep: true, immediate: true },
)

// 用户统计数据
const userStats = reactive({
  favorites: 0,
  following: 0,
  followers: 0,
  history: 0,
})

// 账单统计数据
const billStats = reactive({
  unpaid: 156.80,
  thisMonth: 89.60,
  usage: 25.6
})

// 快捷服务
const quickServices = reactive([
  {
    id: 1,
    title: '缴费记录',
    icon: '📊',
    bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    iconColor: '#fff',
    action: 'payment_history'
  },
  {
    id: 2,
    title: '用量分析',
    icon: '📈',
    bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    iconColor: '#fff',
    action: 'usage_analysis'
  },
  {
    id: 3,
    title: '账户管理',
    icon: '👤',
    bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    iconColor: '#fff',
    action: 'account_manage'
  },
  {
    id: 4,
    title: '在线客服',
    icon: '💬',
    bgColor: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    iconColor: '#fff',
    action: 'customer_service'
  }
])

// 主要功能列表
const mainFeatures = reactive([
  {
    id: 1,
    title: '消息通知',
    icon: '🔔',
    bgColor: '#fff3e0',
    iconColor: '#ff9500',
    badge: '3',
    action: 'notifications'
  },
  {
    id: 2,
    title: '帮助中心',
    icon: '❓',
    bgColor: '#e8f5e8',
    iconColor: '#34c759',
    action: 'help_center'
  },
  {
    id: 3,
    title: '意见反馈',
    icon: '💭',
    bgColor: '#e3f2fd',
    iconColor: '#007aff',
    action: 'feedback'
  },
  {
    id: 4,
    title: '关于我们',
    icon: 'ℹ️',
    bgColor: '#f3e5f5',
    iconColor: '#9c27b0',
    action: 'about'
  }
])

// 跳转到登录页
const toLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index',
  })
}

// 编辑个人资料
const editProfile = () => {
  //uni.navigateTo({
  //  url: '/pages/profile/edit',
  //})
}

// 查看全部订单
const viewAllOrders = () => {
  // uni.navigateTo({
  //   url: '/pages/order/list',
  // })
}

// 处理订单类型点击
const handleOrderTypeClick = ({ item, index }) => {
  //uni.navigateTo({
  //  url: `/pages/order/list?type=${index}`,
  //})
}

// 处理快捷服务点击
const handleServiceClick = (service) => {
  console.log('点击了服务：', service.title)
  switch (service.action) {
    case 'payment_history':
      uni.navigateTo({ url: '/pages/payment/history' })
      break
    case 'usage_analysis':
      uni.navigateTo({ url: '/pages/usage/index' })
      break
    case 'account_manage':
      uni.navigateTo({ url: '/pages/account/index' })
      break
    case 'customer_service':
      uni.showToast({
        title: '客服功能开发中',
        icon: 'none'
      })
      break
    default:
      uni.showToast({
        title: `点击了${service.title}`,
        icon: 'none'
      })
  }
}

// 处理功能点击
const handleFeatureClick = (feature) => {
  console.log('点击了功能：', feature.title)
  switch (feature.action) {
    case 'notifications':
      uni.showToast({
        title: '消息通知功能开发中',
        icon: 'none'
      })
      break
    case 'help_center':
      uni.showToast({
        title: '帮助中心功能开发中',
        icon: 'none'
      })
      break
    case 'feedback':
      uni.showToast({
        title: '意见反馈功能开发中',
        icon: 'none'
      })
      break
    case 'about':
      uni.showToast({
        title: '关于我们功能开发中',
        icon: 'none'
      })
      break
    default:
      uni.showToast({
        title: `点击了${feature.title}`,
        icon: 'none'
      })
  }
}

// 跳转到账单历史
const goToBillHistory = () => {
  uni.navigateTo({ url: '/pages/payment/history' })
}

// 处理头像更新
const handleAvatarUpdate = async (avatarUrl: string) => {
  console.log('开始处理头像更新，临时路径:', avatarUrl)
  
  // 显示加载提示
  uni.showLoading({
    title: '更新头像中...'
  })
  
  try {
    // 直接上传头像文件到服务器（后端会同时更新用户头像）
    console.log('准备上传到:', import.meta.env.VITE_SERVER_BASEURL + '/user/upload-avatar')
    console.log('使用token:', userStore.userInfo.token)
    
    const uploadResult = await uni.uploadFile({
      url: import.meta.env.VITE_SERVER_BASEURL + '/user/upload-avatar',
      filePath: avatarUrl,
      name: 'avatar',
      header: {
        'Authorization': 'Bearer ' + userStore.userInfo.token
      }
    })
    
    console.log('上传结果:', uploadResult)
    const uploadData = JSON.parse(uploadResult.data)
    console.log('解析后的数据:', uploadData)
    
    if (uploadData.code === 0) {
      console.log('上传成功，服务器返回的头像URL:', uploadData.data.avatar)
      // 更新本地用户信息
      userStore.setUserInfo({
        ...userStore.userInfo,
        avatar: uploadData.data.avatar
      })
      uni.hideLoading()
      uni.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    } else {
      throw new Error(uploadData.message || '上传失败')
    }
  } catch (error) {
    console.error('头像更新失败:', error)
    console.error('错误详情:', JSON.stringify(error))
    uni.hideLoading()
    uni.showToast({
      title: '头像更新失败',
      icon: 'error'
      })
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 先调用退出登录接口
          await logout()
          // 成功后再清除本地用户信息
          userStore.clearUserInfo()
          uni.showToast({
            title: '退出成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('退出登录失败:', error)
          uni.showToast({
            title: '退出失败，请重试',
            icon: 'error'
        })
        }
      }
    },
  })
}


</script>

<style scoped>
.my-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20px);
}

.content-area {
  padding: 0 20rpx;
  margin-top: -20rpx;
}

.service-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-text {
  font-size: 26rpx;
  color: #007aff;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.service-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-icon .icon {
  font-size: 36rpx;
}

.service-title {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.bill-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.bill-stats {
  display: flex;
  align-items: center;
}

.bill-item {
  flex: 1;
  text-align: center;
}

.bill-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.bill-label {
  font-size: 24rpx;
  color: #666;
}

.bill-divider {
  width: 1rpx;
  height: 60rpx;
  background: #f0f0f0;
  margin: 0 24rpx;
}

.feature-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.feature-list {
  padding: 16rpx 0;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.feature-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon .icon {
  font-size: 32rpx;
}

.feature-title {
  font-size: 30rpx;
  color: #333;
}

.feature-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.feature-badge {
  background: #ff3b30;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

.feature-arrow {
  font-size: 32rpx;
  color: #c7c7cc;
}

.logout-section {
  padding: 32rpx 0;
}

.logout-btn {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.logout-text {
  font-size: 30rpx;
  color: #ff3b30;
  font-weight: 500;
}
</style>
