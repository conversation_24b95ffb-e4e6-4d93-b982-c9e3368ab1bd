<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AccountOpenRequestController as OpenApi;
use App\Http\Controllers\Api\AccountCancellationRequestController as CancelApi;
use App\Http\Controllers\Api\ActiveAccountController;
use App\Http\Controllers\Api\EsignCallbackController;
use App\Http\Controllers\Api\WechatController;
use App\Http\Controllers\Api\CarouselController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ContractController;
use App\Http\Controllers\Api\DocumentController;
c

Route::post('login', [AuthController::class, 'login']);
//e签宝回调
Route::post('admin/esign/callback', [EsignCallbackController::class, 'handle']);
//微信支付回调
Route::post('payment/notify', [WaterPaymentController::class, 'notify']);

// 微信小程序登录
Route::post('/wechat/login', [WechatController::class, 'login']);

// 轮播图
Route::get('/carousels', [CarouselController::class, 'index']);

Route::middleware('auth:sanctum')->group(function () {
    // 退出登录
    Route::post('/wechat/logout', [WechatController::class, 'logout']);
    Route::get ('user',     [AuthController::class, 'user']);

    // 用户个人中心
    Route::get('user/profile', [UserController::class, 'profile']);
    Route::post('user/profile', [UserController::class, 'updateProfile']);
    Route::post('user/upload-avatar', [UserController::class, 'uploadAvatar']);
    Route::post('user/avatar', [UserController::class, 'updateAvatar']);
    Route::get('user/account-overview', [UserController::class, 'accountOverview']);

    // 开户申请
    Route::get  ('open-requests',        [OpenApi::class, 'index']);
    Route::post ('open-requests',        [OpenApi::class, 'store']);
    Route::get  ('open-requests/{id}',   [OpenApi::class, 'show']);

    // 销户申请
    Route::get  ('cancel-requests',      [CancelApi::class, 'index']);
    Route::post ('cancel-requests',      [CancelApi::class, 'store']);
    Route::get  ('cancel-requests/{id}', [CancelApi::class, 'show']);

    // 已开户留存
    Route::get('active-accounts', [ActiveAccountController::class, 'index']);

    // 合同管理
    Route::get('contracts', [ContractController::class, 'index']);
    Route::get('contracts/statistics', [ContractController::class, 'statistics']);
    Route::get('contracts/{id}', [ContractController::class, 'show']);
    Route::post('contracts/{id}/sign', [ContractController::class, 'sign']);
    Route::post('contracts/{id}/reject', [ContractController::class, 'reject']);
    Route::get('contracts/water-meter/{waterMeterNo}', [ContractController::class, 'getByWaterMeter']);

    // 文档转换
    Route::post('documents/convert-to-images', [DocumentController::class, 'convertToImages']);

    // 水费缴费
    Route::get('water-payments', [WaterPaymentController::class, 'index']);
    Route::post('water-payments', [WaterPaymentController::class, 'store']);
    Route::get('water-payments/{id}', [WaterPaymentController::class, 'show']);
    Route::post('water-payments/{id}/pay', [WaterPaymentController::class, 'pay']);
    Route::get('water-payments/{id}/status', [WaterPaymentController::class, 'checkStatus']);

    // 合同检查（支付前置检查）
    Route::get('water-payments/check-contract/{water_meter_no}', [WaterPaymentController::class, 'checkContract']);
});
