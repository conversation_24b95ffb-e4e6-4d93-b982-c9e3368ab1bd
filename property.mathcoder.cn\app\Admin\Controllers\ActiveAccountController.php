<?php

namespace App\Admin\Controllers;

use App\Models\ActiveAccount;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ActiveAccountController extends AdminController
{
    
    /**
     * 设置标题
     */
    protected $title = '存量用户';
    
    protected function grid(): Grid
    {
        return Grid::make(new ActiveAccount(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user.name', '开户用户');
            $grid->column('community', '小区');
            $grid->column('building_number', '楼号');
            $grid->column('name', '姓名');
            $grid->column('phone', '电话');
            $grid->column('water_meter_number', '水表号');
            $grid->column('opened_at', '开户时间');
            $grid->column('created_at', '记录创建')->sortable();
            $grid->column('updated_at', '记录更新')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '开户用户')->select(User::pluck('name', 'id'));
                $filter->like('community', '小区');
                $filter->like('water_meter_number', '水表号');
                $filter->between('opened_at', '开户时间')->datetime();
            });

            // 不允许前端界面新增、编辑或删除
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableBatchActions();
        });
    }

    protected function form(): Form
    {
        return Form::make(new ActiveAccount(), function (Form $form) {
            $form->display('id', 'ID');
            $form->display('user.username', '开户用户');
            $form->display('community', '小区');
            $form->display('building_number', '楼号');
            $form->display('name', '姓名');
            $form->display('phone', '电话');
            $form->display('water_meter_number', '水表号');
            $form->display('opened_at', '开户时间');
            $form->display('created_at', '记录创建');
            $form->display('updated_at', '记录更新');

            $form->disableSubmitButton();
            $form->disableResetButton();
        });
    }
}
