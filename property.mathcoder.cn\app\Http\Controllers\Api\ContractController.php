<?php

namespace App\Http\Controllers\Api;

use App\Models\ContractSign;
use Illuminate\Http\Request;

class ContractController extends ApiController
{
    /**
     * 获取用户合同列表
     */
    public function index(Request $request)
    {
        $query = ContractSign::with(['template', 'user'])
            ->where('user_id', $request->user()->id);

        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // 分页
        $page = $request->get('page', 1);
        $pageSize = $request->get('page_size', 10);
        
        $contracts = $query->orderBy('created_at', 'desc')
            ->paginate($pageSize, ['*'], 'page', $page);

        return $this->success([
            'data' => $contracts->items(),
            'current_page' => $contracts->currentPage(),
            'last_page' => $contracts->lastPage(),
            'per_page' => $contracts->perPage(),
            'total' => $contracts->total(),
            'has_more' => $contracts->hasMorePages(),
        ]);
    }

    /**
     * 获取合同详情
     */
    public function show(Request $request, $id)
    {
        $contract = ContractSign::with(['template', 'user'])
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        return $this->success($contract);
    }

    /**
     * 签署合同 - 系统内签署
     */
    public function sign(Request $request, $id)
    {
        $request->validate([
            'signature_name' => 'required|string|max:100',
        ]);

        $contract = ContractSign::with('template')->where('user_id', $request->user()->id)
            ->where('status', 'sent')
            ->findOrFail($id);

        try {
            // 更新合同签署状态
            $contract->update([
                'status' => 'signed',
                'signed_at' => now(),
                'signature_name' => $request->signature_name,
                'signature_ip' => $request->ip(),
            ]);

            return $this->success($contract, '合同签署成功');

        } catch (\Exception $e) {
            \Log::error('合同签署失败: ' . $e->getMessage());
            return $this->error('合同签署失败，请重试');
        }
    }

    /**
     * 拒绝签署合同
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reject_reason' => 'required|string|max:500',
        ]);

        $contract = ContractSign::where('user_id', $request->user()->id)
            ->where('status', 'sent')
            ->findOrFail($id);

        $contract->update([
            'status' => 'rejected',
            'reject_reason' => $request->reject_reason,
        ]);

        return $this->success($contract, '已拒绝签署');
    }

    /**
     * 获取合同统计信息
     */
    public function statistics(Request $request)
    {
        $userId = $request->user()->id;

        $stats = [
            'pending' => ContractSign::where('user_id', $userId)->where('status', 'sent')->count(),
            'signed' => ContractSign::where('user_id', $userId)->where('status', 'signed')->count(),
            'rejected' => ContractSign::where('user_id', $userId)->where('status', 'rejected')->count(),
            'total' => ContractSign::where('user_id', $userId)->count(),
        ];

        return $this->success($stats);
    }

    /**
     * 根据水表号查询合同
     */
    public function getByWaterMeter(Request $request, $waterMeterNo)
    {
        $contract = ContractSign::with(['template', 'user'])
            ->where('user_id', $request->user()->id)
            ->where('water_meter_number', $waterMeterNo)
            ->where('status', 'signed')
            ->first();

        if (!$contract) {
            return $this->error('未找到该水表号的有效合同');
        }

        return $this->success($contract);
    }
}
