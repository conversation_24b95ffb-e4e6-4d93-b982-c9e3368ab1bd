<template>
  <view class="px-4 pb-4 bg-white">
    <view class="grid grid-cols-5 gap-3">
      <view
        v-for="(category, index) in categories"
        :key="index"
        class="flex flex-col items-center"
        @click="handleCategoryClick(category)"
      >
        <view class="w-[50px] h-[50px] rounded-full flex items-center justify-center mb-2" :style="{ backgroundColor: category.bgColor }">
          <text :class="category.icon + ' text-2xl text-white'"></text>
        </view>
        <text class="text-xs text-gray-800 text-center">{{ category.title }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Category {
  id: number
  title: string
  icon: string
  bgColor: string
  link: string
}

const props = defineProps<{
  categories: Category[]
}>()

const emit = defineEmits(['click'])

const handleCategoryClick = (category: Category) => {
  emit('click', category)
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'CategoryNav',
})
</script>


