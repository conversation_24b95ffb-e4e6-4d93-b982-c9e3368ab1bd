<template>
  <view class="bg-white dark:bg-gray-800 rounded-2xl p-5 shadow-md mb-4">
    <view class="flex justify-between items-center mb-4">
      <text class="text-base font-bold text-gray-800 dark:text-white">我的订单</text>
      <view class="flex items-center" @click="viewAllOrders">
        <text class="text-xs text-gray-500 dark:text-gray-400">全部订单</text>
        <text class="i-carbon-chevron-right text-gray-400 dark:text-gray-500 ml-1"></text>
      </view>
    </view>

    <view class="grid grid-cols-4 gap-2">
      <view 
        v-for="(item, index) in orderTypes" 
        :key="index" 
        class="flex flex-col items-center"
        @click="handleOrderTypeClick(item, index)"
      >
        <view
          class="w-12 h-12 rounded-full bg-indigo-50 dark:bg-indigo-900/30 flex items-center justify-center mb-2"
        >
          <text :class="`${item.icon} ${item.color}`"></text>
        </view>
        <text class="text-xs text-gray-600 dark:text-gray-300">{{ item.title }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface OrderType {
  icon: string
  title: string
  color: string
}

const props = defineProps<{
  orderTypes: OrderType[]
}>()

const emit = defineEmits(['viewAll', 'typeClick'])

const viewAllOrders = () => {
  emit('viewAll')
}

const handleOrderTypeClick = (item: OrderType, index: number) => {
  emit('typeClick', { item, index })
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'OrderSection',
})
</script>
