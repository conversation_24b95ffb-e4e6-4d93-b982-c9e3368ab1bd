<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AccountCancellationRequest;

class AccountCancellationRequestController extends Controller
{
    public function index(Request $request)
    {
        $list = AccountCancellationRequest::withoutGlobalScope('pending')
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($list);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'water_meter_number' => 'required|string|exists:active_accounts,water_meter_number',
        ]);

        $req = AccountCancellationRequest::create([
            'user_id'            => $request->user()->id,
            'water_meter_number' => $data['water_meter_number'],
            'status'             => AccountCancellationRequest::STATUS_PENDING,
        ]);

        return response()->json($req, 201);
    }

    public function show(Request $request, $id)
    {
        $req = AccountCancellationRequest::withoutGlobalScope('pending')
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        return response()->json($req);
    }
}
