import { http } from '@/utils/http'

// 获取已绑定账户列表
export const getBoundAccounts = () => {
  return http.get('/active-accounts')
}

// 获取账户列表（用于缴费记录筛选）
export const getAccountList = () => {
  return http.get('/active-accounts')
}

// 获取申请记录列表
export const getAccountRequests = (params: { page: number; type?: string }) => {
  const url = params.type === 'open' ? '/open-requests' : 
              params.type === 'cancel' ? '/cancel-requests' : 
              '/open-requests'  // 默认显示开户申请
  return http.get(url, { page: params.page })
}

// 提交开户申请
export const submitAccountOpenRequest = (data: {
  community: string
  building_number: string
  name: string
  phone: string
}) => {
  return http.post('/open-requests', data)
}

// 提交销户申请
export const submitAccountCancelRequest = (data: {
  water_meter_number: string
}) => {
  console.log('准备发送销户申请:', data)
  return http.post('/cancel-requests', data)
}

