<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '用量详情'
  }
}
</route>

<template>
  <view class="detail-page">
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>
    
    <view v-else-if="usageDetail" class="usage-detail">
      <!-- 用量概览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <text class="period-title">{{ usageDetail.period }}</text>
          <text class="status-badge" :class="usageDetail.status">
            {{ getStatusText(usageDetail.status) }}
          </text>
        </view>
        <view class="usage-display">
          <text class="usage-amount">{{ usageDetail.usage }}</text>
          <text class="usage-unit">立方米</text>
        </view>
        <view class="cost-info">
          <text class="cost-label">本期费用</text>
          <text class="cost-amount">¥{{ usageDetail.cost }}</text>
        </view>
      </view>

      <!-- 抄表信息 -->
      <view class="info-card">
        <view class="card-title">
          <text class="title-text">抄表信息</text>
        </view>
        <view class="info-item">
          <text class="info-label">水表号</text>
          <text class="info-value">{{ usageDetail.water_meter_no }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用水地址</text>
          <text class="info-value">{{ usageDetail.address }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">抄表日期</text>
          <text class="info-value">{{ formatDate(usageDetail.reading_date) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">起始读数</text>
          <text class="info-value">{{ usageDetail.start_reading }} m³</text>
        </view>
        <view class="info-item">
          <text class="info-label">结束读数</text>
          <text class="info-value">{{ usageDetail.end_reading }} m³</text>
        </view>
        <view class="info-item">
          <text class="info-label">用水量</text>
          <text class="info-value highlight">{{ usageDetail.usage }} m³</text>
        </view>
      </view>

      <!-- 费用明细 -->
      <view class="info-card">
        <view class="card-title">
          <text class="title-text">费用明细</text>
        </view>
        <view class="fee-breakdown">
          <view v-for="tier in usageDetail.fee_tiers" :key="tier.tier" class="fee-tier">
            <view class="tier-header">
              <text class="tier-name">{{ tier.name }}</text>
              <text class="tier-range">{{ tier.range }}</text>
            </view>
            <view class="tier-details">
              <text class="tier-usage">用量：{{ tier.usage }} m³</text>
              <text class="tier-price">单价：¥{{ tier.unit_price }}/m³</text>
              <text class="tier-amount">小计：¥{{ tier.amount }}</text>
            </view>
          </view>
          <view class="fee-total">
            <text class="total-label">总计费用</text>
            <text class="total-amount">¥{{ usageDetail.cost }}</text>
          </view>
        </view>
      </view>

      <!-- 历史对比 -->
      <view class="info-card">
        <view class="card-title">
          <text class="title-text">历史对比</text>
        </view>
        <view class="comparison-grid">
          <view class="comparison-item">
            <text class="comparison-label">上月用量</text>
            <text class="comparison-value">{{ usageDetail.last_month_usage }} m³</text>
            <text class="comparison-change" :class="{ 'increase': usageDetail.month_change > 0, 'decrease': usageDetail.month_change < 0 }">
              {{ usageDetail.month_change > 0 ? '+' : '' }}{{ usageDetail.month_change }}%
            </text>
          </view>
          <view class="comparison-item">
            <text class="comparison-label">去年同期</text>
            <text class="comparison-value">{{ usageDetail.last_year_usage }} m³</text>
            <text class="comparison-change" :class="{ 'increase': usageDetail.year_change > 0, 'decrease': usageDetail.year_change < 0 }">
              {{ usageDetail.year_change > 0 ? '+' : '' }}{{ usageDetail.year_change }}%
            </text>
          </view>
        </view>
      </view>

      <!-- 用量趋势图 -->
      <view class="info-card">
        <view class="card-title">
          <text class="title-text">用量趋势</text>
        </view>
        <view class="chart-container">
          <view class="chart-placeholder">
            <text class="chart-text">用量趋势图</text>
            <text class="chart-desc">（图表功能开发中）</text>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view v-if="usageDetail.remark" class="info-card">
        <view class="card-title">
          <text class="title-text">备注信息</text>
        </view>
        <view class="remark-content">
          <text class="remark-text">{{ usageDetail.remark }}</text>
        </view>
      </view>
    </view>

    <view v-else class="error">
      <text>用量记录不存在</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const loading = ref(true)
const usageDetail = ref(null)
const recordId = ref('')

onLoad((options) => {
  if (options.id) {
    recordId.value = options.id
  }
})

// Mock详情数据
const mockUsageDetail = {
  id: 1,
  period: '2024年1月',
  water_meter_no: 'WM001234567',
  address: '阳光小区 1栋2单元301',
  start_reading: 1245.6,
  end_reading: 1271.2,
  usage: 25.6,
  cost: 89.60,
  status: 'confirmed',
  reading_date: '2024-01-31',
  last_month_usage: 23.2,
  month_change: 10.3,
  last_year_usage: 22.8,
  year_change: 12.3,
  fee_tiers: [
    {
      tier: 1,
      name: '第一阶梯',
      range: '0-15m³',
      usage: 15.0,
      unit_price: 2.80,
      amount: 42.00
    },
    {
      tier: 2,
      name: '第二阶梯',
      range: '15-25m³',
      usage: 10.0,
      unit_price: 4.10,
      amount: 41.00
    },
    {
      tier: 3,
      name: '第三阶梯',
      range: '25m³以上',
      usage: 0.6,
      unit_price: 6.00,
      amount: 3.60
    }
  ],
  remark: '本月用水量较上月有所增加，请注意节约用水。'
}

const fetchUsageDetail = async () => {
  if (!recordId.value) return

  try {
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock数据
    usageDetail.value = mockUsageDetail
    
  } catch (error) {
    console.error('获取用量详情失败:', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    confirmed: '已确认',
    pending: '待确认',
    estimated: '预估值'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

onMounted(() => {
  fetchUsageDetail()
})
</script>

<style scoped>
.detail-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
}

.loading,
.error {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.overview-card {
  background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 20rpx;
  color: white;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.period-title {
  font-size: 32rpx;
  font-weight: 600;
}

.status-badge {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.2);
}

.usage-display {
  text-align: center;
  margin-bottom: 32rpx;
}

.usage-amount {
  font-size: 72rpx;
  font-weight: 600;
  display: block;
}

.usage-unit {
  font-size: 28rpx;
  opacity: 0.8;
  margin-top: 8rpx;
}

.cost-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-label {
  font-size: 28rpx;
  opacity: 0.8;
}

.cost-amount {
  font-size: 36rpx;
  font-weight: 600;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.card-title {
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 200rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.info-value.highlight {
  color: #007aff;
  font-weight: 600;
}

.fee-breakdown {
  margin-top: 16rpx;
}

.fee-tier {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.tier-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.tier-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tier-range {
  font-size: 24rpx;
  color: #666;
}

.tier-details {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

.fee-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-top: 2rpx solid #e0e0e0;
  margin-top: 16rpx;
}

.total-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.total-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #007aff;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.comparison-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.comparison-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.comparison-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.comparison-change {
  font-size: 24rpx;
  font-weight: 500;
}

.comparison-change.increase {
  color: #ff3b30;
}

.comparison-change.decrease {
  color: #34c759;
}

.chart-container {
  margin-top: 16rpx;
}

.chart-placeholder {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.chart-desc {
  font-size: 24rpx;
  color: #999;
}

.remark-content {
  margin-top: 16rpx;
}

.remark-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
</style>
