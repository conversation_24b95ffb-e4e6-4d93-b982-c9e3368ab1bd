<template>
  <view class="px-4 py-2.5 bg-white">
    <view class="relative">
      <wd-search
        v-model="searchValue"
        placeholder="搜索商品"
        cancel-button
        use-action-slot
        @search="onSearch"
        @clear="onClear"
        @cancel="onCancel"
        @change="onChange"
      ></wd-search>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const searchValue = ref('')

const emit = defineEmits(['search', 'clear', 'cancel', 'change'])

// 搜索事件
const onSearch = () => {
  emit('search', searchValue.value)
}

// 清空事件
const onClear = () => {
  emit('clear')
}

// 取消事件
const onCancel = () => {
  emit('cancel')
}

// 输入变化事件
const onChange = (e: { value: string }) => {
  emit('change', e.value)
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SearchBar',
})
</script>
