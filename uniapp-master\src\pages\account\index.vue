<template>
  <view class="container">
    <!-- 顶部卡片：显示已绑定账户 -->
    <view class="bound-accounts">
      <view class="accounts-header" @tap="toggleAccountsExpanded">
        <view class="header-left">
          <text class="section-title">已绑定账户</text>
          <text class="account-count">({{ boundAccounts.length }})</text>
        </view>
        <view class="header-right">
          <text class="expand-icon" :class="{ expanded: accountsExpanded }">▼</text>
        </view>
      </view>

      <view v-if="boundAccounts.length > 0 && accountsExpanded" class="account-list">
        <view v-for="account in boundAccounts" :key="account.id" class="account-item">
          <view class="account-info">
            <view class="account-main">
              <text class="account-title">{{ account.community }} {{ account.building_number }}</text>
              <text class="account-number">{{ account.water_meter_number }}</text>
            </view>
            <view class="account-status" :class="getAccountStatusClass(account.status)">
              {{ getAccountStatusText(account.status) }}
            </view>
          </view>
        </view>

      </view>

      <view v-if="boundAccounts.length > 0 && !accountsExpanded" class="show-more" @tap="toggleAccountsExpanded">
        <text class="show-more-text">查看全部 {{ boundAccounts.length }} 个账户</text>
      </view>

      <view v-if="boundAccounts.length === 0" class="empty-tip">
        <text class="empty-text">暂无绑定账户</text>
        <text class="empty-desc">请先申请开户</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" @tap="navigateTo('/pages/account/open/index')">
        申请开户
      </button>
      <button class="action-btn secondary" @tap="navigateTo('/pages/account/cancel/index')">
        申请销户
      </button>
      <button class="action-btn refresh" @tap="handleRefresh" :disabled="isRefreshing">
        {{ isRefreshing ? '刷新中...' : '刷新数据' }}
      </button>
    </view>

    <!-- 申请记录标题 -->
    <view class="section-title">申请记录</view>

    <!-- Tab切换 -->
    <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @tap="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 申请列表 -->
    <scroll-view 
      scroll-y 
      class="request-list"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="requests.length > 0">
        <view v-for="request in requests" :key="request.id" class="request-item">
          <view class="request-header">
            <text class="request-type">{{ request.type === 'open' ? '开户申请' : '销户申请' }}</text>
            <text :class="['request-status', request.status]">{{ getStatusText(request.status) }}</text>
          </view>
          <view class="request-content">
            <view class="info-row" v-if="request.type === 'open'">
              <text class="label">小区：</text>
              <text class="value">{{ request.community }}</text>
            </view>
            <view class="info-row" v-if="request.type === 'open'">
              <text class="label">楼号：</text>
              <text class="value">{{ request.building_number }}</text>
            </view>
            <view class="info-row" v-if="request.type === 'cancel'">
              <text class="label">水表号：</text>
              <text class="value">{{ request.water_meter_number }}</text>
            </view>
            <view class="info-row">
              <text class="label">申请时间：</text>
              <text class="value">{{ formatDate(request.created_at) }}</text>
            </view>
            <view class="info-row" v-if="request.remark">
              <text class="label">拒绝原因：</text>
              <text class="value">{{ request.remark }}</text>
            </view>
          </view>
        </view>
      </view>
      <view v-else class="empty-tip">
        <text class="empty-text">暂无申请记录</text>
        <text class="empty-desc">点击上方按钮开始申请</text>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="hasMore" class="loading-more">加载中...</view>
      <view v-else-if="requests.length > 0" class="no-more">没有更多了</view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getBoundAccounts } from '@/api/account'
import { getAccountRequests } from '@/api/account'

// 标签页
const tabs = ['全部', '开户申请', '销户申请']
const currentTab = ref(0)

// 数据列表
const boundAccounts = ref([])
const requests = ref([])
const currentPage = ref(1)
const hasMore = ref(true)
const isRefreshing = ref(false)

// 账户列表折叠状态
const accountsExpanded = ref(false)

// 账户列表展开/收起状态

// 切换账户列表展开/折叠
const toggleAccountsExpanded = () => {
  accountsExpanded.value = !accountsExpanded.value
}

// 获取账户状态样式类
const getAccountStatusClass = (status) => {
  switch (status) {
    case 'active':
    case '正常':
      return 'status-active'
    case 'suspended':
    case '暂停':
      return 'status-suspended'
    case 'arrears':
    case '欠费':
      return 'status-arrears'
    default:
      return 'status-normal'
  }
}

// 获取账户状态文本
const getAccountStatusText = (status) => {
  switch (status) {
    case 'active':
      return '正常'
    case 'suspended':
      return '暂停'
    case 'arrears':
      return '欠费'
    default:
      return '正常'
  }
}

// 获取已绑定账户
const fetchBoundAccounts = async () => {
  try {
    const res = await getBoundAccounts()
    boundAccounts.value = res.data || []
  } catch (error) {
    console.error('获取绑定账户失败:', error)
    uni.showToast({
      title: '获取账户失败',
      icon: 'error'
    })
  }
}

// 获取申请记录
const fetchRequests = async (page = 1, replace = true) => {
  try {
    const type = currentTab.value === 1 ? 'open' : currentTab.value === 2 ? 'cancel' : ''
    const res = await getAccountRequests({ page, type })
    
    const dataList = res.data || []
    const processedData = dataList.map(item => ({
      ...item,
      type: type || 'open'
    }))
    
    if (replace) {
      requests.value = processedData
    } else {
      requests.value = [...requests.value, ...processedData]
    }
    hasMore.value = dataList.length >= 10 // 假设每页10条
    currentPage.value = page
    
  } catch (error) {
    console.error('获取申请记录失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'error'
    })
  }
}

// 切换标签
const switchTab = (index: number) => {
  currentTab.value = index
  currentPage.value = 1
  hasMore.value = true
  fetchRequests(1)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value) {
    fetchRequests(currentPage.value + 1, false)
  }
}

// 下拉刷新
const onRefresh = async () => {
  isRefreshing.value = true
  await fetchRequests(1)
  isRefreshing.value = false
}

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 格式化状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    processed: '已处理'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''

  // 将 "2025-07-16 14:55:37" 格式转换为 iOS 兼容的格式
  const isoDateStr = dateStr.replace(' ', 'T')
  const date = new Date(isoDateStr)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateStr)
    return dateStr
  }

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    fetchBoundAccounts(),
    fetchRequests(1, true)
  ])
}

// 手动刷新
const handleRefresh = async () => {
  if (isRefreshing.value) return

  isRefreshing.value = true
  try {
    await refreshAllData()
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1500
    })
  } catch (error) {
    console.error('刷新失败:', error)
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'error',
      duration: 1500
    })
  } finally {
    isRefreshing.value = false
  }
}

onMounted(() => {
  refreshAllData()
})

// 将刷新方法挂载到全局，供页面生命周期调用
const app = getApp()
if (app) {
  app.globalData = app.globalData || {}
  app.globalData.refreshAccountData = refreshAllData
}
</script>

<script lang="ts">
// UniApp 页面生命周期
export default {
  onShow() {
    // 页面显示时刷新数据（从开户/销户页面返回时）
    const app = getApp()
    if (app && app.globalData && app.globalData.refreshAccountData) {
      app.globalData.refreshAccountData()
    }
  }
}
</script>

<style>
.container {
  padding: 32rpx 32rpx 0;
  min-height: 100vh;
  background: #F5F7FA;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.bound-accounts {
  background: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.accounts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
}

.accounts-header:active {
  background: #f8f9fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.account-count {
  font-size: 24rpx;
  color: #999;
}

.header-right {
  display: flex;
  align-items: center;
}

.expand-icon {
  font-size: 20rpx;
  color: #999;
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.account-list {
  max-height: 1000rpx;
  overflow: hidden;
  transition: all 0.3s;
}

.account-list.collapsed {
  max-height: 0;
  opacity: 0;
  padding: 0;
}

.account-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.account-item:last-child {
  border-bottom: none;
}

.account-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.account-main {
  flex: 1;
}

.account-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.account-number {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.account-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.account-status.status-active {
  color: #52c41a;
  background: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.account-status.status-suspended {
  color: #faad14;
  background: #fffbe6;
  border: 1rpx solid #ffe58f;
}

.account-status.status-arrears {
  color: #ff4d4f;
  background: #fff2f0;
  border: 1rpx solid #ffccc7;
}

.account-status.status-normal {
  color: #52c41a;
  background: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.show-more {
  padding: 24rpx 32rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  transition: all 0.2s;
}

.show-more:active {
  background: #f0f0f0;
}

.show-more-text {
  font-size: 28rpx;
  color: #1890ff;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  border: none;
}

.action-btn.primary {
  background: #1989FA !important;
  color: #FFFFFF !important;
}

.action-btn.secondary {
  background: #FF4D4F !important;
  color: #FFFFFF !important;
}

.action-btn.refresh {
  background: #f5f5f5 !important;
  color: #666 !important;
}

.action-btn.refresh:disabled {
  opacity: 0.6;
}

.tabs {
  display: flex;
  background: #FFFFFF;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1989FA;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #1989FA;
  border-radius: 2rpx;
}

.request-list {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  height: calc(100vh - 600rpx);
}

.request-item {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.request-type {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.request-status {
  font-size: 26rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.request-status.pending {
  color: #FAAD14;
  background: #FFF7E6;
}

.request-status.approved {
  color: #52C41A;
  background: #F6FFED;
}

.request-status.rejected {
  color: #FF4D4F;
  background: #FFF1F0;
}

.request-status.processed {
  color: #1989FA;
  background: #E6F7FF;
}

.empty-tip {
  text-align: center;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.empty-desc {
  color: #ccc;
  font-size: 24rpx;
}

.loading-more, .no-more {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx 0;
}
</style> 