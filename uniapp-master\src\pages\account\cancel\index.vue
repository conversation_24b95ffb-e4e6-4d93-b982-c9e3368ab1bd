<template>
  <view class="container">
    <view class="form-group">
      <view class="form-item">
        <text class="label">水表号</text>
        <picker 
          class="picker" 
          :range="boundAccounts" 
          range-key="water_meter_number"
          @change="handleAccountChange"
        >
          <view class="picker-value" :class="{ placeholder: !selectedAccount }">
            {{ selectedAccount ? selectedAccount.water_meter_number : '请选择要销户的水表号' }}
          </view>
        </picker>
      </view>

      <view class="account-info" v-if="selectedAccount">
        <view class="info-item">
          <text class="info-label">小区</text>
          <text class="info-value">{{ selectedAccount.community }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">楼号</text>
          <text class="info-value">{{ selectedAccount.building_number }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">{{ selectedAccount.name }}</text>
        </view>
      </view>
    </view>

    <view class="tips">
      <view class="tip-item">
        <text class="dot">•</text>
        <text class="tip-text">销户后将无法使用该水表号缴费</text>
      </view>
      <view class="tip-item">
        <text class="dot">•</text>
        <text class="tip-text">请确保已结清所有水费</text>
      </view>
      <view class="tip-item">
        <text class="dot">•</text>
        <text class="tip-text">如有疑问请联系客服</text>
      </view>
    </view>

    <button
      class="submit-btn"
      :disabled="!selectedAccount || isSubmitting"
      @tap="handleSubmit"
    >
      {{ isSubmitting ? '提交中...' : '提交申请' }}
    </button>


  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getBoundAccounts, submitAccountCancelRequest } from '@/api/account'
import { useUserStore } from '@/store'

const boundAccounts = ref([])
const selectedAccount = ref(null)
const isSubmitting = ref(false)

// 获取已绑定账户
const fetchBoundAccounts = async () => {
  try {
    const res = await getBoundAccounts()
    if (res.code === 0) {
      boundAccounts.value = res.data
    }
  } catch (error) {
    console.error('获取绑定账户失败:', error)
    uni.showToast({
      title: '获取账户信息失败',
      icon: 'error'
    })
  }
}

// 选择账户
const handleAccountChange = (e: any) => {
  const index = e.detail.value
  selectedAccount.value = boundAccounts.value[index]
}

// 提交申请
const handleSubmit = async () => {
  if (!selectedAccount.value || isSubmitting.value) return

  // 检查水表号是否为空
  if (!selectedAccount.value.water_meter_number) {
    uni.showToast({
      title: '水表号为空',
      icon: 'error',
      duration: 2000
    })
    return
  }

  try {
    isSubmitting.value = true

    console.log('提交销户申请，水表号:', selectedAccount.value.water_meter_number)

    const res = await submitAccountCancelRequest({
      water_meter_number: selectedAccount.value.water_meter_number
    })
    
    if (res.code === 0) {
      uni.showToast({
        title: '申请提交成功',
        icon: 'success'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.message || '提交失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('提交销户申请失败:', error)

    uni.showToast({
      title: '提交失败，请重试',
      icon: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}

onMounted(() => {
  fetchBoundAccounts()
})
</script>

<style>
.container {
  min-height: 100vh;
  padding: 32rpx;
  background: #F5F7FA;
}

.form-group {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.label {
  width: 140rpx;
  font-size: 30rpx;
  color: #333;
}

.picker {
  flex: 1;
}

.picker-value {
  font-size: 30rpx;
  color: #333;
}

.picker-value.placeholder {
  color: #999;
}

.account-info {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.tips {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.dot {
  color: #FF4D4F;
  margin-right: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.submit-btn {
  position: fixed;
  left: 32rpx;
  right: 32rpx;
  bottom: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  background: #FF4D4F;
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 44rpx;
  text-align: center;
}

.submit-btn[disabled] {
  background: #CCCCCC;
  color: #FFFFFF;
}
</style> 