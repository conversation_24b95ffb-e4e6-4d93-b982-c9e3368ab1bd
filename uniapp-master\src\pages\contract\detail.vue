<template>
  <view class="container">
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>

    <view v-else-if="contract" class="content">
      <!-- 合同文件预览 -->
      <scroll-view class="file-preview-container" scroll-y v-if="contract.template && contract.template.file_path">
        <view class="document-loading" v-if="documentLoading">
          <text>正在加载文档...</text>
        </view>
        <view class="document-pages" v-else-if="documentPages.length > 0">
          <image
            v-for="(page, index) in documentPages"
            :key="index"
            :src="page"
            class="document-page-image"
            mode="widthFix"
            @error="handleImageError"
            @load="handleImageLoad"
          />
        </view>
        <view class="document-error" v-else>
          <text>文档加载失败，请重试</text>
          <button class="retry-btn" @tap="loadDocumentPages">重新加载</button>
        </view>
      </scroll-view>

      <!-- 底部悬浮操作按钮 -->
      <view class="floating-actions" v-if="contract.status === 'sent'">
        <button class="floating-btn reject-btn" @tap="handleReject">
          拒绝签署
        </button>
        <button class="floating-btn sign-btn" @tap="handleSign">
          去签署
        </button>
      </view>
    </view>

    <view v-else class="error">
      <text>合同信息加载失败</text>
      <button class="retry-btn" @tap="loadContractDetail">重试</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getContractDetail, signContract, rejectContract } from '@/api/contract'
import { http } from '@/utils/http'

// 生成文件URL的工具函数
const getFileUrl = (filePath: string) => {
  const baseUrl = 'https://property.oneself.icu'
  // 后端存储路径是 storage/app/files/xxx.pdf，访问路径是 /storage/files/xxx.pdf
  return `${baseUrl}/storage/${filePath}`
}

// 生成PDF查看器URL
const getPdfViewerUrl = (filePath: string) => {
  const pdfUrl = getFileUrl(filePath)
  // 使用在线PDF.js查看器，限制高度不覆盖按钮
  return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(pdfUrl)}`
}

// 页面参数
const contractId = ref('')

// 页面状态
const loading = ref(true)
const contract = ref(null)
const documentPages = ref([])
const documentLoading = ref(false)



// 加载合同详情
const loadContractDetail = async () => {
  if (!contractId.value) return

  loading.value = true
  try {
    const res = await getContractDetail(parseInt(contractId.value))
    if (res.code === 0) {
      contract.value = res.data.data || res.data
      console.log('合同详情:', contract.value)

      // 调试文件URL
      if (contract.value?.template?.file_path) {
        const fileUrl = getFileUrl(contract.value.template.file_path)
        console.log('文件路径:', contract.value.template.file_path)
        console.log('完整文件URL:', fileUrl)
        // 加载文档页面图片
        loadDocumentPages()
      }
    } else {
      uni.showToast({
        title: res.message || '加载失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('加载合同详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 全屏查看
const openFullscreen = () => {
  if (!contract.value?.template?.file_path) {
    uni.showToast({
      title: '文件不存在',
      icon: 'error'
    })
    return
  }

  const fileUrl = getFileUrl(contract.value.template.file_path)
  console.log('打开全屏文件URL:', fileUrl)

  uni.navigateTo({
    url: `/pages/webview/index?url=${encodeURIComponent(fileUrl)}&title=${encodeURIComponent(contract.value.template.name || '合同文件')}`
  })
}

// 签署合同 - 系统内签署
const handleSign = async () => {
  if (!contract.value) return

  // 弹出输入框让用户输入姓名
  uni.showModal({
    title: '确认签署',
    content: '请输入您的真实姓名以确认签署合同',
    editable: true,
    placeholderText: '请输入姓名',
    success: async (res) => {
      if (res.confirm && res.content) {
        const signatureName = res.content.trim()

        if (!signatureName) {
          uni.showToast({
            title: '请输入有效姓名',
            icon: 'error'
          })
          return
        }

        try {
          uni.showLoading({ title: '正在签署...' })

          // 调用后端API进行签署
          const result = await signContract(contract.value.id, {
            signature_name: signatureName
          })

          const isSuccess = result.code === 0 || result.success === true

          if (isSuccess) {
            uni.showToast({
              title: '签署成功',
              icon: 'success'
            })
            // 重新加载合同详情
            loadContractDetail()
          } else {
            uni.showToast({
              title: result.data?.message || result.message || '签署失败',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('签署失败:', error)
          uni.showToast({
            title: '签署失败，请重试',
            icon: 'error'
          })
        } finally {
          uni.hideLoading()
        }
      }
    }
  })
}

// 拒绝签署
const handleReject = async () => {
  if (!contract.value) return

  uni.showModal({
    title: '拒绝签署',
    content: '请输入拒绝原因',
    editable: true,
    placeholderText: '请输入拒绝原因',
    success: async (res) => {
      if (res.confirm && res.content) {
        const rejectReason = res.content.trim()

        if (!rejectReason) {
          uni.showToast({
            title: '请输入拒绝原因',
            icon: 'error'
          })
          return
        }

        try {
          uni.showLoading({ title: '处理中...' })
          const result = await rejectContract(contract.value.id, {
            reject_reason: rejectReason
          })

          // 处理HTTP工具的双重包装
          const isSuccess = result.code === 0 || result.success === true

          if (isSuccess) {
            uni.showToast({
              title: '已拒绝签署',
              icon: 'success'
            })
            // 重新加载合同详情
            loadContractDetail()
          } else {
            uni.showToast({
              title: result.data?.message || result.message || '操作失败',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('拒绝签署失败:', error)
          uni.showToast({
            title: '操作失败，请重试',
            icon: 'error'
          })
        } finally {
          uni.hideLoading()
        }
      }
    }
  })
}

// 打开e签宝链接
const openEsignUrl = () => {
  if (!contract.value?.esign_url) return
  
  // 在小程序中打开web-view或外部链接
  uni.navigateTo({
    url: `/pages/webview/index?url=${encodeURIComponent(contract.value.esign_url)}`
  })
}



// 加载文档页面图片
const loadDocumentPages = async () => {
  if (!contract.value?.template?.file_path) return

  documentLoading.value = true
  try {
    // 调用后端API将文档转换为图片
    const result = await http.post('/documents/convert-to-images', {
      file_path: contract.value.template.file_path,
      contract_id: contract.value.id
    })

    // HTTP工具包装了响应：{code: 0, data: {success: true, data: {pages: [...]}}}
    if (result.code === 0 && result.data?.success === true && result.data?.data?.pages) {
      documentPages.value = result.data.data.pages
      console.log('✅ 文档页面加载成功，共', result.data.data.pages.length, '页')
    } else {
      throw new Error(result.data?.message || result.message || '文档转换失败')
    }
  } catch (error) {
    console.error('加载文档页面失败:', error)
    uni.showToast({
      title: '文档加载失败',
      icon: 'error'
    })
    documentPages.value = []
  } finally {
    documentLoading.value = false
  }
}

// 处理图片加载错误
const handleImageError = (e) => {
  console.error('文档页面图片加载失败:', e)
}

// 处理图片加载成功
const handleImageLoad = (e) => {
  console.log('文档页面图片加载成功:', e)
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''

  // 将 "2025-07-16 14:55:37" 格式转换为 iOS 兼容的格式
  const isoDateStr = dateString.replace(' ', 'T')
  const date = new Date(isoDateStr)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateString)
    return dateString
  }

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待发送',
    'sent': '待签署',
    'signed': '已签署',
    'rejected': '已拒绝'
  }
  return statusMap[status] || status
}

// 页面加载
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  contractId.value = currentPage.options.id || ''
  
  if (contractId.value) {
    loadContractDetail()
  } else {
    loading.value = false
    uni.showToast({
      title: '参数错误',
      icon: 'error'
    })
  }
})
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  padding: 0;
}

.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.retry-btn {
  margin-top: 15px;
  padding: 8px 20px;
  background-color: #1890ff;
  color: white;
  border-radius: 5px;
  font-size: 14px;
}

.info-card, .file-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.contract-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-pending {
  color: #faad14;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.status-sent {
  color: #1890ff;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.status-signed {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.status-rejected {
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.info-list {
  padding: 15px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 80px;
  color: #666666;
  font-size: 14px;
}

.info-value {
  flex: 1;
  color: #333333;
  font-size: 14px;
}

.file-info {
  display: flex;
  align-items: center;
  padding: 15px;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon {
  font-size: 24px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.file-desc {
  font-size: 12px;
  color: #999999;
  display: block;
}

.preview-btn {
  padding: 5px 12px;
  background-color: #f0f0f0;
  color: #666666;
  border-radius: 5px;
  font-size: 12px;
}

.action-section {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn.secondary {
  background-color: #ffffff;
  color: #ff4d4f;
  border: 1px solid #ff4d4f;
}

.esign-section {
  margin-bottom: 15px;
}

.esign-btn {
  width: 100%;
  height: 44px;
  background-color: #52c41a;
  color: white;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.reject-reason {
  width: 100%;
  height: 100px;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  font-size: 14px;
  margin-top: 10px;
}

/* 文件预览相关样式 */
.file-preview-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.file-preview {
  width: 100%;
  height: 500px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  margin: 15px 0;
}

.file-webview {
  width: 100%;
  height: 100%;
}

.file-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.file-actions .action-btn {
  flex: 1;
  padding: 10px 15px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

.action-btn.download {
  background: #f0f9ff;
  color: #0369a1;
}

.action-btn.fullscreen {
  background: #f0fdf4;
  color: #166534;
}

.file-type {
  background: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

/* 文件预览容器 */
.file-preview-container {
  width: 100%;
  height: calc(100vh - 120px); /* 减去按钮高度 */
  background: #f5f5f5;
}

/* 文档加载状态 */
.document-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
}

/* 文档页面容器 */
.document-pages {
  padding: 10px;
}

/* 文档页面图片 */
.document-page-image {
  width: 100%;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
}

/* 文档错误状态 */
.document-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
}

.retry-btn {
  margin-top: 20px;
  padding: 10px 20px;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

/* 底部悬浮按钮样式 */
.floating-actions {
  position: fixed;
  bottom: 30px;
  left: 20px;
  right: 20px;
  display: flex;
  gap: 15px;
  z-index: 1000;
}

.floating-btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.reject-btn {
  background: #fff;
  color: #ff4d4f;
  border: 2px solid #ff4d4f;
}

.sign-btn {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
}

.floating-btn:active {
  transform: scale(0.95);
}
</style>
