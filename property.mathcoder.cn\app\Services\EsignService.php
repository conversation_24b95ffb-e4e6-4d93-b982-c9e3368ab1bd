<?php
namespace App\Services;

use GuzzleHttp\Client;
use App\Models\ContractSign;
use Illuminate\Support\Facades\Config;

class EsignService
{
    protected $client;
    protected $appId;
    protected $secret;
    protected $host;
    protected $callback;

    public function __construct()
    {
        $this->appId    = Config::get('services.esign.app_id');
        $this->secret   = Config::get('services.esign.secret');
        $this->host     = Config::get('services.esign.host');
        $this->callback = Config::get('services.esign.callback');
        $this->client   = new Client(['base_uri' => $this->host]);
    }

    /**
     * 创建签署流程，并更新 $sign
     */
    public function createFlow(ContractSign $sign)
    {
        try {
            // 检查必要的配置
            if (!$this->appId || !$this->secret || !$this->host) {
                throw new \Exception('e签宝配置不完整');
            }

            // 检查合同模板文件是否存在
            if (!$sign->template || !$sign->template->file_path) {
                throw new \Exception('合同模板文件不存在');
            }

            $filePath = storage_path('app/' . $sign->template->file_path);
            if (!file_exists($filePath)) {
                throw new \Exception('合同模板文件不存在: ' . $filePath);
            }

            // 1. 上传文件到e签宝
            $fileId = $this->uploadFile($sign->template->file_path);

            // 2. 创建签署流程
            $resp = $this->client->post('/v1/signflows/createByFiles', [
                'json' => [
                    'flowName'   => "合同#{$sign->id}",
                    'fileIds'    => [$fileId],
                    'signers'    => [[
                        'signerAccountId' => $this->getAccountId($sign->user_id),
                        'signApproverRole' => 'SIGNER',
                    ]],
                    'callbackUrl'=> $this->callback,
                ],
                'headers' => $this->authHeaders(),
            ]);

            $responseBody = $resp->getBody()->getContents();
            $responseData = json_decode($responseBody, true);

            if (!$responseData || !isset($responseData['data']['flowId'])) {
                throw new \Exception('创建签署流程失败: ' . $responseBody);
            }

            $flowId = $responseData['data']['flowId'];

            // 3. 获取签署链接
            $urlResp = $this->client->get("/v1/signflows/{$flowId}/views/web", [
                'query' => ['accountId' => $this->getAccountId($sign->user_id)],
                'headers' => $this->authHeaders(),
            ]);

            $urlResponseBody = $urlResp->getBody()->getContents();
            $urlResponseData = json_decode($urlResponseBody, true);

            if (!$urlResponseData || !isset($urlResponseData['data']['url'])) {
                throw new \Exception('获取签署链接失败: ' . $urlResponseBody);
            }

            $viewUrl = $urlResponseData['data']['url'];

            // 4. 更新模型
            $sign->update([
                'esign_flow_id' => $flowId,
                'esign_url'     => $viewUrl,
                'esign_status'  => 'sent',
            ]);

            \Log::info('e签宝签署流程创建成功', [
                'contract_id' => $sign->id,
                'flow_id' => $flowId,
                'url' => $viewUrl
            ]);

        } catch (\Exception $e) {
            \Log::error('e签宝签署流程创建失败', [
                'contract_id' => $sign->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /** 上传合同文件到 e签宝 返回 fileId */
    protected function uploadFile(string $path)
    {
        $full = storage_path('app/' . $path);

        if (!file_exists($full)) {
            throw new \Exception('文件不存在: ' . $full);
        }

        if (!is_readable($full)) {
            throw new \Exception('文件不可读: ' . $full);
        }

        try {
            $resp = $this->client->post('/v1/files/upload', [
                'multipart' => [
                    [
                        'name'     => 'file',
                        'contents' => fopen($full, 'r'),
                        'filename' => basename($full),
                    ]
                ],
                'headers' => $this->authHeaders(),
            ]);

            $responseBody = $resp->getBody()->getContents();
            $responseData = json_decode($responseBody, true);

            if (!$responseData || !isset($responseData['data']['fileId'])) {
                throw new \Exception('文件上传失败: ' . $responseBody);
            }

            \Log::info('文件上传成功', [
                'file_path' => $path,
                'file_id' => $responseData['data']['fileId']
            ]);

            return $responseData['data']['fileId'];

        } catch (\Exception $e) {
            \Log::error('文件上传失败', [
                'file_path' => $path,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /** 根据用户ID获取 e签宝账户ID（可根据自己业务填充） */
    protected function getAccountId($userId)
    {
        // 假设和系统 user_id 一一对应
        return (string)$userId;
    }

    /** 生成授权头 */
    protected function authHeaders()
    {
        // e签宝简单示例：将appId和secret做Base64
        $token = base64_encode("{$this->appId}:{$this->secret}");
        return [
            'Authorization' => "Basic {$token}",
            'Content-Type'  => 'application/json',
        ];
    }

    /**
     * 处理回调
     */
    public function handleCallback(array $payload)
    {
        try {
            \Log::info('收到e签宝回调', ['payload' => $payload]);

            $flowId = $payload['flowId'] ?? null;
            $status = $payload['status'] ?? null;

            if (!$flowId) {
                \Log::warning('e签宝回调缺少flowId', ['payload' => $payload]);
                return;
            }

            $sign = ContractSign::where('esign_flow_id', $flowId)->first();
            if (!$sign) {
                \Log::warning('未找到对应的合同签署记录', ['flow_id' => $flowId]);
                return;
            }

            $update = ['esign_callback_payload' => json_encode($payload)];

            // 根据不同状态更新合同状态
            if (in_array($status, ['SIGNED', 'COMPLETED'])) {
                $update['esign_status'] = 'signed';
                $update['status'] = 'signed';
                $update['signed_at'] = now();

                \Log::info('合同签署完成', [
                    'contract_id' => $sign->id,
                    'flow_id' => $flowId,
                    'status' => $status
                ]);

            } elseif ($status === 'CANCELED') {
                $update['esign_status'] = 'canceled';
                $update['status'] = 'rejected';

                \Log::info('合同签署被取消', [
                    'contract_id' => $sign->id,
                    'flow_id' => $flowId
                ]);

            } elseif ($status === 'REJECTED') {
                $update['esign_status'] = 'rejected';
                $update['status'] = 'rejected';

                \Log::info('合同签署被拒绝', [
                    'contract_id' => $sign->id,
                    'flow_id' => $flowId
                ]);
            } else {
                \Log::info('e签宝状态更新', [
                    'contract_id' => $sign->id,
                    'flow_id' => $flowId,
                    'status' => $status
                ]);
            }

            $sign->update($update);

        } catch (\Exception $e) {
            \Log::error('处理e签宝回调失败', [
                'payload' => $payload,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
