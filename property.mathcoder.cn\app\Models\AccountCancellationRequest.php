<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class AccountCancellationRequest extends Model
{
    use HasDateTimeFormatter;
    // 对应数据表
    protected $table = 'account_cancellation_requests';

    // 可批量赋值字段
    protected $fillable = [
        'user_id',
        'water_meter_number',
        'status',
        'remark',
        'rejection_reason',
    ];

    // 审核状态
    public const STATUS_PENDING   = 'pending';   // 待审核
    public const STATUS_PROCESSED = 'processed'; // 已处理
    public const STATUS_REJECTED  = 'rejected';  // 已拒绝

    /**
     * 默认只加载待审核记录
     */
    protected static function booted()
    {
        static::addGlobalScope('pending', function (Builder $builder) {
            $builder->where('status', self::STATUS_PENDING);
        });
    }

    /**
     * 关联申请用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 审核通过：更新状态并删除留存账户
     *
     * @return void
     */
    public function approve(): void
    {
        $this->update([
            'status'           => self::STATUS_PROCESSED,
            'rejection_reason' => null,
        ]);

        ActiveAccount::where('water_meter_number', $this->water_meter_number)
            ->delete();
    }

    /**
     * 审核拒绝：更新状态并记录原因
     *
     * @param  string  $reason
     * @return void
     */
    public function reject(string $reason): void
    {
        $this->update([
            'status'           => self::STATUS_REJECTED,
            'rejection_reason' => $reason,
        ]);
    }
}
