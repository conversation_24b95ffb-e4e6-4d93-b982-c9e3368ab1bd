import { CustomRequestOptions } from '@/interceptors/request'
import { toLogin } from './toPath'

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    // 调试信息：打印请求详情
    console.log('HTTP请求详情:', {
      url: options.url,
      method: options.method || 'GET',
      data: options.data,
      header: options.header
    })

    uni.request({
      ...options,
      method: options.method || 'GET',
      data: options.data,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        // 调试信息：打印响应详情
        console.log('HTTP响应详情:', {
          statusCode: res.statusCode,
          data: res.data,
          header: res.header
        })

        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 如果是注销请求，直接返回成功
          if (options.url.endsWith('/logout')) {
            return resolve({ code: 10000, message: '退出成功' } as IResData<T>)
          }

          const data = res.data as IResData<T>
          
          // 如果返回的是数组或者没有code字段，认为是成功的
          if (Array.isArray(data) || (typeof data === 'object' && !('code' in data))) {
            return resolve({ code: 0, data: data as T, message: 'success' } as IResData<T>)
          }
          
          // 业务逻辑成功
          if (data.code === 10000 || data.code === 0) {
            return resolve(data)
          } else if (data.code >= 10003 && data.code <= 10007) {
            // 业务逻辑失败，token 过期或无效
            toLogin()
          } else {
            // 业务逻辑失败，其他错误
            !options.hideErrorToast &&
              uni.showToast({
                icon: 'none',
                title: data.message || '请求错误',
              })
            reject(data)
          }
        } else if (res.statusCode === 401) {
          // 未授权，可能是 token 失效
          toLogin()
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          const errMsg = (res.data as any)?.message || `请求失败(${res.statusCode})`
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: errMsg,
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * 文件上传
 * @param options
 * @returns
 */
export const fileUpload = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.uploadFile({
      ...options,
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // uni.uploadFile 返回的 data 是字符串，需要手动解析
          const data = JSON.parse(res.data as string) as IResData<T>
          // 业务逻辑成功
          if (data.code === 10000 || data.code === 0) {
            return resolve(data)
          } else if (data.code >= 10003 && data.code <= 10007) {
            // 业务逻辑失败，token 过期或无效
            toLogin()
          } else {
            // 业务逻辑失败，其他错误
            uni.showToast({
              icon: 'none',
              title: data.message || '请求错误',
            })
            reject(data)
          }
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          uni.showToast({
            icon: 'none',
            title: '上传失败: ' + res.statusCode,
          })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpGet = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
  })
}
/**
 * PUT 请求
 */
export const httpPut = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
  })
}

/**
 * DELETE 请求（无请求体，仅 query）
 */
export const httpDelete = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
  })
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete
http.fileUpload = fileUpload
