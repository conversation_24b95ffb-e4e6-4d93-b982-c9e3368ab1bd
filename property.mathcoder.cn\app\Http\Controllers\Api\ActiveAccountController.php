<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ActiveAccount;

class ActiveAccountController extends Controller
{
    /**
     * 返回当前用户的已开户记录
     */
    public function index(Request $request)
    {
        $accounts = ActiveAccount::where('user_id', $request->user()->id)
            ->orderBy('opened_at', 'desc')
            ->get();

        return response()->json($accounts);
    }
}
