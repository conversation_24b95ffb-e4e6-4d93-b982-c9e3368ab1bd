<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use App\Models\User;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    
    //轮播图广告管理
    $router->resource('ad-carousel', 'AdCarouselController');
    
    
    //开户销户管理
    //开户管理
    $router->resource('account-open-request', 'AccountOpenRequestController');
    //销户管理
    $router->resource('account-cancellation-request', 'AccountCancellationRequestController');
    //存量用户管理
    $router->resource('active-account', 'ActiveAccountController');
    
    //合同管理
    //合同模板管理
    $router->resource('contract-template', 'ContractTemplateController');
    //合同签署管理
    $router->resource('contract-sign', 'ContractSignController');
    //合同发送给用户
    $router->post('contract-sign/{id}/send', 'ContractSignController@send');

    //水费缴费管理
    $router->resource('water-payment', 'WaterPaymentController');
    //手动标记已支付
    $router->post('water-payment/{id}/mark-paid', 'WaterPaymentController@markPaid');

    //搜索用户
    $router->get('api/users', function (Request $request) {
        $q = $request->get('q', '');
        return User::where('name', 'like', "%{$q}%")
            ->paginate(null, ['id', 'name as text']);
    });
    
    
    

   

});
