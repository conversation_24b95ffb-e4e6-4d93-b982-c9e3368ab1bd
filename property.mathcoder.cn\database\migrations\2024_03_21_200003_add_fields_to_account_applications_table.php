<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('account_applications', function (Blueprint $table) {
            $table->string('application_number')->unique()->after('type');
            $table->string('id_card_front_image')->after('id_card_number');
            $table->string('id_card_back_image')->after('id_card_front_image');
        });
    }

    public function down(): void
    {
        Schema::table('account_applications', function (Blueprint $table) {
            $table->dropColumn(['application_number', 'id_card_front_image', 'id_card_back_image']);
        });
    }
}; 