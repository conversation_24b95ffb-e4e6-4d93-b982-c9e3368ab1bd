<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('contract_signs', function (Blueprint $table) {
            $table->id()->comment('自增主键');
            $table->unsignedBigInteger('contract_template_id')->comment('合同模板 ID');
            $table->unsignedBigInteger('user_id')->comment('签署用户 ID');
            $table->enum('status', ['pending', 'sent', 'signed', 'rejected'])
                  ->default('pending')
                  ->comment('签署状态: 待发送/pending、已发送/sent、已签署/signed、已拒签/rejected');
            $table->timestamp('signed_at')->nullable()->comment('实际签署时间');
            $table->string('esign_flow_id')->nullable()->comment('e签宝流程ID');
            $table->string('esign_url', 1024)->nullable()->comment('e签宝签署跳转地址');
            $table->string('esign_status', 20)->default('pending')->comment('e签宝签署状态');
            $table->text('esign_callback_payload')->nullable()->comment('e签宝回调原始数据');
            $table->string('reject_reason', 500)->nullable()->comment('拒绝原因');
            $table->timestamps();

            // 索引
            $table->index('contract_template_id', 'idx_contract_template');
            $table->index('user_id', 'idx_user');
            $table->index('status', 'idx_status');
            $table->index('esign_flow_id', 'idx_esign_flow_id');

            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('contract_signs');
    }
};
