<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '缴费记录'
  }
}
</route>

<template>
  <view class="history-page">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @tap="showAccountFilter">
        <text class="filter-text">{{ currentAccountText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" @tap="showStatusFilter">
        <text class="filter-text">{{ currentStatusText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>

    <!-- 缴费记录列表 -->
    <scroll-view 
      class="payment-list"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="payments.length === 0 && !loading" class="empty">
        <text class="empty-text">暂无缴费记录</text>
      </view>
      
      <view v-else class="list-content">
        <view 
          v-for="payment in payments" 
          :key="payment.id"
          class="payment-item"
          @tap="viewDetail(payment.id)"
        >
          <view class="payment-left">
            <view class="payment-header">
              <text class="payment-meter">{{ payment.water_meter_no }}</text>
              <text class="payment-status" :class="payment.status">
                {{ getStatusText(payment.status) }}
              </text>
            </view>
            <text class="payment-address">{{ payment.address || '地址信息暂无' }}</text>
            <text class="payment-no">订单号：{{ payment.payment_no }}</text>
            <text class="payment-time">{{ formatDateTime(payment.created_at) }}</text>
          </view>
          <view class="payment-right">
            <text class="payment-amount">¥{{ payment.amount }}</text>
            <text class="payment-method">{{ getPaymentMethodText(payment.payment_method) }}</text>
          </view>
        </view>
      </view>

      <view v-if="loading && payments.length > 0" class="loading-more">
        <text>加载更多...</text>
      </view>

      <view v-if="noMore && payments.length > 0" class="no-more">
        <text>没有更多了</text>
      </view>
    </scroll-view>

    <!-- 账户筛选弹窗 -->
    <wd-popup
      v-model="showAccountPicker"
      position="bottom"
      custom-style="border-radius: 20rpx 20rpx 0 0;"
    >
      <view class="account-picker">
        <view class="picker-header">
          <text class="picker-title">选择账户</text>
          <text class="picker-close" @tap="showAccountPicker = false">✕</text>
        </view>
        <view class="account-options">
          <view
            v-for="account in accountOptions"
            :key="account.value"
            class="account-option"
            :class="{ active: currentAccount === account.value }"
            @tap="selectAccount(account.value)"
          >
            <view class="account-info">
              <text class="account-meter">{{ account.water_meter_no }}</text>
              <text class="account-address">{{ account.address }}</text>
            </view>
            <text v-if="currentAccount === account.value" class="option-check">✓</text>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 状态筛选弹窗 -->
    <wd-popup
      v-model="showStatusPicker"
      position="bottom"
      custom-style="border-radius: 20rpx 20rpx 0 0;"
    >
      <view class="status-picker">
        <view class="picker-header">
          <text class="picker-title">选择状态</text>
          <text class="picker-close" @tap="showStatusPicker = false">✕</text>
        </view>
        <view class="status-options">
          <view
            v-for="status in statusOptions"
            :key="status.value"
            class="status-option"
            :class="{ active: currentStatus === status.value }"
            @tap="selectStatus(status.value)"
          >
            <text class="option-text">{{ status.label }}</text>
            <text v-if="currentStatus === status.value" class="option-check">✓</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPaymentList } from '@/api/payment'
import { getAccountList } from '@/api/account'

const payments = ref([])
const loading = ref(false)
const refreshing = ref(false)
const noMore = ref(false)
const currentPage = ref(1)
const currentStatus = ref('')
const currentAccount = ref('')
const showStatusPicker = ref(false)
const showAccountPicker = ref(false)
const accountOptions = ref([{ value: '', label: '全部账户', water_meter_no: '全部账户', address: '' }])

// 页面参数
const pageParams = ref({
  account_id: '',
  account_name: ''
})

onLoad((options) => {
  if (options.account_id) {
    pageParams.value.account_id = options.account_id
    pageParams.value.account_name = decodeURIComponent(options.account_name || '')
  }
})

const statusOptions = [
  { value: '', label: '全部状态' },
  { value: 'pending', label: '待支付' },
  { value: 'paid', label: '已支付' },
  { value: 'failed', label: '支付失败' },
  { value: 'refunded', label: '已退款' }
]

const currentStatusText = computed(() => {
  const option = statusOptions.find(item => item.value === currentStatus.value)
  return option ? option.label : '全部状态'
})

const currentAccountText = computed(() => {
  if (currentAccount.value) {
    const option = accountOptions.value.find(item => item.value === currentAccount.value)
    return option ? option.water_meter_no : '全部账户'
  }
  return '全部账户'
})

const fetchPayments = async (page = 1, isRefresh = false) => {
  if (loading.value) return

  try {
    loading.value = true
    
    const params = {
      page,
      status: currentStatus.value || undefined,
      account_id: currentAccount.value || undefined
    }

    // 移除undefined的参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key]
      }
    })
    
    const res = await getPaymentList(params)
    
    if (res.code === 0 && res.data) {
      const newPayments = res.data.data || []
      
      if (isRefresh || page === 1) {
        payments.value = newPayments
      } else {
        payments.value = [...payments.value, ...newPayments]
      }
      
      // 检查是否还有更多数据
      noMore.value = newPayments.length < 10 || res.data.current_page >= res.data.last_page
      currentPage.value = page
    }
  } catch (error) {
    console.error('获取缴费记录失败:', error)
    uni.showToast({
      title: '获取记录失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const loadMore = () => {
  if (!noMore.value && !loading.value) {
    fetchPayments(currentPage.value + 1)
  }
}

const onRefresh = () => {
  refreshing.value = true
  noMore.value = false
  fetchPayments(1, true)
}

const showAccountFilter = () => {
  showAccountPicker.value = true
}

const selectAccount = (accountId: string) => {
  currentAccount.value = accountId
  showAccountPicker.value = false

  // 重新加载数据
  noMore.value = false
  fetchPayments(1, true)
}

const showStatusFilter = () => {
  showStatusPicker.value = true
}

const selectStatus = (status: string) => {
  currentStatus.value = status
  showStatusPicker.value = false

  // 重新加载数据
  noMore.value = false
  fetchPayments(1, true)
}

const viewDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/payment/detail?id=${id}`
  })
}

const fetchAccounts = async () => {
  try {
    const res = await getAccountList()
    if (res.code === 0 && res.data) {
      // 只显示有水表号的账户
      const validAccounts = res.data.filter(account => account.water_meter_number)

      const accounts = validAccounts.map(account => ({
        value: account.id.toString(),
        label: account.water_meter_number,
        water_meter_no: account.water_meter_number,
        address: `${account.community} ${account.building_number}`
      }))

      accountOptions.value = [
        { value: '', label: '全部账户', water_meter_no: '全部账户', address: '' },
        ...accounts
      ]

      // 如果页面传入了账户ID，设置为默认选中
      if (pageParams.value.account_id) {
        const targetAccount = accounts.find(account => account.value === pageParams.value.account_id)
        if (targetAccount) {
          currentAccount.value = pageParams.value.account_id
        }
      }
    }
  } catch (error) {
    console.error('获取账户列表失败:', error)
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const getPaymentMethodText = (method: string) => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    bank_transfer: '云闪付',
    cash: '现金',
    other: '其他'
  }
  return methodMap[method] || '未知'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN')
}

onMounted(async () => {
  // 先获取账户列表，再获取缴费记录（这样可以正确应用账户筛选）
  await fetchAccounts()
  fetchPayments()
})
</script>

<style scoped>
.history-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.filter-bar {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  gap: 16rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  flex: 1;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
}

.filter-arrow {
  font-size: 24rpx;
  color: #666;
}

.payment-list {
  flex: 1;
  padding: 20rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.payment-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.payment-meter {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.payment-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.payment-status.pending {
  background: #fff3e0;
  color: #ff9500;
}

.payment-status.paid {
  background: #e8f5e8;
  color: #34c759;
}

.payment-status.failed {
  background: #ffebee;
  color: #ff3b30;
}

.payment-status.refunded {
  background: #e3f2fd;
  color: #007aff;
}

.payment-address {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.payment-no {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.payment-time {
  font-size: 24rpx;
  color: #999;
}

.payment-right {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.payment-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.payment-method {
  font-size: 22rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.loading-more,
.no-more {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 26rpx;
}

.account-picker,
.status-picker {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

.account-options {
  padding: 0 32rpx 32rpx;
}

.account-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.account-option:last-child {
  border-bottom: none;
}

.account-option.active {
  color: #007aff;
}

.account-info {
  flex: 1;
}

.account-meter {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.account-address {
  font-size: 26rpx;
  color: #666;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 32rpx;
  color: #666;
}

.status-options {
  padding: 0 32rpx 32rpx;
}

.status-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-option:last-child {
  border-bottom: none;
}

.status-option.active {
  color: #007aff;
}

.option-text {
  font-size: 30rpx;
}

.option-check {
  font-size: 32rpx;
  color: #007aff;
}
</style>
