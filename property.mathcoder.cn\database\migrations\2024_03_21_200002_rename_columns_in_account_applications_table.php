<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('account_applications', function (Blueprint $table) {
            $table->renameColumn('id_number', 'id_card_number');
            $table->renameColumn('admin_remark', 'admin_remarks');
        });
    }

    public function down(): void
    {
        Schema::table('account_applications', function (Blueprint $table) {
            $table->renameColumn('id_card_number', 'id_number');
            $table->renameColumn('admin_remarks', 'admin_remark');
        });
    }
}; 