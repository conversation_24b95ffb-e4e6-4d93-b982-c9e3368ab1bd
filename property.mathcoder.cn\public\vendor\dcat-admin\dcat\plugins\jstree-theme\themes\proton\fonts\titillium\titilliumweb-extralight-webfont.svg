<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_webthin" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="460" d="M190 0v164h82v-164h-82zM195 438v996h71v-996h-71z" />
<glyph unicode="&#x22;" horiz-adv-x="712" d="M152 1434h79l-6 -430h-67zM481 1434h80l-6 -430h-68z" />
<glyph unicode="#" d="M61 389v66h246v463h-246v65h246v389h74v-389h385v389h74v-389h245v-65h-245v-463h245v-66h-245v-389h-74v389h-385v-389h-74v389h-246zM381 455h385v463h-385v-463z" />
<glyph unicode="$" d="M164 1030q0 340 416 340q41 0 63 -2l33 250h72l-33 -254l248 -25l-9 -67q-133 16 -249 24l-74 -569q193 -33 276.5 -102.5t83.5 -233.5q0 -219 -100 -312t-307 -93q-31 0 -49 2l-29 -234q-72 6 -72 15l29 221l-293 26l8 68q158 -20 293 -29l80 613q-209 33 -298 103.5 t-89 258.5zM236 1034q0 -156 71.5 -212t251.5 -85l76 566q-23 2 -55 2q-344 -1 -344 -271zM543 51h39q338 0 338 314q0 154 -67 209t-230 83z" />
<glyph unicode="%" d="M78 1098q0 272 211 272t211 -274q0 -141 -57.5 -212t-155 -71t-153.5 70.5t-56 214.5zM150 1098q0 -219 139 -219t139 217q0 106 -31.5 157.5t-106.5 51.5t-107.5 -51.5t-32.5 -155.5zM358 -2l379 1372l66 -18l-381 -1370zM649 266q0 273 211 273t211 -275 q0 -141 -57.5 -211.5t-154.5 -70.5t-153.5 70.5t-56.5 213.5zM721 266q0 -219 139 -219t139 217q0 106 -31.5 157.5t-106.5 51.5t-107.5 -51.5t-32.5 -155.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1392" d="M96 407.5q0 200.5 78 289.5t256 141q-66 72 -85 133t-19 155q0 134 79.5 214.5t236.5 80.5t233.5 -79.5t76.5 -222t-77.5 -225.5t-260.5 -142l383 -379q23 61 44.5 177t27.5 202h72q-23 -266 -88 -433l286 -280l-49 -53l-268 266q-135 -270 -455 -270 q-258 0 -364.5 112.5t-106.5 313zM168 412q0 -174 94 -268.5t309 -94.5q281 0 398 254l-486 481q-176 -47 -245.5 -122.5t-69.5 -249.5zM397 1126q0 -160 92 -252l74 -73q178 61 248 128.5t70 196.5q0 229 -242 229t-242 -229z" />
<glyph unicode="'" horiz-adv-x="382" d="M152 1434h81l-10 -430h-67z" />
<glyph unicode="(" horiz-adv-x="479" d="M121 616.5q0 182.5 50 411.5t99 369l49 139h72q-70 -182 -132 -465t-62 -454t49 -389t98 -351l47 -133h-72q-23 51 -55.5 139t-87.5 319.5t-55 414z" />
<glyph unicode=")" horiz-adv-x="479" d="M90 -256q70 172 132.5 437t62.5 436t-48.5 401.5t-97.5 374.5l-49 143h72q23 -57 55.5 -153.5t87.5 -340t55 -426t-49 -400.5t-98 -345l-51 -127h-72z" />
<glyph unicode="*" horiz-adv-x="854" d="M131 938l225 164l-223 164l41 55l225 -166l86 264l66 -20l-86 -266h276v-70h-280l86 -264l-66 -21l-86 266l-225 -161z" />
<glyph unicode="+" d="M121 479v70h414v434h73v-434h418v-70h-418v-438h-73v438h-414z" />
<glyph unicode="," horiz-adv-x="403" d="M90 -252l86 430h82l-104 -430h-64z" />
<glyph unicode="-" horiz-adv-x="937" d="M156 541v69h628v-69h-628z" />
<glyph unicode="." horiz-adv-x="389" d="M154 0v164h82v-164h-82z" />
<glyph unicode="/" horiz-adv-x="770" d="M68 25l567 1413l67 -29l-567 -1411z" />
<glyph unicode="0" d="M100 645q0 428 125 584q66 80 148 111.5t200.5 31.5t201.5 -31.5t147 -111.5q123 -152 122 -582q0 -381 -126 -528q-63 -74 -146.5 -105.5t-198 -31.5t-197.5 30.5t-147 102.5q-129 145 -129 530zM178 643q0 -350 107 -477q53 -63 121.5 -89t183.5 -26t196.5 46 t131 180.5t49.5 381t-45 392t-128 199t-221.5 53.5t-220.5 -54q-174 -111 -174 -606z" />
<glyph unicode="1" d="M264 1073l418 279h74v-1352h-74v1272l-383 -258z" />
<glyph unicode="2" d="M147 0v63l465 496q100 106 147.5 161.5t85.5 137.5t38 166q0 150 -85 213t-259 63q-139 0 -312 -38l-57 -15l-10 70q190 53 390 53t305.5 -80t105.5 -266q0 -137 -61.5 -238.5t-207.5 -250.5l-444 -469h756v-66h-857z" />
<glyph unicode="3" d="M135 29l12 69q215 -47 433 -47q348 2 348 316q0 168 -89 236.5t-243 76.5h-274v70h272q100 0 197.5 75.5t97.5 216t-73 201t-253 60.5t-338 -33l-51 -13l-12 70q195 45 405.5 45t305 -81t94.5 -257q0 -72 -23.5 -130t-56.5 -90t-68 -54q-59 -37 -84 -37 q133 -35 202 -108.5t69 -258.5q0 -375 -426 -374q-182 0 -383 34z" />
<glyph unicode="4" d="M82 291v61l420 1000h80l-412 -992h623v449h73v-449h201v-69h-201v-291h-73v291h-711z" />
<glyph unicode="5" d="M147 35l11 69q225 -49 405 -49t280.5 102.5t100.5 293.5q0 324 -350 323q-82 0 -173 -28.5t-142 -57.5l-52 -26l-69 8l39 682h761v-74h-692l-37 -539q70 41 176.5 73t190.5 32q426 0 426 -393q0 -231 -118 -348t-329 -117q-182 0 -366 37z" />
<glyph unicode="6" d="M123 690q0 344 133 512t371 168q152 0 299 -14l49 -6l-8 -68q-168 18 -355.5 18t-298 -144t-112.5 -423l57 25q59 23 157.5 48t178.5 25q446 0 446 -405q0 -211 -117.5 -327.5t-340.5 -116.5q-459 0 -459 708zM199 662q10 -610 383 -611q186 0 283.5 96.5t97.5 271.5 t-97.5 259t-273.5 84q-84 0 -182.5 -24.5t-155.5 -49.5z" />
<glyph unicode="7" d="M170 1282v70h823v-133l-549 -1237l-71 28l542 1225v47h-745z" />
<glyph unicode="8" d="M70 344q0 156 68.5 233.5t207.5 135.5q-127 45 -190.5 118.5t-63.5 204.5q0 336 477 336q227 0 356.5 -83t129.5 -255q0 -133 -67.5 -206.5t-211.5 -114.5q143 -41 221 -120t78 -226q0 -213 -136 -299t-363.5 -86t-366.5 85t-139 277zM150 346q-1 -295 419 -295 q201 0 314.5 69.5t113.5 244.5q0 158 -96 221q-51 35 -98 52t-125 38h-240q-143 -35 -215.5 -108.5t-72.5 -221.5zM170 1046.5q0 -133.5 64.5 -199t203.5 -104.5h240q150 35 223.5 102.5t73.5 199t-104.5 195t-300 63.5t-298 -61.5t-102.5 -195z" />
<glyph unicode="9" d="M102 924q0 205 123 325.5t328 120.5q240 0 354.5 -185.5t114.5 -538.5t-129 -508.5t-387 -155.5q-168 0 -299 16l-49 6l8 68q168 -21 340 -21q219 0 327.5 135.5t110.5 427.5q-233 -90 -414 -90q-428 0 -428 400zM180 924q0 -172 88.5 -251t266.5 -79q72 0 175 22.5 t171 47.5l65 22q-14 614 -393 614q-172 0 -272.5 -102t-100.5 -274z" />
<glyph unicode=":" horiz-adv-x="391" d="M154 0v164h82v-164h-82zM154 705v163h82v-163h-82z" />
<glyph unicode=";" horiz-adv-x="466" d="M127 -252l84 430h86l-107 -430h-63zM205 705v163h82v-163h-82z" />
<glyph unicode="&#x3c;" d="M117 477v74l868 442v-80l-801 -399l801 -399v-84z" />
<glyph unicode="=" d="M139 305v70h869v-70h-869zM139 651v70h869v-70h-869z" />
<glyph unicode="&#x3e;" d="M160 31v84l801 399l-801 399v80l868 -442v-74z" />
<glyph unicode="?" horiz-adv-x="927" d="M84 1397q229 55 340 55q203 0 306.5 -75t103.5 -249q0 -125 -38 -191.5t-139.5 -155.5t-133 -119.5t-64.5 -86t-33 -108.5v-78h-70q-2 45 -2 88t16.5 87t55.5 91q61 68 161.5 158t137.5 151.5t37 163.5q0 141 -83 198.5t-255 57.5q-88 0 -268 -43l-62 -14zM350 2v164h82 v-164h-82z" />
<glyph unicode="@" horiz-adv-x="2004" d="M123 515q0 525 221 741.5t686 216.5t664.5 -203t199.5 -645v-35q0 -367 -71 -488q-25 -45 -48.5 -68.5t-62.5 -35.5q-61 -18 -134 -18t-116 13t-65 32q-45 43 -56 84q-84 -49 -200.5 -88t-178 -39t-98.5 5t-90 33.5t-86 80t-58.5 153.5t-25.5 244q0 279 94.5 411.5 t335.5 132.5q109 0 228 -41l43 -14v37h71v-410q0 -410 29 -483q10 -29 33.5 -54.5t53.5 -30.5t81 -5t80.5 6t66.5 35t56 86q43 131 43 422v37q0 412 -180.5 596t-611.5 184t-629.5 -196.5t-198.5 -675.5q0 -254 49 -424t156.5 -269.5t257 -141.5t370.5 -42l293 20l6 -67 q-201 -18 -299 -19q-238 0 -399.5 44.5t-280.5 150.5q-229 203 -229 728zM676 492q0 -289 96 -377q45 -41 85 -54.5t98.5 -13.5t152.5 31t158 61l61 31q-23 233 -22 444v308q-150 55 -271 55q-207 0 -282.5 -118t-75.5 -367z" />
<glyph unicode="A" horiz-adv-x="1212" d="M57 0l441 1434h217l440 -1434h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115z" />
<glyph unicode="B" horiz-adv-x="1269" d="M188 0v1434h492q414 0 414 -355q0 -266 -199 -340q254 -70 254 -344q0 -221 -109.5 -308t-320.5 -87h-531zM262 70h457q170 0 262 72.5t92 252.5q0 303 -371 303h-440v-628zM262 768h440q166 0 241 76t75 229.5t-82 222t-256 68.5h-418v-596z" />
<glyph unicode="C" horiz-adv-x="1116" d="M137 692q0 164 11.5 268.5t43 203t87.5 157.5q123 131 368 131q180 0 379 -39l-6 -72q-197 41 -341 41t-224 -32.5t-126 -85t-72 -143.5t-34 -183t-8 -227.5t8 -227.5t35 -180t74 -139q104 -113 315 -113q160 0 373 41l6 -69q-205 -41 -369.5 -41t-266 48t-157 148.5 t-76 225t-20.5 288.5z" />
<glyph unicode="D" horiz-adv-x="1329" d="M188 0v1434h533q131 0 226 -57.5t149.5 -159t79 -221t24.5 -263t-25.5 -268.5t-79 -232.5t-149.5 -170t-225 -62.5h-533zM262 70h459q111 0 191.5 59t124.5 158.5t64.5 211t20.5 257t-33.5 279.5t-128 231.5t-239.5 97.5h-459v-1294z" />
<glyph unicode="E" horiz-adv-x="1130" d="M188 0v1434h838v-70h-764v-596h641v-70h-641v-628h764v-70h-838z" />
<glyph unicode="F" horiz-adv-x="1071" d="M188 0v1434h822v-70h-748v-647h643v-70h-643v-647h-74z" />
<glyph unicode="G" horiz-adv-x="1267" d="M133 711q0 178 23.5 306t81.5 230q115 205 432 205q182 0 387 -39l63 -12l-6 -72q-258 53 -422 53t-256 -45t-140 -139t-66.5 -207.5t-18.5 -278.5t19.5 -279t68.5 -204q96 -178 371 -178q152 0 377 41v549h-308v70h381v-678q-264 -51 -450 -51q-301 0 -419 182t-118 547 z" />
<glyph unicode="H" horiz-adv-x="1380" d="M188 0v1434h74v-680h856v680h74v-1434h-74v684h-856v-684h-74z" />
<glyph unicode="I" horiz-adv-x="450" d="M188 0v1434h74v-1434h-74z" />
<glyph unicode="J" horiz-adv-x="573" d="M37 -80q199 0 231 35q41 45 45 107.5t4 134.5v1237h74v-1268q0 -160 -39 -219q-23 -35 -45 -54.5t-69 -27.5q-70 -14 -201 -15v70z" />
<glyph unicode="K" horiz-adv-x="1132" d="M188 0v1434h74v-691h264l451 691h88l-473 -721l506 -713h-94l-474 674h-268v-674h-74z" />
<glyph unicode="L" horiz-adv-x="989" d="M188 0v1434h74v-1364h699v-70h-773z" />
<glyph unicode="M" horiz-adv-x="1695" d="M188 0v1434h156l504 -1321l504 1321h155v-1434h-73v1364h-29l-504 -1321h-106l-504 1321h-29v-1364h-74z" />
<glyph unicode="N" horiz-adv-x="1361" d="M188 0v1434h174l682 -1364h56v1364h74v-1434h-175l-684 1364h-53v-1364h-74z" />
<glyph unicode="O" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -566.5t-116 -554t-437 -168.5t-436 186q-61 98 -85 226t-24 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178 q-49 -90 -68.5 -205.5t-19.5 -288.5z" />
<glyph unicode="P" horiz-adv-x="1218" d="M188 0v1434h523q221 0 322.5 -107.5t101.5 -333.5q0 -467 -424 -467h-449v-526h-74zM262 596h449q346 0 346 397q0 188 -82 279.5t-264 91.5h-449v-768z" />
<glyph unicode="Q" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -560q0 -281 -54.5 -440.5t-193.5 -231.5l176 -287l-74 -35l-178 293q-96 -29 -239 -29q-2 0 -4 1q-146 0 -254.5 45t-169 142t-84 225t-23.5 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38 t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178q-49 -90 -68.5 -205.5t-19.5 -288.5z" />
<glyph unicode="R" horiz-adv-x="1255" d="M188 0v1434h514q217 0 321.5 -98.5t104.5 -311.5q0 -362 -297 -420l324 -604h-84l-319 594h-490v-594h-74zM262 664h440q349 0 349 360q0 178 -84 259t-265 81h-440v-700z" />
<glyph unicode="S" horiz-adv-x="1105" d="M115 1096q0 360 442 360q141 0 340 -24l68 -9l-9 -71q-280 34 -399 34q-364 0 -364 -284q0 -131 43 -189q25 -31 51 -52t78 -38q84 -25 213 -45q223 -35 320 -107.5t97 -254.5q0 -231 -107.5 -330.5t-326.5 -99.5q-135 0 -362 24l-76 8l8 72q283 -35 447 -35 q340 0 340 355q0 150 -78 207t-241 81.5t-231.5 44t-133 60.5t-92 111.5t-27.5 181.5z" />
<glyph unicode="T" horiz-adv-x="1077" d="M27 1364v70h1024v-70h-473v-1364h-74v1364h-477z" />
<glyph unicode="U" horiz-adv-x="1306" d="M180 401v1033h74v-1033q0 -186 100.5 -268t296 -82t299 81t103.5 269v1033h73v-1033q0 -223 -121.5 -321t-354 -98t-351.5 98t-119 321z" />
<glyph unicode="V" horiz-adv-x="1163" d="M61 1434h74l381 -1364h131l381 1364h74l-402 -1434h-237z" />
<glyph unicode="W" horiz-adv-x="1753" d="M78 1434h74l286 -1364h80l313 1343h91l313 -1343h80l287 1364h73l-301 -1434h-196l-301 1313l-302 -1313h-196z" />
<glyph unicode="X" horiz-adv-x="1128" d="M51 0l463 711l-463 723h88l426 -668l424 668h88l-456 -723l456 -711h-88l-422 659l-428 -659h-88z" />
<glyph unicode="Y" horiz-adv-x="1077" d="M35 1434h86l418 -736l417 736h86l-467 -818v-616h-73v616z" />
<glyph unicode="Z" horiz-adv-x="1103" d="M90 -2v141l846 1176v49h-846v70h922v-144l-846 -1177v-45h846v-70h-922z" />
<glyph unicode="[" horiz-adv-x="636" d="M176 -252v1786h373v-66h-301v-1654h301v-66h-373z" />
<glyph unicode="\" horiz-adv-x="808" d="M59 1407l72 29l617 -1407l-72 -29z" />
<glyph unicode="]" horiz-adv-x="636" d="M88 -186h301v1654h-301v66h373v-1786h-373v66z" />
<glyph unicode="^" d="M168 662l369 690h73l387 -690h-82l-342 616l-323 -616h-82z" />
<glyph unicode="_" horiz-adv-x="1310" d="M217 -213h877v-70h-877v70z" />
<glyph unicode="`" horiz-adv-x="440" d="M20 1436l33 63l393 -196l-30 -56z" />
<glyph unicode="a" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q223 36 384 36h9q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166z" />
<glyph unicode="b" horiz-adv-x="1052" d="M160 0v1495h71v-534q169 82 359 82q0 -1 2 -1q192 0 266 -118.5t74 -407.5t-96.5 -411.5t-370.5 -122.5q-143 0 -260 14zM231 59q160 -12 259.5 -12t178.5 26.5t119.5 92t54 146.5t13.5 216q0 242 -55.5 345.5t-216.5 103.5q-82 0 -170 -20.5t-135 -41.5l-48 -20v-836z " />
<glyph unicode="c" horiz-adv-x="878" d="M123 517q0 300 94 412.5t326 112.5l245 -20l-4 -66q-163 21 -241 21q-195 0 -269.5 -98.5t-74.5 -359.5t67.5 -366.5t276.5 -105.5l245 19l5 -66q-172 -18 -250 -18q-250 0 -335 117.5t-85 417.5z" />
<glyph unicode="d" horiz-adv-x="1054" d="M119 496q0 279 95 412.5t337 133.5q117 0 270 -24v477h72v-1495h-72v86q-59 -39 -165.5 -71.5t-173 -32.5t-102.5 5t-89 32.5t-87 79t-59.5 153.5t-25.5 244zM195 496q0 -295 94 -381q45 -41 85 -54.5t105.5 -13.5t154.5 26.5t138 53.5l49 27v798q-160 25 -270 25 q-207 0 -281.5 -117t-74.5 -364z" />
<glyph unicode="e" horiz-adv-x="1028" d="M119 512q0 530 413 530q195 0 292.5 -116.5t97.5 -374.5v-68h-727q0 -221 69.5 -328.5t231 -107.5t321.5 14l62 5l4 -66q-229 -18 -379 -18q-217 2 -301 132t-84 398zM195 549h651q0 231 -75 329.5t-239 98.5q-337 0 -337 -428z" />
<glyph unicode="f" horiz-adv-x="641" d="M59 958v66h142v125q0 215 57 289.5t203 74.5l188 -6v-63q-131 4 -188 4q-109 0 -149 -60.5t-40 -240.5v-123h332v-66h-332v-958h-71v958h-142z" />
<glyph unicode="g" horiz-adv-x="1026" d="M119 -164q0 117 78 187q29 27 96 75q-45 29 -45 133q0 29 47 127l14 33q-176 57 -176 311q0 342 350 342q106 0 189 -20l28 -6l287 6v-72h-221q82 -76 82 -255t-89 -254.5t-284 -75.5q-61 0 -110 8q-47 -117 -47.5 -147.5t4 -47t16.5 -31t47 -18.5q63 -10 239.5 -10 t256.5 -56.5t80 -230.5t-103.5 -248.5t-342.5 -74.5t-317.5 68.5t-78.5 256.5zM193 -162q0 -152 64.5 -207t262 -55t281.5 58.5t84 198.5t-61.5 181t-229.5 41l-248 6q-92 -63 -122.5 -107t-30.5 -116zM207 702.5q0 -151.5 63.5 -211t218 -59.5t219 58.5t64.5 212t-64.5 215 t-219 61.5t-218 -62.5t-63.5 -214z" />
<glyph unicode="h" horiz-adv-x="1077" d="M162 0v1495h71v-532q179 80 369 80q1 -1 2 -1q191 0 256.5 -103t65.5 -407v-532h-72v528q0 270 -49 359.5t-215 89.5q-82 0 -171 -20.5t-136 -41.5l-50 -20v-895h-71z" />
<glyph unicode="i" horiz-adv-x="395" d="M162 0v1024h71v-1024h-71zM162 1321v113h71v-113h-71z" />
<glyph unicode="j" horiz-adv-x="393" d="M-121 -383q178 96 230.5 166.5t52.5 228.5v1012h71v-1014q0 -174 -64.5 -265t-260.5 -191zM162 1321v113h71v-113h-71z" />
<glyph unicode="k" horiz-adv-x="931" d="M162 0v1495h71v-903h197l352 432h86l-376 -463l393 -561h-86l-369 526h-197v-526h-71z" />
<glyph unicode="l" horiz-adv-x="419" d="M174 0v1495h72v-1495h-72z" />
<glyph unicode="m" horiz-adv-x="1708" d="M162 0v1024h71v-82q18 10 49 27.5t120.5 45t183.5 27.5q188 0 258 -110q74 43 180 76.5t197 33.5q205 0 270 -103t65 -407v-532h-71v528q0 270 -49.5 359.5t-214.5 89.5q-88 0 -176.5 -25.5t-131.5 -50.5l-45 -24q37 -100 37 -345v-532h-71v528q0 270 -49.5 359.5 t-215.5 89.5q-82 0 -166 -25.5t-127 -50.5l-43 -27v-874h-71z" />
<glyph unicode="n" horiz-adv-x="1079" d="M162 0v1024h71v-82q182 100 373 100t256.5 -103t65.5 -407v-532h-72v528q0 270 -49 359.5t-215 89.5q-82 0 -171 -25.5t-138 -50.5l-50 -27v-874h-71z" />
<glyph unicode="o" horiz-adv-x="1077" d="M123 514q0 299 94 413.5t321.5 114.5t321.5 -114.5t94 -413.5t-84 -415.5t-331.5 -116.5t-331.5 116.5t-84 415.5zM199 530q0 -145 10 -226t46 -145.5t102.5 -88t181 -23.5t181 23.5t102.5 88t46.5 145.5t10.5 226q0 258 -72 352.5t-268.5 94.5t-268 -94.5t-71.5 -352.5z " />
<glyph unicode="p" horiz-adv-x="1056" d="M162 -471v1495h71v-86q59 39 166 71.5t189 32.5q184 0 267 -124.5t83 -401t-99.5 -405.5t-334.5 -129q-139 0 -271 28v-481h-71zM233 76q158 -29 271 -29q197 0 277.5 109.5t80.5 360.5t-68.5 355.5t-205.5 104.5q-78 0 -167 -27.5t-138 -54.5l-50 -27v-792z" />
<glyph unicode="q" horiz-adv-x="1052" d="M119 507.5q0 278.5 102.5 406.5t364.5 128l305 -18v-1495h-72v530q-149 -78 -340 -78q-1 0 -2 1q-192 0 -275 123.5t-83 402zM195 510q0 -244 64.5 -353.5t227.5 -109.5q82 0 165 20.5t126 41.5l41 20v836q-159 12 -231 12q-229 0 -311 -111.5t-82 -355.5z" />
<glyph unicode="r" horiz-adv-x="671" d="M162 0v1024h71v-160q61 49 183.5 105.5t222.5 74.5v-73q-90 -18 -191.5 -61.5t-156.5 -78.5l-58 -34v-797h-71z" />
<glyph unicode="s" horiz-adv-x="944" d="M104 751.5q0 118.5 55.5 183t129 86t153.5 21.5q166 0 312 -22l53 -8l-4 -68q-209 33 -348 33q-274 0 -275 -205q0 -96 57.5 -136t254 -70.5t274.5 -82t78 -206t-98.5 -225t-290.5 -70.5q-104 0 -279 24l-59 8l8 68q221 -35 352 -35t211 51.5t80 174t-62.5 161.5 t-256 68.5t-269.5 80t-76 169z" />
<glyph unicode="t" horiz-adv-x="700" d="M59 958v66h162v334h72v-334h373v-66h-373v-536q0 -236 33.5 -306.5t154.5 -70.5l201 16l6 -65q-135 -16 -207 -16q-145 0 -202.5 74.5t-57.5 289.5v614h-162z" />
<glyph unicode="u" horiz-adv-x="1056" d="M152 492v532h71v-528q0 -270 49.5 -359.5t214.5 -89.5q82 0 166 25.5t127 50.5l43 27v874h72v-1024h-72v82q-18 -10 -49 -27.5t-120 -45t-167 -27.5q-205 0 -270 103t-65 407z" />
<glyph unicode="v" horiz-adv-x="962" d="M63 1024h84l289 -958h90l297 958h76l-317 -1024h-201z" />
<glyph unicode="w" horiz-adv-x="1548" d="M74 1024h76l260 -958h22l303 938h78l303 -938h23l260 958h76l-277 -1024h-139l-285 918l-285 -918h-139z" />
<glyph unicode="x" horiz-adv-x="894" d="M57 0l346 512l-346 512h84l305 -457l308 457h84l-349 -512l347 -512h-84l-306 457l-305 -457h-84z" />
<glyph unicode="y" horiz-adv-x="964" d="M63 1024h76l301 -958h84l303 958h76l-469 -1495h-76l148 471h-125z" />
<glyph unicode="z" horiz-adv-x="931" d="M86 0v66l670 892h-670v66h760v-66l-668 -892h668v-66h-760z" />
<glyph unicode="{" horiz-adv-x="708" d="M59 602v66q117 29 165 86t48 159l-14 263q0 184 83 269t277 93l5 -65q-168 -8 -230.5 -79t-62.5 -218l14 -244q0 -133 -45 -195.5t-160 -99.5q115 -37 160 -100.5t45 -190.5q-14 -143 -14 -237q0 -145 63.5 -217t229.5 -80l-5 -66q-195 8 -277.5 93t-82.5 270l14 251 q0 98 -48 153.5t-165 88.5z" />
<glyph unicode="|" horiz-adv-x="419" d="M174 -471v1966h72v-1966h-72z" />
<glyph unicode="}" horiz-adv-x="708" d="M86 -188q166 8 229.5 80t63.5 217q0 94 -14 237q0 127 45 190.5t159 100.5q-115 37 -159.5 99.5t-44.5 195.5l14 244q0 147 -62.5 217.5t-230.5 79.5l4 65q195 -8 278 -93t83 -269l-15 -263q0 -102 48 -159.5t165 -85.5v-66q-117 -33 -165 -88t-48 -154l15 -251 q0 -184 -83 -269.5t-278 -93.5z" />
<glyph unicode="~" d="M145 543q127 111 230 110q61 0 211.5 -74.5t187.5 -74.5t89 26.5t87 53.5l37 26l23 -63q-128 -113 -230 -113q-61 0 -211.5 75t-187.5 75t-89 -27t-87 -53l-37 -27z" />
<glyph unicode="&#xa1;" horiz-adv-x="417" d="M166 860v164h82v-164h-82zM172 -410v996h72v-996h-72z" />
<glyph unicode="&#xa2;" d="M223 499.5q0 235.5 84 337t273 107.5v266h71v-268q82 0 199 -18l39 -6l-4 -68q-164 20 -283 20q-164 0 -230.5 -82.5t-66.5 -284.5t64.5 -273.5t253.5 -71.5q106 0 225 12l41 4l4 -72q-152 -16 -240 -18v-258h-71v258q-197 6 -278 93t-81 322.5z" />
<glyph unicode="&#xa3;" d="M170 0v70h188v696h-141v65h141v107q0 258 60.5 345t220.5 87q70 0 201 -18l45 -6v-66q-178 25 -246 25q-127 0 -168 -74t-41 -301v-99h373v-65h-373v-696h356l168 32l15 -65l-168 -37h-631z" />
<glyph unicode="&#xa4;" d="M158 154l145 143q-78 94 -78 219t78 219l-145 144l53 53l143 -146q94 78 219 78t220 -78l143 146l53 -53l-145 -144q78 -94 78 -219t-78 -219l145 -143l-53 -54l-143 146q-95 -78 -219.5 -78t-219.5 78l-143 -146zM299 516q0 -113 81 -193.5t193.5 -80.5t193.5 80.5 t81 193.5t-81 194t-193.5 81t-193.5 -81t-81 -194z" />
<glyph unicode="&#xa5;" d="M72 1352h84l419 -633l414 633h86l-391 -604h344v-70h-389l-27 -41v-195h418v-69h-418v-373h-73v373h-420v69h420v195l-27 41h-395v70h350z" />
<glyph unicode="&#xa6;" horiz-adv-x="428" d="M178 299h72v-770h-72v770zM178 725v770h72v-770h-72z" />
<glyph unicode="&#xa7;" horiz-adv-x="997" d="M127 580q0 61 27.5 122.5t54.5 92.5l29 30q-90 68 -91 224q0 303 359 303q129 0 268 -21l51 -6l-4 -65q-217 26 -315 26q-287 0 -287 -225q0 -133 66.5 -179t239.5 -73t255 -86t82 -201q0 -59 -19.5 -123.5t-39.5 -99.5l-21 -37q74 -57 74 -203.5t-91 -228.5t-263 -82 q-90 0 -285 21l-59 6l6 65q225 -29 338 -28q283 0 282 258q0 117 -64.5 154.5t-242.5 62t-264 87t-86 206.5zM199 580.5q0 -107.5 71.5 -157.5t233.5 -73.5t225 -54.5q61 106 61.5 222t-66.5 160t-222.5 68.5t-212.5 51.5q-90 -109 -90 -216.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="440" d="M0 1307v127h72v-127h-72zM389 1307v127h72v-127h-72z" />
<glyph unicode="&#xa9;" horiz-adv-x="1325" d="M139 889q0 227 149.5 386t373 159t374 -159t150.5 -386t-150.5 -385t-374 -158t-373 158t-149.5 385zM205 889q0 -201 130 -339t326.5 -138t327.5 138t131 339t-131 340t-327.5 139t-326.5 -139t-130 -340zM438 887.5q0 154.5 47 234.5t177 80q80 0 131 -10l20 -4l-2 -66 q-84 14 -149 15q-100 0 -129 -57.5t-29 -188.5t32.5 -190.5t125.5 -59.5l149 16l2 -65q-88 -17 -151 -17q-127 0 -175.5 79t-48.5 233.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="749" d="M127 954q0 143 154 156l208 14v47q0 63 -25.5 90t-80.5 27l-213 -14l-4 59q141 16 223 16.5t123 -42t41 -138.5v-274q23 -35 76 -47l-4 -57q-66 2 -111 45q-129 -45 -244 -45q-63 0 -103 45t-40 118zM195 950q0 -96 81.5 -96t180.5 27l32 10v174l-198 -12 q-51 -2 -73.5 -27t-22.5 -76z" />
<glyph unicode="&#xab;" horiz-adv-x="1017" d="M98 479v74l342 264v-80l-276 -217l276 -239v-84zM522 479v74l342 264v-80l-276 -217l276 -239v-84z" />
<glyph unicode="&#xac;" d="M145 610v70h865v-422h-74v352h-791z" />
<glyph unicode="&#xad;" horiz-adv-x="937" d="M156 541v69h628v-69h-628z" />
<glyph unicode="&#xae;" horiz-adv-x="1325" d="M139 889q0 227 149.5 386t373 159t374 -159t150.5 -386t-150.5 -385t-374 -158t-373 158t-149.5 385zM205 889q0 -201 130 -339t326.5 -138t327.5 138t131 339t-131 340t-327.5 139t-326.5 -139t-130 -340zM453 586v604h190q121 0 176 -41t55 -141q0 -147 -120 -179 l133 -243h-72l-135 237h-162v-237h-65zM518 887h164q70 0 98.5 26.5t28.5 94t-39 93t-125 25.5h-127v-239z" />
<glyph unicode="&#xaf;" horiz-adv-x="440" d="M-41 1315v65h547v-65h-547z" />
<glyph unicode="&#xb0;" d="M295 1173.5q0 124.5 77 201.5t201.5 77t201.5 -77t77 -201.5t-77 -200.5t-201.5 -76t-201.5 76t-77 200.5zM365 1173.5q0 -96.5 57 -153.5t152.5 -57t153.5 58t58 153.5t-59 153.5t-153.5 58t-151.5 -58t-57 -154.5z" />
<glyph unicode="&#xb1;" d="M121 102v70h905v-70h-905zM121 631v69h414v283h73v-283h418v-69h-418v-295h-73v295h-414z" />
<glyph unicode="&#xb2;" horiz-adv-x="573" d="M74 979v63l200 220q68 72 89.5 117.5t21.5 92t-33 73t-73 26.5q-72 0 -164 -15l-31 -6l-4 60q104 27 190 26q186 0 187 -174q0 -68 -30 -120t-101 -125l-164 -170h309v-68h-397z" />
<glyph unicode="&#xb3;" horiz-adv-x="573" d="M76 983l6 64q111 -18 194.5 -18.5t115.5 32.5t32 88q0 127 -123 127h-133v67h133q37 0 71 39t34 91q0 96 -119 96q-94 0 -168 -13l-29 -6l-6 64q109 23 205 22.5t142 -39t46 -107.5t-22 -108q-37 -59 -72 -65q59 -16 86 -50t27 -116q0 -190 -201 -190q-98 0 -186 16z" />
<glyph unicode="&#xb4;" horiz-adv-x="548" d="M82 1303l393 196l33 -63l-395 -189z" />
<glyph unicode="&#xb5;" d="M201 -471v1495h71v-528q0 -270 49.5 -359.5t215.5 -89.5q82 0 165.5 25.5t126.5 50.5l43 27v874h72v-1024h-72v82q-18 -10 -48.5 -27.5t-120 -45t-166.5 -27.5q-197 0 -265 92v-545h-71z" />
<glyph unicode="&#xb6;" horiz-adv-x="1206" d="M82 1073q0 158 109.5 259.5t271.5 101.5h672v-68h-199v-1366h-74v1366h-311v-1366h-74v717h-14q-162 0 -271.5 99t-109.5 257z" />
<glyph unicode="&#xb7;" horiz-adv-x="391" d="M156 553v164h82v-164h-82z" />
<glyph unicode="&#xb8;" horiz-adv-x="538" d="M102 -403l4 57q90 -8 160 -8q98 0 99 94q0 86 -99 86h-110v176h67v-119q115 0 164 -30.5t49 -112.5q0 -152 -168 -152z" />
<glyph unicode="&#xb9;" horiz-adv-x="573" d="M94 1481l203 137h72v-639h-72v559l-168 -115z" />
<glyph unicode="&#xba;" horiz-adv-x="722" d="M126 1070.5q0 279.5 236 279.5q119 0 175.5 -66.5t56.5 -210.5q0 -283 -232 -282q-236 0 -236 279.5zM197 1072q0 -116 38.5 -166t127 -50t125 50t36.5 167t-36.5 164t-125 47t-127 -48t-38.5 -164z" />
<glyph unicode="&#xbb;" horiz-adv-x="1017" d="M154 197v84l276 239l-276 217v80l342 -264v-74zM578 197v84l276 239l-276 217v80l342 -264v-74z" />
<glyph unicode="&#xbc;" horiz-adv-x="1075" d="M80 57l803 1293l61 -39l-803 -1291zM100 1481l203 137h72v-639h-72v559l-168 -115zM549 -25l182 459h78l-178 -454h217v180h74v-180h75v-68h-75v-117h-74v117h-299v63z" />
<glyph unicode="&#xbd;" horiz-adv-x="1064" d="M74 57l803 1293l61 -39l-803 -1291zM115 1481l202 137h72v-639h-72v559l-167 -115zM563 -141l201 219q68 72 89 118t21 92t-32.5 72.5t-73.5 26.5q-72 0 -164 -14l-31 -6l-4 59q104 27 191 27q186 0 186 -174q0 -68 -29.5 -120.5t-101.5 -125.5l-164 -170h310v-68h-398 v64z" />
<glyph unicode="&#xbe;" horiz-adv-x="1112" d="M119 57l803 1293l61 -39l-803 -1291zM119 983l6 64q111 -18 194.5 -18.5t115.5 32.5t32 88q0 127 -123 127h-133v67h133q37 0 71 39t34 91q0 96 -119 96q-94 0 -168 -13l-29 -6l-6 64q109 23 205 22.5t142 -39t46 -107.5t-22 -108q-31 -51 -72 -65q59 -16 86 -51t27 -115 q0 -190 -201 -190q-98 0 -186 16zM588 -25l182 459h78l-178 -454h217v180h74v-180h75v-68h-75v-117h-74v117h-299v63z" />
<glyph unicode="&#xbf;" horiz-adv-x="915" d="M88 -102q0 125 38 191.5t139.5 155.5t133 119.5t64.5 86t33 108.5v78h69q2 -45 2 -88t-16 -87t-55 -89q-61 -70 -162 -160t-137.5 -151.5t-36.5 -163.5q0 -141 83 -198.5t255 -57.5q88 0 268 43l61 14l11 -70q-229 -55 -340 -55q-203 0 -306.5 75t-103.5 249zM489 860 v164h82v-164h-82z" />
<glyph unicode="&#xc0;" horiz-adv-x="1212" d="M57 0l441 1434h217l440 -1434h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115zM362 1825l33 63l393 -196l-30 -56z" />
<glyph unicode="&#xc1;" horiz-adv-x="1212" d="M57 0l441 1434h217l440 -1434h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115zM414 1692l393 196l33 -63l-396 -189z" />
<glyph unicode="&#xc2;" horiz-adv-x="1212" d="M57 0l441 1434h217l440 -1434h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115zM299 1657l274 256h62l274 -256h-92l-213 196l-211 -196h-94z" />
<glyph unicode="&#xc3;" horiz-adv-x="1212" d="M57 0l441 1434h217l440 -1434h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115zM289 1782q90 111 157 110q47 0 171 -60t149 -60q37 0 104 73l23 25l22 -58q-80 -106 -135 -106t-179 61.5t-148 61.5q-45 0 -119 -80l-25 -27z" />
<glyph unicode="&#xc4;" horiz-adv-x="1212" d="M57 0l441 1434h217l440 -1434h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115zM369 1716v127h71v-127h-71zM770 1716v127h72v-127h-72z" />
<glyph unicode="&#xc5;" horiz-adv-x="1212" d="M57 0l428 1395q-82 49 -82 148t54.5 149.5t144.5 50.5t144.5 -50.5t54.5 -145.5t-76 -148l430 -1399h-74l-133 440h-684l-133 -440h-74zM287 510h639l-262 854h-115zM471 1554q0 -98 82 -120h96q84 23 84 120q0 61 -34.5 94t-96 33t-96.5 -33t-35 -94z" />
<glyph unicode="&#xc6;" horiz-adv-x="1800" d="M51 0l514 1434h1131v-70h-764v-590h641v-69h-641v-635h764v-70h-838v440h-565l-160 -440h-82zM317 510h541v854h-235z" />
<glyph unicode="&#xc7;" horiz-adv-x="1116" d="M137 713.5q0 142.5 11.5 247t43 203t87.5 157.5q123 131 368 131q180 0 379 -39l-6 -72q-197 41 -341 41t-224 -32.5t-126 -85t-72 -143.5t-34 -183t-8 -227.5t8 -227.5t35 -180t74 -139q104 -113 315 -113q160 0 373 41l6 -69q-184 -39 -356 -41v-99q115 0 164 -30.5 t49 -112.5q0 -152 -168 -152l-166 9l4 57q90 -8 160 -8q98 0 98 94q0 86 -98 86h-111v158q-221 12 -330 137q-98 115 -125 346q-10 104 -10 246.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1130" d="M188 0v1434h838v-70h-764v-596h641v-70h-641v-628h764v-70h-838zM387 1825l33 63l393 -196l-31 -56z" />
<glyph unicode="&#xc9;" horiz-adv-x="1130" d="M188 0v1434h838v-70h-764v-596h641v-70h-641v-628h764v-70h-838zM393 1692l393 196l33 -63l-395 -189z" />
<glyph unicode="&#xca;" horiz-adv-x="1130" d="M188 0v1434h838v-70h-764v-596h641v-70h-641v-628h764v-70h-838zM291 1657l274 256h62l274 -256h-92l-213 196l-211 -196h-94z" />
<glyph unicode="&#xcb;" horiz-adv-x="1130" d="M188 0v1434h838v-70h-764v-596h641v-70h-641v-628h764v-70h-838zM369 1716v127h71v-127h-71zM770 1716v127h72v-127h-72z" />
<glyph unicode="&#xcc;" horiz-adv-x="450" d="M-37 1825l33 63l393 -196l-31 -56zM188 0v1434h74v-1434h-74z" />
<glyph unicode="&#xcd;" horiz-adv-x="450" d="M35 1692l393 196l33 -63l-395 -189zM188 0v1434h74v-1434h-74z" />
<glyph unicode="&#xce;" horiz-adv-x="450" d="M-92 1657l274 256h62l274 -256h-92l-213 196l-211 -196h-94zM188 0v1434h74v-1434h-74z" />
<glyph unicode="&#xcf;" horiz-adv-x="450" d="M-12 1716v127h71v-127h-71zM188 0v1434h74v-1434h-74zM389 1716v127h72v-127h-72z" />
<glyph unicode="&#xd0;" horiz-adv-x="1333" d="M35 684v70h158v680h532q131 0 226.5 -57.5t150.5 -157.5q102 -190 102 -482q0 -289 -104 -501q-53 -109 -149.5 -172.5t-225.5 -63.5h-532v684h-158zM266 70h459q111 0 191.5 59t124.5 160.5t64.5 213t20.5 234.5t-20.5 228.5t-64.5 198.5t-124.5 146.5t-191.5 53.5h-459 v-610h363v-70h-363v-614z" />
<glyph unicode="&#xd1;" horiz-adv-x="1361" d="M188 0v1434h174l682 -1364h56v1364h74v-1434h-175l-684 1364h-53v-1364h-74zM367 1782q90 111 157 110q47 0 171 -60t149 -60q37 0 104 73l23 25l22 -58q-80 -106 -135 -106t-179 61.5t-149 61.5q-45 0 -118 -80l-25 -27z" />
<glyph unicode="&#xd2;" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -566.5t-116 -554t-437 -168.5t-436 186q-61 98 -85 226t-24 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178 q-49 -90 -68.5 -205.5t-19.5 -288.5zM463 1825l33 63l393 -196l-31 -56z" />
<glyph unicode="&#xd3;" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -566.5t-116 -554t-437 -168.5t-436 186q-61 98 -85 226t-24 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178 q-49 -90 -68.5 -205.5t-19.5 -288.5zM449 1692l393 196l32 -63l-395 -189z" />
<glyph unicode="&#xd4;" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -566.5t-116 -554t-437 -168.5t-436 186q-61 98 -85 226t-24 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178 q-49 -90 -68.5 -205.5t-19.5 -288.5zM377 1657l274 256h62l274 -256h-92l-213 196l-211 -196h-94z" />
<glyph unicode="&#xd5;" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -566.5t-116 -554t-437 -168.5t-436 186q-61 98 -85 226t-24 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178 q-49 -90 -68.5 -205.5t-19.5 -288.5zM360 1782q90 111 158 110q47 0 171 -60t147 -60q43 0 106 73l23 25l22 -58q-80 -106 -135 -106t-179 61.5t-149 61.5q-45 0 -118 -80l-25 -27z" />
<glyph unicode="&#xd6;" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182t430 -181t119 -566.5t-116 -554t-437 -168.5t-436 186q-61 98 -85 226t-24 317zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t240.5 -41t239.5 38t143.5 125t68.5 201.5t18.5 295.5q0 344 -99.5 507.5t-371.5 163.5q-285 0 -383 -178 q-49 -90 -68.5 -205.5t-19.5 -288.5zM446 1716v127h72v-127h-72zM848 1716v127h72v-127h-72z" />
<glyph unicode="&#xd7;" d="M166 156l360 358l-360 358l49 50l358 -361l359 361l49 -50l-360 -358l360 -358l-49 -50l-359 361l-358 -361z" />
<glyph unicode="&#xd8;" horiz-adv-x="1363" d="M133 711q0 377 119 559t430 182q176 0 291 -59l108 229l62 -25l-113 -241q111 -84 156 -243t45 -402q0 -391 -116 -560t-433 -169q-164 0 -274 47l-119 -252l-62 22l121 260q-121 80 -168 238t-47 414zM211 681q0 -202 37 -341t133 -211l561 1196q-102 57 -252.5 57 t-245 -43t-144.5 -134t-69.5 -206.5t-19.5 -317.5zM438 94q94 -43 242.5 -43t242 38t143.5 125t68.5 201.5t18.5 295.5q0 434 -158 573z" />
<glyph unicode="&#xd9;" horiz-adv-x="1306" d="M180 401v1033h74v-1033q0 -186 100.5 -268t296 -82t299 81t103.5 269v1033h73v-1033q0 -223 -121.5 -321t-354 -98t-351.5 98t-119 321zM430 1825l33 63l393 -196l-31 -56z" />
<glyph unicode="&#xda;" horiz-adv-x="1306" d="M180 401v1033h74v-1033q0 -186 100.5 -268t296 -82t299 81t103.5 269v1033h73v-1033q0 -223 -121.5 -321t-354 -98t-351.5 98t-119 321zM440 1692l394 196l32 -63l-395 -189z" />
<glyph unicode="&#xdb;" horiz-adv-x="1306" d="M180 401v1033h74v-1033q0 -186 100.5 -268t296 -82t299 81t103.5 269v1033h73v-1033q0 -223 -121.5 -321t-354 -98t-351.5 98t-119 321zM346 1657l275 256h61l274 -256h-92l-213 196l-211 -196h-94z" />
<glyph unicode="&#xdc;" horiz-adv-x="1306" d="M180 401v1033h74v-1033q0 -186 100.5 -268t296 -82t299 81t103.5 269v1033h73v-1033q0 -223 -121.5 -321t-354 -98t-351.5 98t-119 321zM418 1716v127h71v-127h-71zM819 1716v127h72v-127h-72z" />
<glyph unicode="&#xdd;" horiz-adv-x="1077" d="M35 1434h86l418 -736l417 736h86l-467 -818v-616h-73v616zM389 1692l393 196l33 -63l-395 -189z" />
<glyph unicode="&#xde;" horiz-adv-x="1230" d="M188 0v1434h74v-250h449q221 0 322.5 -104.5t101.5 -327.5q0 -476 -424 -476h-449v-276h-74zM262 346h449q346 0 346 406q0 188 -81 275t-265 87h-449v-768z" />
<glyph unicode="&#xdf;" horiz-adv-x="1124" d="M162 0v1149q0 205 81 284.5t273.5 79.5t269 -65.5t76.5 -212.5q0 -106 -33.5 -167.5t-112.5 -97.5t-102.5 -54.5t-23.5 -51.5t34.5 -60.5t165 -95t188.5 -136t58 -191.5q0 -211 -94 -305t-315 -94q-72 0 -195 14l-41 6l4 68q158 -18 232 -19q184 0 257.5 77t73.5 243 q0 109 -50 166t-184 125.5t-172 105.5t-38 94.5t36 89t110.5 66.5t99 82t24.5 149.5t-59 148.5t-212 46t-216.5 -64.5t-63.5 -257.5v-1122h-71z" />
<glyph unicode="&#xe0;" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q229 37 393 36q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166zM272 1436l33 63l393 -196l-30 -56z" />
<glyph unicode="&#xe1;" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q229 37 393 36q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166zM270 1303l394 196l32 -63l-395 -189z" />
<glyph unicode="&#xe2;" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q229 37 393 36q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166zM211 1229l248 270h57l250 -270h-88l-189 205l-190 -205h-88z" />
<glyph unicode="&#xe3;" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q229 37 393 36q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166zM188 1368q100 96 168 96q43 0 157 -54t138 -54q41 0 111 61l22 23l19 -62q-86 -88 -139.5 -88t-167 55.5t-138.5 55.5q-45 0 -122 -70l-27 -22z" />
<glyph unicode="&#xe4;" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q229 37 393 36q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166zM270 1307v127h72v-127h-72zM659 1307v127h72v-127h-72z" />
<glyph unicode="&#xe5;" horiz-adv-x="1013" d="M98 273.5q0 140.5 67.5 214t215.5 87.5l389 41v107q0 131 -56.5 190.5t-170.5 59.5q-117 0 -324 -27l-63 -8l-6 68q229 37 393 36q299 0 299 -319v-590q8 -74 153 -86l-4 -63q-150 0 -211 86q-209 -88 -413 -88q-127 0 -198 75.5t-71 216zM174 278.5q0 -110.5 50 -171 t140.5 -60.5t190.5 21.5t158 42.5l57 20v420l-381 -39q-117 -12 -166 -67.5t-49 -166zM313 1331q0 82 57.5 138.5t139.5 56.5t137 -56.5t55 -138.5t-55 -138t-138 -56t-139.5 56t-56.5 138zM383 1331q0 -53 37 -92t89 -39t89 39t37 92t-37 91t-89 38t-89 -38t-37 -91z" />
<glyph unicode="&#xe6;" horiz-adv-x="1675" d="M98 281q0 164 80 220t299 85l293 30v107q0 131 -56.5 190.5t-170.5 59.5q-104 0 -322 -15l-63 -4l-4 68q225 20 389 20q236 0 284 -196q100 197 353 196q195 0 292 -116.5t97 -374.5v-68h-727q0 -221 69.5 -328.5t231 -107.5t321.5 14l62 5l4 -66q-229 -18 -379 -18 q-201 0 -293 118l-78 -32q-190 -86 -422 -86q-260 0 -260 299zM174 279.5q0 -111.5 49 -172t136 -60.5t200 25q158 35 268 80q-61 121 -61 397l-377 -37q-113 -10 -164 -65.5t-51 -167zM842 549h651q0 231 -75 329.5t-238 98.5q-338 0 -338 -428z" />
<glyph unicode="&#xe7;" horiz-adv-x="878" d="M123 534.5q0 282.5 94 395t326 112.5l245 -20l-4 -66q-164 20 -241 21q-195 0 -269.5 -98.5t-74.5 -359.5t67.5 -366.5t276.5 -105.5l245 19l5 -66q-172 -18 -225.5 -18t-67.5 2v-101q115 0 164 -30.5t49 -112.5q0 -152 -168 -152l-166 9l4 57q90 -8 160 -8q98 0 98 94 q0 86 -98 86h-111v162q-182 23 -245.5 143.5t-63.5 403z" />
<glyph unicode="&#xe8;" horiz-adv-x="1028" d="M119 512q0 530 413 530q195 0 292.5 -116.5t97.5 -374.5v-68h-727q0 -221 69.5 -328.5t231 -107.5t321.5 14l62 5l4 -66q-229 -18 -379 -18q-217 2 -301 132t-84 398zM195 549h651q0 231 -75 329.5t-239 98.5q-337 0 -337 -428zM262 1438l33 63l393 -196l-31 -56z" />
<glyph unicode="&#xe9;" horiz-adv-x="1028" d="M119 512q0 530 413 530q195 0 292.5 -116.5t97.5 -374.5v-68h-727q0 -221 69.5 -328.5t231 -107.5t321.5 14l62 5l4 -66q-229 -18 -379 -18q-217 2 -301 132t-84 398zM195 549h651q0 231 -75 329.5t-239 98.5q-337 0 -337 -428zM350 1303l393 196l33 -63l-395 -189z" />
<glyph unicode="&#xea;" horiz-adv-x="1028" d="M119 512q0 530 413 530q195 0 292.5 -116.5t97.5 -374.5v-68h-727q0 -221 69.5 -328.5t231 -107.5t321.5 14l62 5l4 -66q-229 -18 -379 -18q-217 2 -301 132t-84 398zM195 549h651q0 231 -75 329.5t-239 98.5q-337 0 -337 -428zM262 1229l248 270h57l250 -270h-88 l-188 205l-191 -205h-88z" />
<glyph unicode="&#xeb;" horiz-adv-x="1028" d="M119 512q0 530 413 530q195 0 292.5 -116.5t97.5 -374.5v-68h-727q0 -221 69.5 -328.5t231 -107.5t321.5 14l62 5l4 -66q-229 -18 -379 -18q-217 2 -301 132t-84 398zM195 549h651q0 231 -75 329.5t-239 98.5q-337 0 -337 -428zM295 1307v127h72v-127h-72zM684 1307v127 h72v-127h-72z" />
<glyph unicode="&#xec;" horiz-adv-x="395" d="M-123 1436l33 63l393 -196l-31 -56zM162 0v1024h71v-1024h-71z" />
<glyph unicode="&#xed;" horiz-adv-x="395" d="M88 1303l393 196l33 -63l-395 -189zM162 0v1024h71v-1024h-71z" />
<glyph unicode="&#xee;" horiz-adv-x="395" d="M-80 1229l248 270h57l250 -270h-88l-188 205l-191 -205h-88zM162 0v1024h71v-1024h-71z" />
<glyph unicode="&#xef;" horiz-adv-x="395" d="M-39 1307v127h72v-127h-72zM162 0v1024h71v-1024h-71zM350 1307v127h72v-127h-72z" />
<glyph unicode="&#xf0;" horiz-adv-x="1095" d="M92 427q0 206 112.5 324.5t305.5 118.5q72 0 168 -22.5t158 -44.5l61 -23q-10 319 -342 523l-244 -164l-41 55l217 147q-123 63 -272 109l27 63q178 -53 311 -129l209 142l41 -56l-182 -124q352 -227 352 -629t-101.5 -568.5t-339.5 -166.5q-207 0 -323.5 119.5 t-116.5 325.5zM166 426q0 -174 95 -274.5t284.5 -100.5t270.5 141.5t83 516.5q-238 92 -392.5 92t-247.5 -100.5t-93 -274.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1079" d="M162 0v1024h71v-82l50 25q51 25 141 50t168 25q205 0 270.5 -103t65.5 -407v-532h-72v528q0 270 -49 359.5t-215 89.5q-82 0 -189.5 -33t-169.5 -70v-874h-71zM256 1368q100 96 168 96q43 0 156.5 -54t136.5 -54q43 0 110 61l25 23l18 -62q-86 -88 -139 -88t-166.5 55.5 t-138.5 55.5q-45 0 -123 -70l-27 -22z" />
<glyph unicode="&#xf2;" horiz-adv-x="1077" d="M123 514q0 299 94 413.5t321.5 114.5t321.5 -114.5t94 -413.5t-84 -415.5t-331.5 -116.5t-331.5 116.5t-84 415.5zM199 530q0 -145 10 -226t46 -145.5t102.5 -88t181 -23.5t181 23.5t102.5 88t46.5 145.5t10.5 226q0 258 -72 352.5t-268.5 94.5t-268 -94.5t-71.5 -352.5z M315 1436l33 63l393 -196l-30 -56z" />
<glyph unicode="&#xf3;" horiz-adv-x="1077" d="M123 514q0 299 94 413.5t321.5 114.5t321.5 -114.5t94 -413.5t-84 -415.5t-331.5 -116.5t-331.5 116.5t-84 415.5zM199 530q0 -145 10 -226t46 -145.5t102.5 -88t181 -23.5t181 23.5t102.5 88t46.5 145.5t10.5 226q0 258 -72 352.5t-268.5 94.5t-268 -94.5t-71.5 -352.5z M367 1303l393 196l33 -63l-396 -189z" />
<glyph unicode="&#xf4;" horiz-adv-x="1077" d="M123 514q0 299 94 413.5t321.5 114.5t321.5 -114.5t94 -413.5t-84 -415.5t-331.5 -116.5t-331.5 116.5t-84 415.5zM199 530q0 -145 10 -226t46 -145.5t102.5 -88t181 -23.5t181 23.5t102.5 88t46.5 145.5t10.5 226q0 258 -72 352.5t-268.5 94.5t-268 -94.5t-71.5 -352.5z M262 1229l248 270h57l250 -270h-88l-188 205l-191 -205h-88z" />
<glyph unicode="&#xf5;" horiz-adv-x="1077" d="M123 514q0 299 94 413.5t321.5 114.5t321.5 -114.5t94 -413.5t-84 -415.5t-331.5 -116.5t-331.5 116.5t-84 415.5zM199 530q0 -145 10 -226t46 -145.5t102.5 -88t181 -23.5t181 23.5t102.5 88t46.5 145.5t10.5 226q0 258 -72 352.5t-268.5 94.5t-268 -94.5t-71.5 -352.5z M233 1368q100 96 168 96q43 0 157 -54t138 -54q41 0 111 61l22 23l19 -62q-86 -88 -139.5 -88t-167 55.5t-138.5 55.5q-45 0 -122 -70l-27 -22z" />
<glyph unicode="&#xf6;" horiz-adv-x="1077" d="M123 514q0 299 94 413.5t321.5 114.5t321.5 -114.5t94 -413.5t-84 -415.5t-331.5 -116.5t-331.5 116.5t-84 415.5zM199 530q0 -145 10 -226t46 -145.5t102.5 -88t181 -23.5t181 23.5t102.5 88t46.5 145.5t10.5 226q0 258 -72 352.5t-268.5 94.5t-268 -94.5t-71.5 -352.5z M299 1307v127h72v-127h-72zM688 1307v127h72v-127h-72z" />
<glyph unicode="&#xf7;" d="M119 479v70h909v-70h-909zM535 80v164h77v-164h-77zM535 784v164h77v-164h-77z" />
<glyph unicode="&#xf8;" horiz-adv-x="1077" d="M123 557q0 256 94.5 370.5t321.5 114.5q109 0 176 -22l90 219l61 -22l-92 -220q94 -51 137 -162.5t43 -304.5q0 -315 -84 -431.5t-331 -116.5q-100 0 -172 18l-95 -233l-61 20l94 231q-104 47 -143 165t-39 374zM199 557q0 -231 26.5 -332.5t108.5 -142.5l356 876 q-61 19 -151 19q-197 0 -268.5 -94.5t-71.5 -325.5zM391 61q55 -14 158.5 -14t170 23.5t102.5 88t46.5 145.5t10.5 239.5t-29 253t-100 137.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1056" d="M152 492v532h71v-528q0 -270 49.5 -359.5t214.5 -89.5q82 0 166 25.5t127 50.5l43 27v874h72v-1024h-72v82q-18 -10 -49 -27.5t-120 -45t-167 -27.5q-205 0 -270 103t-65 407zM256 1436l33 63l393 -196l-31 -56z" />
<glyph unicode="&#xfa;" horiz-adv-x="1056" d="M152 492v532h71v-528q0 -270 49.5 -359.5t214.5 -89.5q82 0 185.5 34t150.5 69v874h72v-1024h-72v82l-45 -25q-45 -27 -129 -51t-162 -24q-205 0 -270 103t-65 407zM352 1303l393 196l33 -63l-395 -189z" />
<glyph unicode="&#xfb;" horiz-adv-x="1056" d="M152 492v532h71v-528q0 -270 49.5 -359.5t214.5 -89.5q82 0 185.5 34t150.5 69v874h72v-1024h-72v82l-45 -25q-45 -27 -129 -51t-162 -24q-205 0 -270 103t-65 407zM248 1229l248 270h57l250 -270h-88l-189 205l-190 -205h-88z" />
<glyph unicode="&#xfc;" horiz-adv-x="1056" d="M152 492v532h71v-528q0 -270 49.5 -359.5t214.5 -89.5q82 0 185.5 34t150.5 69v874h72v-1024h-72v82l-45 -25q-45 -27 -129 -51t-162 -24q-205 0 -270 103t-65 407zM305 1307v127h72v-127h-72zM694 1307v127h72v-127h-72z" />
<glyph unicode="&#xfd;" horiz-adv-x="964" d="M63 1024h76l301 -958h84l303 958h76l-469 -1495h-76l148 471h-125zM358 1303l394 196l32 -63l-395 -189z" />
<glyph unicode="&#xfe;" horiz-adv-x="1054" d="M162 -471v1966h71v-534q170 82 362.5 81.5t265.5 -116t73 -404.5t-96.5 -414.5t-370.5 -125.5l-234 12v-465h-71zM233 59q160 -12 259.5 -12t178.5 27.5t119.5 93t54 147.5t13.5 215q0 244 -54 345.5t-218 101.5q-82 0 -170 -20.5t-135 -41.5l-48 -20v-836z" />
<glyph unicode="&#xff;" horiz-adv-x="964" d="M63 1024h76l301 -958h84l303 958h76l-469 -1495h-76l148 471h-125zM248 1307v127h71v-127h-71zM637 1307v127h72v-127h-72z" />
<glyph unicode="&#x152;" horiz-adv-x="1925" d="M133 706.5q0 186.5 24.5 316.5t84.5 230q119 199 440 199q207 0 322 -18h817v-74h-744v-586h621v-74h-621v-626h744v-76h-815q-172 -16 -330 -16t-266.5 45t-169 142t-84 224t-23.5 313.5zM211 710q0 -173 18.5 -286t68.5 -202t144.5 -130t220 -41t341.5 19v1288 q-287 25 -322 24q-285 0 -383 -178q-49 -90 -68.5 -205.5t-19.5 -288.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1787" d="M123 518q0 299 95 411.5t325 112.5q299 0 377 -245q90 246 372 245q195 0 292 -116.5t97 -374.5v-68h-727q0 -221 70 -328.5t231.5 -107.5t321.5 14l61 5l4 -66q-229 -18 -378 -18q-266 0 -346 219q-37 -115 -125 -167t-250 -52q-250 0 -335 118.5t-85 417.5zM199 535 q0 -143 11 -226.5t48 -148t104.5 -89t179 -24.5t180.5 27.5t102.5 94.5t44 146.5t10.5 214.5q0 242 -81 344.5t-267.5 102.5t-259 -93t-72.5 -349zM954 549h652q0 231 -75 329.5t-239 98.5q-338 0 -338 -428z" />
<glyph unicode="&#x178;" horiz-adv-x="1077" d="M35 1434h86l418 -736l417 736h86l-467 -818v-616h-73v616zM301 1716v127h72v-127h-72zM702 1716v127h72v-127h-72z" />
<glyph unicode="&#x2c6;" horiz-adv-x="440" d="M-31 1229l248 270h57l250 -270h-88l-188 205l-191 -205h-88z" />
<glyph unicode="&#x2dc;" horiz-adv-x="440" d="M-72 1368q100 96 168 96q43 0 157 -54t136 -54q43 0 111 61l24 23l19 -62q-86 -88 -139.5 -88t-167 55.5t-138.5 55.5q-45 0 -123 -70l-26 -22z" />
<glyph unicode="&#x2000;" horiz-adv-x="956" />
<glyph unicode="&#x2001;" horiz-adv-x="1913" />
<glyph unicode="&#x2002;" horiz-adv-x="956" />
<glyph unicode="&#x2003;" horiz-adv-x="1913" />
<glyph unicode="&#x2004;" horiz-adv-x="637" />
<glyph unicode="&#x2005;" horiz-adv-x="478" />
<glyph unicode="&#x2006;" horiz-adv-x="318" />
<glyph unicode="&#x2007;" horiz-adv-x="318" />
<glyph unicode="&#x2008;" horiz-adv-x="239" />
<glyph unicode="&#x2009;" horiz-adv-x="382" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="937" d="M156 541v69h628v-69h-628z" />
<glyph unicode="&#x2011;" horiz-adv-x="937" d="M156 541v69h628v-69h-628z" />
<glyph unicode="&#x2012;" horiz-adv-x="937" d="M156 541v69h628v-69h-628z" />
<glyph unicode="&#x2013;" horiz-adv-x="1316" d="M147 516v70h1024v-70h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2340" d="M147 516v70h2048v-70h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="395" d="M127 1079l100 410h60l-82 -410h-78z" />
<glyph unicode="&#x2019;" horiz-adv-x="391" d="M129 1079l82 410h78l-101 -410h-59z" />
<glyph unicode="&#x201a;" horiz-adv-x="393" d="M88 -174l100 410h60l-82 -410h-78z" />
<glyph unicode="&#x201c;" horiz-adv-x="667" d="M127 1079l100 410h60l-82 -410h-78zM399 1079l101 410h59l-82 -410h-78z" />
<glyph unicode="&#x201d;" horiz-adv-x="686" d="M129 1079l82 410h78l-101 -410h-59zM424 1079l82 410h78l-101 -410h-59z" />
<glyph unicode="&#x201e;" horiz-adv-x="649" d="M84 -186l100 409h60l-82 -409h-78zM346 -186l100 409h60l-82 -409h-78z" />
<glyph unicode="&#x2022;" horiz-adv-x="999" d="M244 225v594h512v-594h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1429" d="M154 0v164h82v-164h-82zM674 0v164h82v-164h-82zM1194 0v164h82v-164h-82z" />
<glyph unicode="&#x202f;" horiz-adv-x="382" />
<glyph unicode="&#x2039;" horiz-adv-x="593" d="M98 479v74l342 264v-80l-276 -217l276 -239v-84z" />
<glyph unicode="&#x203a;" horiz-adv-x="593" d="M154 197v84l276 239l-276 217v80l342 -264v-74z" />
<glyph unicode="&#x205f;" horiz-adv-x="478" />
<glyph unicode="&#x20ac;" d="M66 502v69h129v101q0 90 2 133h-131v69h135q23 246 128 371t347 125q182 0 358 -37l-6 -67q-172 39 -366.5 39t-282.5 -107.5t-105 -323.5h656v-69h-660q-2 -43 -2 -123t2 -111h660v-69h-658q16 -229 103.5 -341t300.5 -112q152 0 352 37l6 -66q-193 -37 -358 -36 q-244 0 -350.5 128t-126.5 390h-133z" />
<glyph unicode="&#x2122;" horiz-adv-x="1411" d="M203 1208v64h409v-64h-170v-528h-67v528h-172zM684 678v594h102l172 -496l179 496h102v-594h-65v528l-181 -506h-71l-172 506v-528h-66z" />
<glyph unicode="&#xe000;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#xf0;" k="33" />
<hkern u1="&#x22;" u2="&#xec;" k="-20" />
<hkern u1="&#x22;" u2="&#xc6;" k="129" />
<hkern u1="&#x22;" u2="&#x40;" k="29" />
<hkern u1="&#x22;" u2="&#x2f;" k="127" />
<hkern u1="&#x22;" u2="&#x26;" k="68" />
<hkern u1="&#x26;" u2="&#x201d;" k="117" />
<hkern u1="&#x26;" u2="&#x2019;" k="117" />
<hkern u1="&#x26;" u2="&#x178;" k="123" />
<hkern u1="&#x26;" u2="&#x152;" k="23" />
<hkern u1="&#x26;" u2="&#xff;" k="43" />
<hkern u1="&#x26;" u2="&#xfd;" k="43" />
<hkern u1="&#x26;" u2="&#xdd;" k="123" />
<hkern u1="&#x26;" u2="&#xdc;" k="20" />
<hkern u1="&#x26;" u2="&#xdb;" k="20" />
<hkern u1="&#x26;" u2="&#xda;" k="20" />
<hkern u1="&#x26;" u2="&#xd9;" k="20" />
<hkern u1="&#x26;" u2="&#xd8;" k="23" />
<hkern u1="&#x26;" u2="&#xd6;" k="23" />
<hkern u1="&#x26;" u2="&#xd5;" k="23" />
<hkern u1="&#x26;" u2="&#xd4;" k="23" />
<hkern u1="&#x26;" u2="&#xd3;" k="23" />
<hkern u1="&#x26;" u2="&#xd2;" k="23" />
<hkern u1="&#x26;" u2="&#xc7;" k="23" />
<hkern u1="&#x26;" u2="y" k="43" />
<hkern u1="&#x26;" u2="w" k="35" />
<hkern u1="&#x26;" u2="v" k="43" />
<hkern u1="&#x26;" u2="t" k="25" />
<hkern u1="&#x26;" u2="Y" k="123" />
<hkern u1="&#x26;" u2="W" k="51" />
<hkern u1="&#x26;" u2="V" k="82" />
<hkern u1="&#x26;" u2="U" k="20" />
<hkern u1="&#x26;" u2="T" k="96" />
<hkern u1="&#x26;" u2="Q" k="23" />
<hkern u1="&#x26;" u2="O" k="23" />
<hkern u1="&#x26;" u2="G" k="23" />
<hkern u1="&#x26;" u2="C" k="23" />
<hkern u1="&#x26;" u2="&#x27;" k="117" />
<hkern u1="&#x26;" u2="&#x22;" k="117" />
<hkern u1="&#x27;" u2="&#xf0;" k="33" />
<hkern u1="&#x27;" u2="&#xec;" k="-20" />
<hkern u1="&#x27;" u2="&#xc6;" k="129" />
<hkern u1="&#x27;" u2="&#x40;" k="29" />
<hkern u1="&#x27;" u2="&#x2f;" k="127" />
<hkern u1="&#x27;" u2="&#x26;" k="68" />
<hkern u1="&#x28;" u2="&#x153;" k="35" />
<hkern u1="&#x28;" u2="&#x152;" k="23" />
<hkern u1="&#x28;" u2="&#xfc;" k="27" />
<hkern u1="&#x28;" u2="&#xfb;" k="27" />
<hkern u1="&#x28;" u2="&#xfa;" k="27" />
<hkern u1="&#x28;" u2="&#xf9;" k="27" />
<hkern u1="&#x28;" u2="&#xf8;" k="35" />
<hkern u1="&#x28;" u2="&#xf6;" k="35" />
<hkern u1="&#x28;" u2="&#xf5;" k="35" />
<hkern u1="&#x28;" u2="&#xf4;" k="35" />
<hkern u1="&#x28;" u2="&#xf3;" k="35" />
<hkern u1="&#x28;" u2="&#xf2;" k="35" />
<hkern u1="&#x28;" u2="&#xec;" k="-49" />
<hkern u1="&#x28;" u2="&#xeb;" k="35" />
<hkern u1="&#x28;" u2="&#xea;" k="35" />
<hkern u1="&#x28;" u2="&#xe9;" k="35" />
<hkern u1="&#x28;" u2="&#xe8;" k="35" />
<hkern u1="&#x28;" u2="&#xe7;" k="35" />
<hkern u1="&#x28;" u2="&#xd8;" k="23" />
<hkern u1="&#x28;" u2="&#xd6;" k="23" />
<hkern u1="&#x28;" u2="&#xd5;" k="23" />
<hkern u1="&#x28;" u2="&#xd4;" k="23" />
<hkern u1="&#x28;" u2="&#xd3;" k="23" />
<hkern u1="&#x28;" u2="&#xd2;" k="23" />
<hkern u1="&#x28;" u2="&#xc7;" k="20" />
<hkern u1="&#x28;" u2="&#x7b;" k="20" />
<hkern u1="&#x28;" u2="u" k="27" />
<hkern u1="&#x28;" u2="q" k="35" />
<hkern u1="&#x28;" u2="o" k="35" />
<hkern u1="&#x28;" u2="e" k="35" />
<hkern u1="&#x28;" u2="d" k="35" />
<hkern u1="&#x28;" u2="c" k="35" />
<hkern u1="&#x28;" u2="Q" k="23" />
<hkern u1="&#x28;" u2="O" k="23" />
<hkern u1="&#x28;" u2="G" k="23" />
<hkern u1="&#x28;" u2="C" k="20" />
<hkern u1="&#x29;" u2="&#x7d;" k="29" />
<hkern u1="&#x29;" u2="]" k="29" />
<hkern u1="&#x2a;" u2="&#x153;" k="35" />
<hkern u1="&#x2a;" u2="&#xf8;" k="35" />
<hkern u1="&#x2a;" u2="&#xf6;" k="35" />
<hkern u1="&#x2a;" u2="&#xf5;" k="35" />
<hkern u1="&#x2a;" u2="&#xf4;" k="35" />
<hkern u1="&#x2a;" u2="&#xf3;" k="35" />
<hkern u1="&#x2a;" u2="&#xf2;" k="35" />
<hkern u1="&#x2a;" u2="&#xf0;" k="39" />
<hkern u1="&#x2a;" u2="&#xeb;" k="35" />
<hkern u1="&#x2a;" u2="&#xea;" k="35" />
<hkern u1="&#x2a;" u2="&#xe9;" k="35" />
<hkern u1="&#x2a;" u2="&#xe8;" k="35" />
<hkern u1="&#x2a;" u2="&#xe7;" k="35" />
<hkern u1="&#x2a;" u2="&#xc6;" k="115" />
<hkern u1="&#x2a;" u2="&#xc5;" k="92" />
<hkern u1="&#x2a;" u2="&#xc4;" k="92" />
<hkern u1="&#x2a;" u2="&#xc3;" k="92" />
<hkern u1="&#x2a;" u2="&#xc2;" k="92" />
<hkern u1="&#x2a;" u2="&#xc1;" k="92" />
<hkern u1="&#x2a;" u2="&#xc0;" k="92" />
<hkern u1="&#x2a;" u2="s" k="23" />
<hkern u1="&#x2a;" u2="q" k="43" />
<hkern u1="&#x2a;" u2="o" k="35" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="35" />
<hkern u1="&#x2a;" u2="d" k="43" />
<hkern u1="&#x2a;" u2="c" k="35" />
<hkern u1="&#x2a;" u2="Z" k="39" />
<hkern u1="&#x2a;" u2="J" k="59" />
<hkern u1="&#x2a;" u2="A" k="92" />
<hkern u1="&#x2c;" u2="v" k="88" />
<hkern u1="&#x2c;" u2="f" k="18" />
<hkern u1="&#x2c;" u2="V" k="125" />
<hkern u1="&#x2d;" u2="&#xc6;" k="47" />
<hkern u1="&#x2d;" u2="x" k="74" />
<hkern u1="&#x2d;" u2="v" k="31" />
<hkern u1="&#x2d;" u2="f" k="25" />
<hkern u1="&#x2d;" u2="X" k="88" />
<hkern u1="&#x2d;" u2="V" k="72" />
<hkern u1="&#x2e;" u2="v" k="88" />
<hkern u1="&#x2e;" u2="f" k="18" />
<hkern u1="&#x2e;" u2="V" k="125" />
<hkern u1="&#x2f;" u2="&#x153;" k="63" />
<hkern u1="&#x2f;" u2="&#xfc;" k="33" />
<hkern u1="&#x2f;" u2="&#xfb;" k="33" />
<hkern u1="&#x2f;" u2="&#xfa;" k="33" />
<hkern u1="&#x2f;" u2="&#xf9;" k="33" />
<hkern u1="&#x2f;" u2="&#xf8;" k="63" />
<hkern u1="&#x2f;" u2="&#xf6;" k="63" />
<hkern u1="&#x2f;" u2="&#xf5;" k="63" />
<hkern u1="&#x2f;" u2="&#xf4;" k="63" />
<hkern u1="&#x2f;" u2="&#xf3;" k="63" />
<hkern u1="&#x2f;" u2="&#xf2;" k="63" />
<hkern u1="&#x2f;" u2="&#xf1;" k="37" />
<hkern u1="&#x2f;" u2="&#xf0;" k="39" />
<hkern u1="&#x2f;" u2="&#xef;" k="-25" />
<hkern u1="&#x2f;" u2="&#xec;" k="-82" />
<hkern u1="&#x2f;" u2="&#xeb;" k="63" />
<hkern u1="&#x2f;" u2="&#xea;" k="63" />
<hkern u1="&#x2f;" u2="&#xe9;" k="63" />
<hkern u1="&#x2f;" u2="&#xe8;" k="63" />
<hkern u1="&#x2f;" u2="&#xe7;" k="63" />
<hkern u1="&#x2f;" u2="&#xe6;" k="35" />
<hkern u1="&#x2f;" u2="&#xe5;" k="35" />
<hkern u1="&#x2f;" u2="&#xe4;" k="35" />
<hkern u1="&#x2f;" u2="&#xe3;" k="35" />
<hkern u1="&#x2f;" u2="&#xe2;" k="35" />
<hkern u1="&#x2f;" u2="&#xe1;" k="35" />
<hkern u1="&#x2f;" u2="&#xe0;" k="35" />
<hkern u1="&#x2f;" u2="&#xc6;" k="117" />
<hkern u1="&#x2f;" u2="&#xc5;" k="96" />
<hkern u1="&#x2f;" u2="&#xc4;" k="96" />
<hkern u1="&#x2f;" u2="&#xc3;" k="96" />
<hkern u1="&#x2f;" u2="&#xc2;" k="96" />
<hkern u1="&#x2f;" u2="&#xc1;" k="96" />
<hkern u1="&#x2f;" u2="&#xc0;" k="96" />
<hkern u1="&#x2f;" u2="u" k="33" />
<hkern u1="&#x2f;" u2="s" k="47" />
<hkern u1="&#x2f;" u2="r" k="37" />
<hkern u1="&#x2f;" u2="q" k="68" />
<hkern u1="&#x2f;" u2="p" k="37" />
<hkern u1="&#x2f;" u2="o" k="63" />
<hkern u1="&#x2f;" u2="n" k="37" />
<hkern u1="&#x2f;" u2="m" k="37" />
<hkern u1="&#x2f;" u2="g" k="61" />
<hkern u1="&#x2f;" u2="e" k="63" />
<hkern u1="&#x2f;" u2="d" k="68" />
<hkern u1="&#x2f;" u2="c" k="63" />
<hkern u1="&#x2f;" u2="a" k="35" />
<hkern u1="&#x2f;" u2="J" k="47" />
<hkern u1="&#x2f;" u2="A" k="96" />
<hkern u1="&#x2f;" u2="&#x2f;" k="551" />
<hkern u1="&#x3a;" u2="V" k="33" />
<hkern u1="&#x3b;" u2="V" k="33" />
<hkern u1="&#x40;" u2="&#x178;" k="66" />
<hkern u1="&#x40;" u2="&#xdd;" k="66" />
<hkern u1="&#x40;" u2="&#xc6;" k="20" />
<hkern u1="&#x40;" u2="Y" k="66" />
<hkern u1="&#x40;" u2="V" k="27" />
<hkern u1="&#x40;" u2="T" k="53" />
<hkern u1="&#x40;" u2="J" k="27" />
<hkern u1="A" u2="&#x2122;" k="98" />
<hkern u1="A" u2="&#xf0;" k="10" />
<hkern u1="A" u2="&#xae;" k="63" />
<hkern u1="A" u2="&#x7d;" k="68" />
<hkern u1="A" u2="v" k="41" />
<hkern u1="A" u2="f" k="14" />
<hkern u1="A" u2="]" k="78" />
<hkern u1="A" u2="\" k="102" />
<hkern u1="A" u2="V" k="59" />
<hkern u1="A" u2="&#x3f;" k="66" />
<hkern u1="A" u2="&#x2a;" k="86" />
<hkern u1="B" u2="&#x178;" k="55" />
<hkern u1="B" u2="&#xff;" k="12" />
<hkern u1="B" u2="&#xfd;" k="12" />
<hkern u1="B" u2="&#xdd;" k="55" />
<hkern u1="B" u2="&#xc6;" k="29" />
<hkern u1="B" u2="&#xc5;" k="23" />
<hkern u1="B" u2="&#xc4;" k="23" />
<hkern u1="B" u2="&#xc3;" k="23" />
<hkern u1="B" u2="&#xc2;" k="23" />
<hkern u1="B" u2="&#xc1;" k="23" />
<hkern u1="B" u2="&#xc0;" k="23" />
<hkern u1="B" u2="&#x7d;" k="68" />
<hkern u1="B" u2="y" k="12" />
<hkern u1="B" u2="x" k="16" />
<hkern u1="B" u2="w" k="12" />
<hkern u1="B" u2="v" k="12" />
<hkern u1="B" u2="g" k="20" />
<hkern u1="B" u2="]" k="72" />
<hkern u1="B" u2="Y" k="55" />
<hkern u1="B" u2="X" k="29" />
<hkern u1="B" u2="W" k="10" />
<hkern u1="B" u2="V" k="27" />
<hkern u1="B" u2="T" k="55" />
<hkern u1="B" u2="J" k="55" />
<hkern u1="B" u2="A" k="23" />
<hkern u1="B" u2="&#x3f;" k="25" />
<hkern u1="C" u2="&#xf0;" k="16" />
<hkern u1="C" u2="&#xec;" k="-51" />
<hkern u1="C" u2="&#xae;" k="43" />
<hkern u1="C" u2="v" k="47" />
<hkern u1="C" u2="f" k="12" />
<hkern u1="D" u2="&#xc6;" k="41" />
<hkern u1="D" u2="&#x7d;" k="72" />
<hkern u1="D" u2="x" k="10" />
<hkern u1="D" u2="]" k="74" />
<hkern u1="D" u2="X" k="47" />
<hkern u1="D" u2="V" k="27" />
<hkern u1="D" u2="&#x3f;" k="31" />
<hkern u1="D" u2="&#x29;" k="23" />
<hkern u1="E" u2="&#xf0;" k="18" />
<hkern u1="E" u2="&#xec;" k="-66" />
<hkern u1="E" u2="v" k="39" />
<hkern u1="E" u2="f" k="10" />
<hkern u1="F" u2="&#x2026;" k="176" />
<hkern u1="F" u2="&#x201e;" k="176" />
<hkern u1="F" u2="&#x201a;" k="176" />
<hkern u1="F" u2="&#x153;" k="37" />
<hkern u1="F" u2="&#x152;" k="35" />
<hkern u1="F" u2="&#xff;" k="39" />
<hkern u1="F" u2="&#xfd;" k="39" />
<hkern u1="F" u2="&#xfc;" k="43" />
<hkern u1="F" u2="&#xfb;" k="43" />
<hkern u1="F" u2="&#xfa;" k="43" />
<hkern u1="F" u2="&#xf9;" k="43" />
<hkern u1="F" u2="&#xf8;" k="37" />
<hkern u1="F" u2="&#xf6;" k="37" />
<hkern u1="F" u2="&#xf5;" k="37" />
<hkern u1="F" u2="&#xf4;" k="37" />
<hkern u1="F" u2="&#xf3;" k="37" />
<hkern u1="F" u2="&#xf2;" k="37" />
<hkern u1="F" u2="&#xf1;" k="49" />
<hkern u1="F" u2="&#xf0;" k="53" />
<hkern u1="F" u2="&#xef;" k="-29" />
<hkern u1="F" u2="&#xec;" k="-109" />
<hkern u1="F" u2="&#xeb;" k="37" />
<hkern u1="F" u2="&#xea;" k="37" />
<hkern u1="F" u2="&#xe9;" k="37" />
<hkern u1="F" u2="&#xe8;" k="37" />
<hkern u1="F" u2="&#xe7;" k="37" />
<hkern u1="F" u2="&#xe6;" k="90" />
<hkern u1="F" u2="&#xe5;" k="90" />
<hkern u1="F" u2="&#xe4;" k="90" />
<hkern u1="F" u2="&#xe3;" k="90" />
<hkern u1="F" u2="&#xe2;" k="90" />
<hkern u1="F" u2="&#xe1;" k="90" />
<hkern u1="F" u2="&#xe0;" k="90" />
<hkern u1="F" u2="&#xd8;" k="35" />
<hkern u1="F" u2="&#xd6;" k="35" />
<hkern u1="F" u2="&#xd5;" k="35" />
<hkern u1="F" u2="&#xd4;" k="35" />
<hkern u1="F" u2="&#xd3;" k="35" />
<hkern u1="F" u2="&#xd2;" k="35" />
<hkern u1="F" u2="&#xc7;" k="33" />
<hkern u1="F" u2="&#xc6;" k="119" />
<hkern u1="F" u2="&#xc5;" k="86" />
<hkern u1="F" u2="&#xc4;" k="86" />
<hkern u1="F" u2="&#xc3;" k="86" />
<hkern u1="F" u2="&#xc2;" k="86" />
<hkern u1="F" u2="&#xc1;" k="86" />
<hkern u1="F" u2="&#xc0;" k="86" />
<hkern u1="F" u2="z" k="53" />
<hkern u1="F" u2="y" k="39" />
<hkern u1="F" u2="x" k="68" />
<hkern u1="F" u2="w" k="39" />
<hkern u1="F" u2="v" k="39" />
<hkern u1="F" u2="u" k="43" />
<hkern u1="F" u2="t" k="25" />
<hkern u1="F" u2="s" k="37" />
<hkern u1="F" u2="r" k="49" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="49" />
<hkern u1="F" u2="o" k="37" />
<hkern u1="F" u2="n" k="49" />
<hkern u1="F" u2="m" k="49" />
<hkern u1="F" u2="g" k="43" />
<hkern u1="F" u2="f" k="16" />
<hkern u1="F" u2="e" k="37" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="c" k="37" />
<hkern u1="F" u2="a" k="90" />
<hkern u1="F" u2="X" k="12" />
<hkern u1="F" u2="S" k="35" />
<hkern u1="F" u2="Q" k="35" />
<hkern u1="F" u2="O" k="35" />
<hkern u1="F" u2="J" k="61" />
<hkern u1="F" u2="G" k="35" />
<hkern u1="F" u2="C" k="33" />
<hkern u1="F" u2="A" k="86" />
<hkern u1="F" u2="&#x2f;" k="55" />
<hkern u1="F" u2="&#x2e;" k="176" />
<hkern u1="F" u2="&#x2c;" k="176" />
<hkern u1="G" u2="&#xec;" k="-16" />
<hkern u1="G" u2="v" k="18" />
<hkern u1="G" u2="f" k="12" />
<hkern u1="G" u2="V" k="20" />
<hkern u1="K" u2="&#xf0;" k="10" />
<hkern u1="K" u2="&#xef;" k="-23" />
<hkern u1="K" u2="&#xec;" k="-102" />
<hkern u1="K" u2="&#xae;" k="39" />
<hkern u1="K" u2="v" k="59" />
<hkern u1="L" u2="&#x2122;" k="203" />
<hkern u1="L" u2="&#xf0;" k="16" />
<hkern u1="L" u2="&#xb7;" k="190" />
<hkern u1="L" u2="&#xae;" k="201" />
<hkern u1="L" u2="&#x7d;" k="37" />
<hkern u1="L" u2="v" k="111" />
<hkern u1="L" u2="]" k="49" />
<hkern u1="L" u2="\" k="143" />
<hkern u1="L" u2="V" k="125" />
<hkern u1="L" u2="&#x3f;" k="45" />
<hkern u1="L" u2="&#x2a;" k="203" />
<hkern u1="O" u2="&#xc6;" k="35" />
<hkern u1="O" u2="&#x7d;" k="68" />
<hkern u1="O" u2="]" k="70" />
<hkern u1="O" u2="X" k="43" />
<hkern u1="O" u2="V" k="27" />
<hkern u1="O" u2="&#x3f;" k="25" />
<hkern u1="P" u2="&#x2039;" k="31" />
<hkern u1="P" u2="&#x2026;" k="193" />
<hkern u1="P" u2="&#x201e;" k="193" />
<hkern u1="P" u2="&#x201a;" k="193" />
<hkern u1="P" u2="&#x2014;" k="31" />
<hkern u1="P" u2="&#x2013;" k="31" />
<hkern u1="P" u2="&#x178;" k="41" />
<hkern u1="P" u2="&#x153;" k="14" />
<hkern u1="P" u2="&#xf8;" k="14" />
<hkern u1="P" u2="&#xf6;" k="14" />
<hkern u1="P" u2="&#xf5;" k="14" />
<hkern u1="P" u2="&#xf4;" k="14" />
<hkern u1="P" u2="&#xf3;" k="14" />
<hkern u1="P" u2="&#xf2;" k="14" />
<hkern u1="P" u2="&#xf0;" k="41" />
<hkern u1="P" u2="&#xeb;" k="14" />
<hkern u1="P" u2="&#xea;" k="14" />
<hkern u1="P" u2="&#xe9;" k="14" />
<hkern u1="P" u2="&#xe8;" k="14" />
<hkern u1="P" u2="&#xe7;" k="14" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xe5;" k="10" />
<hkern u1="P" u2="&#xe4;" k="10" />
<hkern u1="P" u2="&#xe3;" k="10" />
<hkern u1="P" u2="&#xe2;" k="10" />
<hkern u1="P" u2="&#xe1;" k="10" />
<hkern u1="P" u2="&#xe0;" k="10" />
<hkern u1="P" u2="&#xdd;" k="41" />
<hkern u1="P" u2="&#xc6;" k="90" />
<hkern u1="P" u2="&#xc5;" k="80" />
<hkern u1="P" u2="&#xc4;" k="80" />
<hkern u1="P" u2="&#xc3;" k="80" />
<hkern u1="P" u2="&#xc2;" k="80" />
<hkern u1="P" u2="&#xc1;" k="80" />
<hkern u1="P" u2="&#xc0;" k="80" />
<hkern u1="P" u2="&#xab;" k="31" />
<hkern u1="P" u2="&#x7d;" k="59" />
<hkern u1="P" u2="q" k="16" />
<hkern u1="P" u2="o" k="14" />
<hkern u1="P" u2="g" k="12" />
<hkern u1="P" u2="e" k="14" />
<hkern u1="P" u2="d" k="16" />
<hkern u1="P" u2="c" k="14" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="]" k="59" />
<hkern u1="P" u2="Z" k="23" />
<hkern u1="P" u2="Y" k="41" />
<hkern u1="P" u2="X" k="35" />
<hkern u1="P" u2="V" k="12" />
<hkern u1="P" u2="J" k="76" />
<hkern u1="P" u2="A" k="80" />
<hkern u1="P" u2="&#x2f;" k="72" />
<hkern u1="P" u2="&#x2e;" k="193" />
<hkern u1="P" u2="&#x2d;" k="31" />
<hkern u1="P" u2="&#x2c;" k="193" />
<hkern u1="Q" u2="&#xc6;" k="35" />
<hkern u1="Q" u2="&#x7d;" k="68" />
<hkern u1="Q" u2="]" k="70" />
<hkern u1="Q" u2="X" k="43" />
<hkern u1="Q" u2="V" k="27" />
<hkern u1="Q" u2="&#x3f;" k="25" />
<hkern u1="R" u2="&#xf0;" k="25" />
<hkern u1="R" u2="&#xc6;" k="12" />
<hkern u1="R" u2="&#x7d;" k="51" />
<hkern u1="R" u2="]" k="59" />
<hkern u1="R" u2="V" k="23" />
<hkern u1="S" u2="&#xec;" k="-29" />
<hkern u1="S" u2="&#xc6;" k="27" />
<hkern u1="S" u2="x" k="20" />
<hkern u1="S" u2="v" k="27" />
<hkern u1="S" u2="f" k="14" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="20" />
<hkern u1="T" u2="&#xf0;" k="94" />
<hkern u1="T" u2="&#xef;" k="-61" />
<hkern u1="T" u2="&#xec;" k="-143" />
<hkern u1="T" u2="&#xe4;" k="205" />
<hkern u1="T" u2="&#xe3;" k="166" />
<hkern u1="T" u2="&#xdf;" k="14" />
<hkern u1="T" u2="&#xc6;" k="117" />
<hkern u1="T" u2="&#xae;" k="29" />
<hkern u1="T" u2="x" k="178" />
<hkern u1="T" u2="v" k="162" />
<hkern u1="T" u2="f" k="29" />
<hkern u1="T" u2="&#x40;" k="68" />
<hkern u1="T" u2="&#x2f;" k="109" />
<hkern u1="T" u2="&#x26;" k="57" />
<hkern u1="U" u2="&#xc6;" k="23" />
<hkern u1="V" u2="&#x203a;" k="51" />
<hkern u1="V" u2="&#x2039;" k="63" />
<hkern u1="V" u2="&#x2026;" k="125" />
<hkern u1="V" u2="&#x201e;" k="125" />
<hkern u1="V" u2="&#x201a;" k="125" />
<hkern u1="V" u2="&#x2014;" k="72" />
<hkern u1="V" u2="&#x2013;" k="72" />
<hkern u1="V" u2="&#x153;" k="70" />
<hkern u1="V" u2="&#x152;" k="27" />
<hkern u1="V" u2="&#xff;" k="18" />
<hkern u1="V" u2="&#xfd;" k="18" />
<hkern u1="V" u2="&#xfc;" k="55" />
<hkern u1="V" u2="&#xfb;" k="55" />
<hkern u1="V" u2="&#xfa;" k="55" />
<hkern u1="V" u2="&#xf9;" k="55" />
<hkern u1="V" u2="&#xf8;" k="70" />
<hkern u1="V" u2="&#xf6;" k="70" />
<hkern u1="V" u2="&#xf5;" k="70" />
<hkern u1="V" u2="&#xf4;" k="70" />
<hkern u1="V" u2="&#xf3;" k="70" />
<hkern u1="V" u2="&#xf2;" k="70" />
<hkern u1="V" u2="&#xf1;" k="61" />
<hkern u1="V" u2="&#xf0;" k="70" />
<hkern u1="V" u2="&#xef;" k="-27" />
<hkern u1="V" u2="&#xec;" k="-109" />
<hkern u1="V" u2="&#xeb;" k="70" />
<hkern u1="V" u2="&#xea;" k="70" />
<hkern u1="V" u2="&#xe9;" k="70" />
<hkern u1="V" u2="&#xe8;" k="70" />
<hkern u1="V" u2="&#xe7;" k="70" />
<hkern u1="V" u2="&#xe6;" k="59" />
<hkern u1="V" u2="&#xe5;" k="59" />
<hkern u1="V" u2="&#xe4;" k="59" />
<hkern u1="V" u2="&#xe3;" k="59" />
<hkern u1="V" u2="&#xe2;" k="59" />
<hkern u1="V" u2="&#xe1;" k="59" />
<hkern u1="V" u2="&#xe0;" k="59" />
<hkern u1="V" u2="&#xd8;" k="27" />
<hkern u1="V" u2="&#xd6;" k="27" />
<hkern u1="V" u2="&#xd5;" k="27" />
<hkern u1="V" u2="&#xd4;" k="27" />
<hkern u1="V" u2="&#xd3;" k="27" />
<hkern u1="V" u2="&#xd2;" k="27" />
<hkern u1="V" u2="&#xc7;" k="25" />
<hkern u1="V" u2="&#xc6;" k="68" />
<hkern u1="V" u2="&#xc5;" k="59" />
<hkern u1="V" u2="&#xc4;" k="59" />
<hkern u1="V" u2="&#xc3;" k="59" />
<hkern u1="V" u2="&#xc2;" k="59" />
<hkern u1="V" u2="&#xc1;" k="59" />
<hkern u1="V" u2="&#xc0;" k="59" />
<hkern u1="V" u2="&#xbb;" k="51" />
<hkern u1="V" u2="&#xae;" k="25" />
<hkern u1="V" u2="&#xab;" k="63" />
<hkern u1="V" u2="z" k="25" />
<hkern u1="V" u2="y" k="18" />
<hkern u1="V" u2="x" k="16" />
<hkern u1="V" u2="w" k="20" />
<hkern u1="V" u2="v" k="18" />
<hkern u1="V" u2="u" k="55" />
<hkern u1="V" u2="s" k="55" />
<hkern u1="V" u2="r" k="61" />
<hkern u1="V" u2="q" k="72" />
<hkern u1="V" u2="p" k="61" />
<hkern u1="V" u2="o" k="70" />
<hkern u1="V" u2="n" k="61" />
<hkern u1="V" u2="m" k="61" />
<hkern u1="V" u2="g" k="86" />
<hkern u1="V" u2="e" k="70" />
<hkern u1="V" u2="d" k="72" />
<hkern u1="V" u2="c" k="70" />
<hkern u1="V" u2="a" k="59" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="27" />
<hkern u1="V" u2="O" k="27" />
<hkern u1="V" u2="J" k="76" />
<hkern u1="V" u2="G" k="27" />
<hkern u1="V" u2="C" k="25" />
<hkern u1="V" u2="A" k="59" />
<hkern u1="V" u2="&#x40;" k="33" />
<hkern u1="V" u2="&#x3b;" k="33" />
<hkern u1="V" u2="&#x3a;" k="33" />
<hkern u1="V" u2="&#x2f;" k="86" />
<hkern u1="V" u2="&#x2e;" k="125" />
<hkern u1="V" u2="&#x2d;" k="72" />
<hkern u1="V" u2="&#x2c;" k="125" />
<hkern u1="V" u2="&#x26;" k="45" />
<hkern u1="W" u2="&#xf0;" k="45" />
<hkern u1="W" u2="&#xec;" k="-90" />
<hkern u1="W" u2="&#xc6;" k="61" />
<hkern u1="W" u2="&#x2f;" k="53" />
<hkern u1="W" u2="&#x26;" k="20" />
<hkern u1="X" u2="&#x2039;" k="53" />
<hkern u1="X" u2="&#x2014;" k="86" />
<hkern u1="X" u2="&#x2013;" k="86" />
<hkern u1="X" u2="&#x153;" k="35" />
<hkern u1="X" u2="&#x152;" k="43" />
<hkern u1="X" u2="&#xff;" k="59" />
<hkern u1="X" u2="&#xfd;" k="59" />
<hkern u1="X" u2="&#xfc;" k="29" />
<hkern u1="X" u2="&#xfb;" k="29" />
<hkern u1="X" u2="&#xfa;" k="29" />
<hkern u1="X" u2="&#xf9;" k="29" />
<hkern u1="X" u2="&#xf8;" k="35" />
<hkern u1="X" u2="&#xf6;" k="35" />
<hkern u1="X" u2="&#xf5;" k="35" />
<hkern u1="X" u2="&#xf4;" k="35" />
<hkern u1="X" u2="&#xf3;" k="35" />
<hkern u1="X" u2="&#xf2;" k="35" />
<hkern u1="X" u2="&#xf0;" k="16" />
<hkern u1="X" u2="&#xef;" k="-37" />
<hkern u1="X" u2="&#xec;" k="-117" />
<hkern u1="X" u2="&#xeb;" k="35" />
<hkern u1="X" u2="&#xea;" k="35" />
<hkern u1="X" u2="&#xe9;" k="35" />
<hkern u1="X" u2="&#xe8;" k="35" />
<hkern u1="X" u2="&#xe7;" k="35" />
<hkern u1="X" u2="&#xd8;" k="43" />
<hkern u1="X" u2="&#xd6;" k="43" />
<hkern u1="X" u2="&#xd5;" k="43" />
<hkern u1="X" u2="&#xd4;" k="43" />
<hkern u1="X" u2="&#xd3;" k="43" />
<hkern u1="X" u2="&#xd2;" k="43" />
<hkern u1="X" u2="&#xc7;" k="41" />
<hkern u1="X" u2="&#xae;" k="31" />
<hkern u1="X" u2="&#xab;" k="53" />
<hkern u1="X" u2="y" k="59" />
<hkern u1="X" u2="w" k="53" />
<hkern u1="X" u2="v" k="59" />
<hkern u1="X" u2="u" k="29" />
<hkern u1="X" u2="t" k="18" />
<hkern u1="X" u2="q" k="35" />
<hkern u1="X" u2="o" k="35" />
<hkern u1="X" u2="g" k="27" />
<hkern u1="X" u2="e" k="35" />
<hkern u1="X" u2="d" k="35" />
<hkern u1="X" u2="c" k="35" />
<hkern u1="X" u2="Q" k="43" />
<hkern u1="X" u2="O" k="43" />
<hkern u1="X" u2="G" k="43" />
<hkern u1="X" u2="C" k="41" />
<hkern u1="X" u2="&#x2d;" k="86" />
<hkern u1="Y" u2="&#xff;" k="82" />
<hkern u1="Y" u2="&#xf0;" k="98" />
<hkern u1="Y" u2="&#xef;" k="-55" />
<hkern u1="Y" u2="&#xec;" k="-135" />
<hkern u1="Y" u2="&#xeb;" k="145" />
<hkern u1="Y" u2="&#xe4;" k="135" />
<hkern u1="Y" u2="&#xe3;" k="135" />
<hkern u1="Y" u2="&#xc6;" k="96" />
<hkern u1="Y" u2="&#xae;" k="49" />
<hkern u1="Y" u2="x" k="90" />
<hkern u1="Y" u2="v" k="94" />
<hkern u1="Y" u2="f" k="31" />
<hkern u1="Y" u2="&#x40;" k="76" />
<hkern u1="Y" u2="&#x2f;" k="131" />
<hkern u1="Y" u2="&#x26;" k="88" />
<hkern u1="Z" u2="&#xf0;" k="18" />
<hkern u1="Z" u2="&#xee;" k="-14" />
<hkern u1="Z" u2="&#xec;" k="-76" />
<hkern u1="Z" u2="&#xae;" k="25" />
<hkern u1="Z" u2="v" k="31" />
<hkern u1="[" u2="&#x153;" k="94" />
<hkern u1="[" u2="&#x152;" k="70" />
<hkern u1="[" u2="&#xff;" k="76" />
<hkern u1="[" u2="&#xfd;" k="76" />
<hkern u1="[" u2="&#xfc;" k="88" />
<hkern u1="[" u2="&#xfb;" k="88" />
<hkern u1="[" u2="&#xfa;" k="88" />
<hkern u1="[" u2="&#xf9;" k="88" />
<hkern u1="[" u2="&#xf8;" k="94" />
<hkern u1="[" u2="&#xf6;" k="94" />
<hkern u1="[" u2="&#xf5;" k="94" />
<hkern u1="[" u2="&#xf4;" k="94" />
<hkern u1="[" u2="&#xf3;" k="94" />
<hkern u1="[" u2="&#xf2;" k="94" />
<hkern u1="[" u2="&#xf1;" k="86" />
<hkern u1="[" u2="&#xf0;" k="31" />
<hkern u1="[" u2="&#xec;" k="-68" />
<hkern u1="[" u2="&#xeb;" k="94" />
<hkern u1="[" u2="&#xea;" k="94" />
<hkern u1="[" u2="&#xe9;" k="94" />
<hkern u1="[" u2="&#xe8;" k="94" />
<hkern u1="[" u2="&#xe7;" k="94" />
<hkern u1="[" u2="&#xe6;" k="84" />
<hkern u1="[" u2="&#xe5;" k="84" />
<hkern u1="[" u2="&#xe4;" k="84" />
<hkern u1="[" u2="&#xe3;" k="84" />
<hkern u1="[" u2="&#xe2;" k="84" />
<hkern u1="[" u2="&#xe1;" k="84" />
<hkern u1="[" u2="&#xe0;" k="84" />
<hkern u1="[" u2="&#xd8;" k="70" />
<hkern u1="[" u2="&#xd6;" k="70" />
<hkern u1="[" u2="&#xd5;" k="70" />
<hkern u1="[" u2="&#xd4;" k="70" />
<hkern u1="[" u2="&#xd3;" k="70" />
<hkern u1="[" u2="&#xd2;" k="70" />
<hkern u1="[" u2="&#xc7;" k="68" />
<hkern u1="[" u2="&#xc6;" k="80" />
<hkern u1="[" u2="&#xc5;" k="78" />
<hkern u1="[" u2="&#xc4;" k="78" />
<hkern u1="[" u2="&#xc3;" k="78" />
<hkern u1="[" u2="&#xc2;" k="78" />
<hkern u1="[" u2="&#xc1;" k="78" />
<hkern u1="[" u2="&#xc0;" k="78" />
<hkern u1="[" u2="&#x7b;" k="47" />
<hkern u1="[" u2="z" k="70" />
<hkern u1="[" u2="y" k="76" />
<hkern u1="[" u2="x" k="61" />
<hkern u1="[" u2="w" k="84" />
<hkern u1="[" u2="v" k="82" />
<hkern u1="[" u2="u" k="88" />
<hkern u1="[" u2="t" k="70" />
<hkern u1="[" u2="s" k="84" />
<hkern u1="[" u2="r" k="86" />
<hkern u1="[" u2="q" k="92" />
<hkern u1="[" u2="p" k="86" />
<hkern u1="[" u2="o" k="94" />
<hkern u1="[" u2="n" k="86" />
<hkern u1="[" u2="m" k="86" />
<hkern u1="[" u2="f" k="37" />
<hkern u1="[" u2="e" k="94" />
<hkern u1="[" u2="d" k="92" />
<hkern u1="[" u2="c" k="94" />
<hkern u1="[" u2="a" k="84" />
<hkern u1="[" u2="S" k="39" />
<hkern u1="[" u2="Q" k="70" />
<hkern u1="[" u2="O" k="70" />
<hkern u1="[" u2="G" k="70" />
<hkern u1="[" u2="C" k="68" />
<hkern u1="[" u2="A" k="78" />
<hkern u1="[" u2="&#x28;" k="29" />
<hkern u1="\" u2="&#x201d;" k="139" />
<hkern u1="\" u2="&#x2019;" k="139" />
<hkern u1="\" u2="&#x178;" k="135" />
<hkern u1="\" u2="&#xff;" k="49" />
<hkern u1="\" u2="&#xfd;" k="49" />
<hkern u1="\" u2="&#xdd;" k="135" />
<hkern u1="\" u2="y" k="49" />
<hkern u1="\" u2="w" k="39" />
<hkern u1="\" u2="v" k="49" />
<hkern u1="\" u2="t" k="20" />
<hkern u1="\" u2="Y" k="135" />
<hkern u1="\" u2="W" k="55" />
<hkern u1="\" u2="V" k="90" />
<hkern u1="\" u2="T" k="117" />
<hkern u1="\" u2="&#x27;" k="135" />
<hkern u1="\" u2="&#x22;" k="135" />
<hkern u1="a" u2="&#x2122;" k="20" />
<hkern u1="a" u2="&#x7d;" k="31" />
<hkern u1="a" u2="v" k="8" />
<hkern u1="a" u2="]" k="39" />
<hkern u1="a" u2="\" k="61" />
<hkern u1="a" u2="V" k="47" />
<hkern u1="a" u2="&#x3f;" k="33" />
<hkern u1="b" u2="&#x2122;" k="37" />
<hkern u1="b" u2="&#xc6;" k="16" />
<hkern u1="b" u2="&#x7d;" k="88" />
<hkern u1="b" u2="x" k="29" />
<hkern u1="b" u2="v" k="18" />
<hkern u1="b" u2="]" k="92" />
<hkern u1="b" u2="\" k="68" />
<hkern u1="b" u2="X" k="39" />
<hkern u1="b" u2="V" k="70" />
<hkern u1="b" u2="&#x3f;" k="63" />
<hkern u1="b" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x29;" k="35" />
<hkern u1="c" u2="&#xf0;" k="29" />
<hkern u1="c" u2="&#x7d;" k="55" />
<hkern u1="c" u2="]" k="66" />
<hkern u1="c" u2="V" k="25" />
<hkern u1="c" u2="&#x3f;" k="31" />
<hkern u1="e" u2="&#x2122;" k="31" />
<hkern u1="e" u2="&#xc6;" k="10" />
<hkern u1="e" u2="&#x7d;" k="80" />
<hkern u1="e" u2="x" k="14" />
<hkern u1="e" u2="v" k="18" />
<hkern u1="e" u2="]" k="84" />
<hkern u1="e" u2="\" k="66" />
<hkern u1="e" u2="V" k="72" />
<hkern u1="e" u2="&#x3f;" k="53" />
<hkern u1="f" u2="&#x203a;" k="74" />
<hkern u1="f" u2="&#x2039;" k="84" />
<hkern u1="f" u2="&#x2026;" k="94" />
<hkern u1="f" u2="&#x201e;" k="94" />
<hkern u1="f" u2="&#x201a;" k="94" />
<hkern u1="f" u2="&#x2014;" k="100" />
<hkern u1="f" u2="&#x2013;" k="100" />
<hkern u1="f" u2="&#x178;" k="51" />
<hkern u1="f" u2="&#x153;" k="25" />
<hkern u1="f" u2="&#xf8;" k="25" />
<hkern u1="f" u2="&#xf6;" k="25" />
<hkern u1="f" u2="&#xf5;" k="25" />
<hkern u1="f" u2="&#xf4;" k="25" />
<hkern u1="f" u2="&#xf3;" k="25" />
<hkern u1="f" u2="&#xf2;" k="25" />
<hkern u1="f" u2="&#xf0;" k="94" />
<hkern u1="f" u2="&#xec;" k="-170" />
<hkern u1="f" u2="&#xeb;" k="25" />
<hkern u1="f" u2="&#xea;" k="25" />
<hkern u1="f" u2="&#xe9;" k="25" />
<hkern u1="f" u2="&#xe8;" k="25" />
<hkern u1="f" u2="&#xe7;" k="25" />
<hkern u1="f" u2="&#xdd;" k="51" />
<hkern u1="f" u2="&#xc6;" k="82" />
<hkern u1="f" u2="&#xc5;" k="78" />
<hkern u1="f" u2="&#xc4;" k="78" />
<hkern u1="f" u2="&#xc3;" k="78" />
<hkern u1="f" u2="&#xc2;" k="78" />
<hkern u1="f" u2="&#xc1;" k="78" />
<hkern u1="f" u2="&#xc0;" k="78" />
<hkern u1="f" u2="&#xbb;" k="74" />
<hkern u1="f" u2="&#xab;" k="84" />
<hkern u1="f" u2="q" k="33" />
<hkern u1="f" u2="o" k="25" />
<hkern u1="f" u2="g" k="16" />
<hkern u1="f" u2="e" k="25" />
<hkern u1="f" u2="d" k="33" />
<hkern u1="f" u2="c" k="25" />
<hkern u1="f" u2="Z" k="39" />
<hkern u1="f" u2="Y" k="51" />
<hkern u1="f" u2="X" k="45" />
<hkern u1="f" u2="T" k="104" />
<hkern u1="f" u2="J" k="76" />
<hkern u1="f" u2="A" k="78" />
<hkern u1="f" u2="&#x2f;" k="57" />
<hkern u1="f" u2="&#x2e;" k="94" />
<hkern u1="f" u2="&#x2d;" k="100" />
<hkern u1="f" u2="&#x2c;" k="94" />
<hkern u1="f" u2="&#x26;" k="31" />
<hkern u1="g" u2="&#xf0;" k="10" />
<hkern u1="g" u2="j" k="-33" />
<hkern u1="h" u2="&#x2122;" k="35" />
<hkern u1="h" u2="&#x7d;" k="82" />
<hkern u1="h" u2="v" k="12" />
<hkern u1="h" u2="]" k="88" />
<hkern u1="h" u2="\" k="68" />
<hkern u1="h" u2="V" k="63" />
<hkern u1="h" u2="&#x3f;" k="55" />
<hkern u1="k" u2="&#xf0;" k="41" />
<hkern u1="k" u2="&#x7d;" k="45" />
<hkern u1="k" u2="]" k="57" />
<hkern u1="k" u2="V" k="18" />
<hkern u1="k" u2="&#x3f;" k="23" />
<hkern u1="l" u2="&#xb7;" k="98" />
<hkern u1="m" u2="&#x2122;" k="35" />
<hkern u1="m" u2="&#x7d;" k="82" />
<hkern u1="m" u2="v" k="12" />
<hkern u1="m" u2="]" k="88" />
<hkern u1="m" u2="\" k="68" />
<hkern u1="m" u2="V" k="63" />
<hkern u1="m" u2="&#x3f;" k="55" />
<hkern u1="n" u2="&#x2122;" k="35" />
<hkern u1="n" u2="&#x7d;" k="82" />
<hkern u1="n" u2="v" k="12" />
<hkern u1="n" u2="]" k="88" />
<hkern u1="n" u2="\" k="68" />
<hkern u1="n" u2="V" k="63" />
<hkern u1="n" u2="&#x3f;" k="55" />
<hkern u1="o" u2="&#x2122;" k="33" />
<hkern u1="o" u2="&#xc6;" k="14" />
<hkern u1="o" u2="&#x7d;" k="88" />
<hkern u1="o" u2="x" k="29" />
<hkern u1="o" u2="v" k="20" />
<hkern u1="o" u2="]" k="94" />
<hkern u1="o" u2="\" k="72" />
<hkern u1="o" u2="X" k="35" />
<hkern u1="o" u2="V" k="70" />
<hkern u1="o" u2="&#x3f;" k="59" />
<hkern u1="o" u2="&#x29;" k="35" />
<hkern u1="p" u2="&#x2122;" k="37" />
<hkern u1="p" u2="&#xc6;" k="16" />
<hkern u1="p" u2="&#x7d;" k="88" />
<hkern u1="p" u2="x" k="29" />
<hkern u1="p" u2="v" k="18" />
<hkern u1="p" u2="]" k="92" />
<hkern u1="p" u2="\" k="68" />
<hkern u1="p" u2="X" k="39" />
<hkern u1="p" u2="V" k="70" />
<hkern u1="p" u2="&#x3f;" k="63" />
<hkern u1="p" u2="&#x2a;" k="16" />
<hkern u1="p" u2="&#x29;" k="35" />
<hkern u1="q" u2="&#x2122;" k="27" />
<hkern u1="q" u2="&#x7d;" k="78" />
<hkern u1="q" u2="]" k="86" />
<hkern u1="q" u2="\" k="41" />
<hkern u1="q" u2="V" k="61" />
<hkern u1="q" u2="&#x3f;" k="43" />
<hkern u1="r" u2="&#xf0;" k="111" />
<hkern u1="r" u2="&#xc6;" k="102" />
<hkern u1="r" u2="&#x7d;" k="63" />
<hkern u1="r" u2="]" k="72" />
<hkern u1="r" u2="X" k="53" />
<hkern u1="r" u2="&#x2f;" k="70" />
<hkern u1="r" u2="&#x26;" k="35" />
<hkern u1="s" u2="&#x2122;" k="25" />
<hkern u1="s" u2="&#xc6;" k="10" />
<hkern u1="s" u2="&#x7d;" k="76" />
<hkern u1="s" u2="x" k="10" />
<hkern u1="s" u2="v" k="16" />
<hkern u1="s" u2="]" k="84" />
<hkern u1="s" u2="\" k="35" />
<hkern u1="s" u2="V" k="51" />
<hkern u1="s" u2="&#x3f;" k="43" />
<hkern u1="t" u2="&#xf0;" k="12" />
<hkern u1="t" u2="&#x7d;" k="29" />
<hkern u1="t" u2="]" k="39" />
<hkern u1="u" u2="&#x2122;" k="27" />
<hkern u1="u" u2="&#x7d;" k="78" />
<hkern u1="u" u2="]" k="86" />
<hkern u1="u" u2="\" k="41" />
<hkern u1="u" u2="V" k="61" />
<hkern u1="u" u2="&#x3f;" k="43" />
<hkern u1="v" u2="&#x2039;" k="27" />
<hkern u1="v" u2="&#x2026;" k="88" />
<hkern u1="v" u2="&#x201e;" k="88" />
<hkern u1="v" u2="&#x201a;" k="88" />
<hkern u1="v" u2="&#x2014;" k="33" />
<hkern u1="v" u2="&#x2013;" k="33" />
<hkern u1="v" u2="&#x178;" k="94" />
<hkern u1="v" u2="&#x153;" k="20" />
<hkern u1="v" u2="&#xf8;" k="20" />
<hkern u1="v" u2="&#xf6;" k="20" />
<hkern u1="v" u2="&#xf5;" k="20" />
<hkern u1="v" u2="&#xf4;" k="20" />
<hkern u1="v" u2="&#xf3;" k="20" />
<hkern u1="v" u2="&#xf2;" k="20" />
<hkern u1="v" u2="&#xf0;" k="29" />
<hkern u1="v" u2="&#xeb;" k="20" />
<hkern u1="v" u2="&#xea;" k="20" />
<hkern u1="v" u2="&#xe9;" k="20" />
<hkern u1="v" u2="&#xe8;" k="20" />
<hkern u1="v" u2="&#xe7;" k="20" />
<hkern u1="v" u2="&#xe6;" k="18" />
<hkern u1="v" u2="&#xe5;" k="18" />
<hkern u1="v" u2="&#xe4;" k="18" />
<hkern u1="v" u2="&#xe3;" k="18" />
<hkern u1="v" u2="&#xe2;" k="18" />
<hkern u1="v" u2="&#xe1;" k="18" />
<hkern u1="v" u2="&#xe0;" k="18" />
<hkern u1="v" u2="&#xdd;" k="94" />
<hkern u1="v" u2="&#xc6;" k="47" />
<hkern u1="v" u2="&#xc5;" k="41" />
<hkern u1="v" u2="&#xc4;" k="41" />
<hkern u1="v" u2="&#xc3;" k="41" />
<hkern u1="v" u2="&#xc2;" k="41" />
<hkern u1="v" u2="&#xc1;" k="41" />
<hkern u1="v" u2="&#xc0;" k="41" />
<hkern u1="v" u2="&#xab;" k="27" />
<hkern u1="v" u2="&#x7d;" k="76" />
<hkern u1="v" u2="s" k="14" />
<hkern u1="v" u2="q" k="20" />
<hkern u1="v" u2="o" k="20" />
<hkern u1="v" u2="g" k="20" />
<hkern u1="v" u2="e" k="20" />
<hkern u1="v" u2="d" k="20" />
<hkern u1="v" u2="c" k="20" />
<hkern u1="v" u2="a" k="18" />
<hkern u1="v" u2="]" k="82" />
<hkern u1="v" u2="Z" k="43" />
<hkern u1="v" u2="Y" k="94" />
<hkern u1="v" u2="X" k="59" />
<hkern u1="v" u2="V" k="18" />
<hkern u1="v" u2="T" k="162" />
<hkern u1="v" u2="J" k="72" />
<hkern u1="v" u2="A" k="41" />
<hkern u1="v" u2="&#x3f;" k="31" />
<hkern u1="v" u2="&#x2f;" k="45" />
<hkern u1="v" u2="&#x2e;" k="88" />
<hkern u1="v" u2="&#x2d;" k="33" />
<hkern u1="v" u2="&#x2c;" k="88" />
<hkern u1="w" u2="&#xf0;" k="23" />
<hkern u1="w" u2="&#xc6;" k="43" />
<hkern u1="w" u2="&#x7d;" k="76" />
<hkern u1="w" u2="]" k="84" />
<hkern u1="w" u2="X" k="55" />
<hkern u1="w" u2="V" k="20" />
<hkern u1="w" u2="&#x3f;" k="33" />
<hkern u1="w" u2="&#x2f;" k="39" />
<hkern u1="x" u2="&#x2039;" k="59" />
<hkern u1="x" u2="&#x2014;" k="76" />
<hkern u1="x" u2="&#x2013;" k="76" />
<hkern u1="x" u2="&#x178;" k="90" />
<hkern u1="x" u2="&#x153;" k="29" />
<hkern u1="x" u2="&#xf8;" k="29" />
<hkern u1="x" u2="&#xf6;" k="29" />
<hkern u1="x" u2="&#xf5;" k="29" />
<hkern u1="x" u2="&#xf4;" k="29" />
<hkern u1="x" u2="&#xf3;" k="29" />
<hkern u1="x" u2="&#xf2;" k="29" />
<hkern u1="x" u2="&#xf0;" k="41" />
<hkern u1="x" u2="&#xeb;" k="29" />
<hkern u1="x" u2="&#xea;" k="29" />
<hkern u1="x" u2="&#xe9;" k="29" />
<hkern u1="x" u2="&#xe8;" k="29" />
<hkern u1="x" u2="&#xe7;" k="29" />
<hkern u1="x" u2="&#xe6;" k="12" />
<hkern u1="x" u2="&#xe5;" k="12" />
<hkern u1="x" u2="&#xe4;" k="12" />
<hkern u1="x" u2="&#xe3;" k="12" />
<hkern u1="x" u2="&#xe2;" k="12" />
<hkern u1="x" u2="&#xe1;" k="12" />
<hkern u1="x" u2="&#xe0;" k="12" />
<hkern u1="x" u2="&#xdd;" k="90" />
<hkern u1="x" u2="&#xab;" k="59" />
<hkern u1="x" u2="&#x7d;" k="53" />
<hkern u1="x" u2="q" k="31" />
<hkern u1="x" u2="o" k="29" />
<hkern u1="x" u2="g" k="25" />
<hkern u1="x" u2="e" k="29" />
<hkern u1="x" u2="d" k="31" />
<hkern u1="x" u2="c" k="29" />
<hkern u1="x" u2="a" k="12" />
<hkern u1="x" u2="]" k="63" />
<hkern u1="x" u2="Y" k="90" />
<hkern u1="x" u2="V" k="14" />
<hkern u1="x" u2="T" k="180" />
<hkern u1="x" u2="J" k="10" />
<hkern u1="x" u2="&#x2d;" k="76" />
<hkern u1="y" u2="&#xf0;" k="29" />
<hkern u1="y" u2="&#xc6;" k="47" />
<hkern u1="y" u2="&#x7d;" k="68" />
<hkern u1="y" u2="]" k="74" />
<hkern u1="y" u2="X" k="59" />
<hkern u1="y" u2="V" k="18" />
<hkern u1="y" u2="&#x3f;" k="31" />
<hkern u1="y" u2="&#x2f;" k="47" />
<hkern u1="z" u2="&#x2122;" k="16" />
<hkern u1="z" u2="&#xf0;" k="23" />
<hkern u1="z" u2="&#x7d;" k="59" />
<hkern u1="z" u2="]" k="70" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x3f;" k="29" />
<hkern u1="&#x7b;" u2="&#x153;" k="88" />
<hkern u1="&#x7b;" u2="&#x152;" k="68" />
<hkern u1="&#x7b;" u2="&#xff;" k="70" />
<hkern u1="&#x7b;" u2="&#xfd;" k="70" />
<hkern u1="&#x7b;" u2="&#xfc;" k="82" />
<hkern u1="&#x7b;" u2="&#xfb;" k="82" />
<hkern u1="&#x7b;" u2="&#xfa;" k="82" />
<hkern u1="&#x7b;" u2="&#xf9;" k="82" />
<hkern u1="&#x7b;" u2="&#xf8;" k="88" />
<hkern u1="&#x7b;" u2="&#xf6;" k="88" />
<hkern u1="&#x7b;" u2="&#xf5;" k="88" />
<hkern u1="&#x7b;" u2="&#xf4;" k="88" />
<hkern u1="&#x7b;" u2="&#xf3;" k="88" />
<hkern u1="&#x7b;" u2="&#xf2;" k="88" />
<hkern u1="&#x7b;" u2="&#xf1;" k="78" />
<hkern u1="&#x7b;" u2="&#xf0;" k="31" />
<hkern u1="&#x7b;" u2="&#xec;" k="-70" />
<hkern u1="&#x7b;" u2="&#xeb;" k="88" />
<hkern u1="&#x7b;" u2="&#xea;" k="88" />
<hkern u1="&#x7b;" u2="&#xe9;" k="88" />
<hkern u1="&#x7b;" u2="&#xe8;" k="88" />
<hkern u1="&#x7b;" u2="&#xe7;" k="88" />
<hkern u1="&#x7b;" u2="&#xe6;" k="76" />
<hkern u1="&#x7b;" u2="&#xe5;" k="76" />
<hkern u1="&#x7b;" u2="&#xe4;" k="76" />
<hkern u1="&#x7b;" u2="&#xe3;" k="76" />
<hkern u1="&#x7b;" u2="&#xe2;" k="76" />
<hkern u1="&#x7b;" u2="&#xe1;" k="76" />
<hkern u1="&#x7b;" u2="&#xe0;" k="76" />
<hkern u1="&#x7b;" u2="&#xd8;" k="68" />
<hkern u1="&#x7b;" u2="&#xd6;" k="68" />
<hkern u1="&#x7b;" u2="&#xd5;" k="68" />
<hkern u1="&#x7b;" u2="&#xd4;" k="68" />
<hkern u1="&#x7b;" u2="&#xd3;" k="68" />
<hkern u1="&#x7b;" u2="&#xd2;" k="68" />
<hkern u1="&#x7b;" u2="&#xc7;" k="66" />
<hkern u1="&#x7b;" u2="&#xc6;" k="72" />
<hkern u1="&#x7b;" u2="&#xc5;" k="68" />
<hkern u1="&#x7b;" u2="&#xc4;" k="68" />
<hkern u1="&#x7b;" u2="&#xc3;" k="68" />
<hkern u1="&#x7b;" u2="&#xc2;" k="68" />
<hkern u1="&#x7b;" u2="&#xc1;" k="68" />
<hkern u1="&#x7b;" u2="&#xc0;" k="68" />
<hkern u1="&#x7b;" u2="&#x7b;" k="49" />
<hkern u1="&#x7b;" u2="z" k="59" />
<hkern u1="&#x7b;" u2="y" k="70" />
<hkern u1="&#x7b;" u2="x" k="53" />
<hkern u1="&#x7b;" u2="w" k="76" />
<hkern u1="&#x7b;" u2="v" k="74" />
<hkern u1="&#x7b;" u2="u" k="82" />
<hkern u1="&#x7b;" u2="t" k="61" />
<hkern u1="&#x7b;" u2="s" k="76" />
<hkern u1="&#x7b;" u2="r" k="78" />
<hkern u1="&#x7b;" u2="q" k="88" />
<hkern u1="&#x7b;" u2="p" k="78" />
<hkern u1="&#x7b;" u2="o" k="88" />
<hkern u1="&#x7b;" u2="n" k="78" />
<hkern u1="&#x7b;" u2="m" k="78" />
<hkern u1="&#x7b;" u2="f" k="37" />
<hkern u1="&#x7b;" u2="e" k="88" />
<hkern u1="&#x7b;" u2="d" k="88" />
<hkern u1="&#x7b;" u2="c" k="88" />
<hkern u1="&#x7b;" u2="a" k="76" />
<hkern u1="&#x7b;" u2="S" k="37" />
<hkern u1="&#x7b;" u2="Q" k="68" />
<hkern u1="&#x7b;" u2="O" k="68" />
<hkern u1="&#x7b;" u2="G" k="68" />
<hkern u1="&#x7b;" u2="C" k="66" />
<hkern u1="&#x7b;" u2="A" k="68" />
<hkern u1="&#x7b;" u2="&#x28;" k="29" />
<hkern u1="&#x7d;" u2="&#x7d;" k="49" />
<hkern u1="&#x7d;" u2="]" k="47" />
<hkern u1="&#x7d;" u2="&#x29;" k="20" />
<hkern u1="&#xa1;" u2="&#x178;" k="72" />
<hkern u1="&#xa1;" u2="&#xdd;" k="72" />
<hkern u1="&#xa1;" u2="Y" k="72" />
<hkern u1="&#xa1;" u2="V" k="27" />
<hkern u1="&#xa1;" u2="T" k="123" />
<hkern u1="&#xab;" u2="V" k="51" />
<hkern u1="&#xae;" u2="&#x178;" k="51" />
<hkern u1="&#xae;" u2="&#xdd;" k="51" />
<hkern u1="&#xae;" u2="&#xc6;" k="78" />
<hkern u1="&#xae;" u2="&#xc5;" k="63" />
<hkern u1="&#xae;" u2="&#xc4;" k="63" />
<hkern u1="&#xae;" u2="&#xc3;" k="63" />
<hkern u1="&#xae;" u2="&#xc2;" k="63" />
<hkern u1="&#xae;" u2="&#xc1;" k="63" />
<hkern u1="&#xae;" u2="&#xc0;" k="63" />
<hkern u1="&#xae;" u2="Z" k="45" />
<hkern u1="&#xae;" u2="Y" k="51" />
<hkern u1="&#xae;" u2="X" k="33" />
<hkern u1="&#xae;" u2="V" k="25" />
<hkern u1="&#xae;" u2="T" k="31" />
<hkern u1="&#xae;" u2="J" k="63" />
<hkern u1="&#xae;" u2="A" k="63" />
<hkern u1="&#xb7;" u2="l" k="98" />
<hkern u1="&#xbb;" u2="&#xc6;" k="23" />
<hkern u1="&#xbb;" u2="x" k="59" />
<hkern u1="&#xbb;" u2="v" k="27" />
<hkern u1="&#xbb;" u2="X" k="55" />
<hkern u1="&#xbb;" u2="V" k="63" />
<hkern u1="&#xbf;" u2="&#x178;" k="150" />
<hkern u1="&#xbf;" u2="&#x153;" k="82" />
<hkern u1="&#xbf;" u2="&#x152;" k="72" />
<hkern u1="&#xbf;" u2="&#xff;" k="70" />
<hkern u1="&#xbf;" u2="&#xfe;" k="82" />
<hkern u1="&#xbf;" u2="&#xfd;" k="70" />
<hkern u1="&#xbf;" u2="&#xfc;" k="78" />
<hkern u1="&#xbf;" u2="&#xfb;" k="78" />
<hkern u1="&#xbf;" u2="&#xfa;" k="78" />
<hkern u1="&#xbf;" u2="&#xf9;" k="78" />
<hkern u1="&#xbf;" u2="&#xf8;" k="82" />
<hkern u1="&#xbf;" u2="&#xf6;" k="82" />
<hkern u1="&#xbf;" u2="&#xf5;" k="82" />
<hkern u1="&#xbf;" u2="&#xf4;" k="82" />
<hkern u1="&#xbf;" u2="&#xf3;" k="82" />
<hkern u1="&#xbf;" u2="&#xf2;" k="82" />
<hkern u1="&#xbf;" u2="&#xf1;" k="82" />
<hkern u1="&#xbf;" u2="&#xf0;" k="80" />
<hkern u1="&#xbf;" u2="&#xef;" k="82" />
<hkern u1="&#xbf;" u2="&#xee;" k="82" />
<hkern u1="&#xbf;" u2="&#xed;" k="82" />
<hkern u1="&#xbf;" u2="&#xec;" k="82" />
<hkern u1="&#xbf;" u2="&#xeb;" k="82" />
<hkern u1="&#xbf;" u2="&#xea;" k="82" />
<hkern u1="&#xbf;" u2="&#xe9;" k="82" />
<hkern u1="&#xbf;" u2="&#xe8;" k="82" />
<hkern u1="&#xbf;" u2="&#xe7;" k="82" />
<hkern u1="&#xbf;" u2="&#xe6;" k="88" />
<hkern u1="&#xbf;" u2="&#xe5;" k="88" />
<hkern u1="&#xbf;" u2="&#xe4;" k="88" />
<hkern u1="&#xbf;" u2="&#xe3;" k="88" />
<hkern u1="&#xbf;" u2="&#xe2;" k="88" />
<hkern u1="&#xbf;" u2="&#xe1;" k="88" />
<hkern u1="&#xbf;" u2="&#xe0;" k="88" />
<hkern u1="&#xbf;" u2="&#xdf;" k="82" />
<hkern u1="&#xbf;" u2="&#xde;" k="78" />
<hkern u1="&#xbf;" u2="&#xdd;" k="150" />
<hkern u1="&#xbf;" u2="&#xdc;" k="76" />
<hkern u1="&#xbf;" u2="&#xdb;" k="76" />
<hkern u1="&#xbf;" u2="&#xda;" k="76" />
<hkern u1="&#xbf;" u2="&#xd9;" k="76" />
<hkern u1="&#xbf;" u2="&#xd8;" k="72" />
<hkern u1="&#xbf;" u2="&#xd6;" k="72" />
<hkern u1="&#xbf;" u2="&#xd5;" k="72" />
<hkern u1="&#xbf;" u2="&#xd4;" k="72" />
<hkern u1="&#xbf;" u2="&#xd3;" k="72" />
<hkern u1="&#xbf;" u2="&#xd2;" k="72" />
<hkern u1="&#xbf;" u2="&#xd1;" k="78" />
<hkern u1="&#xbf;" u2="&#xd0;" k="78" />
<hkern u1="&#xbf;" u2="&#xcf;" k="78" />
<hkern u1="&#xbf;" u2="&#xce;" k="78" />
<hkern u1="&#xbf;" u2="&#xcd;" k="78" />
<hkern u1="&#xbf;" u2="&#xcc;" k="78" />
<hkern u1="&#xbf;" u2="&#xcb;" k="78" />
<hkern u1="&#xbf;" u2="&#xca;" k="78" />
<hkern u1="&#xbf;" u2="&#xc9;" k="78" />
<hkern u1="&#xbf;" u2="&#xc8;" k="78" />
<hkern u1="&#xbf;" u2="&#xc7;" k="74" />
<hkern u1="&#xbf;" u2="&#xc6;" k="113" />
<hkern u1="&#xbf;" u2="&#xc5;" k="109" />
<hkern u1="&#xbf;" u2="&#xc4;" k="109" />
<hkern u1="&#xbf;" u2="&#xc3;" k="109" />
<hkern u1="&#xbf;" u2="&#xc2;" k="109" />
<hkern u1="&#xbf;" u2="&#xc1;" k="109" />
<hkern u1="&#xbf;" u2="&#xc0;" k="109" />
<hkern u1="&#xbf;" u2="z" k="74" />
<hkern u1="&#xbf;" u2="y" k="70" />
<hkern u1="&#xbf;" u2="x" k="68" />
<hkern u1="&#xbf;" u2="w" k="78" />
<hkern u1="&#xbf;" u2="v" k="78" />
<hkern u1="&#xbf;" u2="u" k="78" />
<hkern u1="&#xbf;" u2="t" k="70" />
<hkern u1="&#xbf;" u2="s" k="82" />
<hkern u1="&#xbf;" u2="r" k="82" />
<hkern u1="&#xbf;" u2="q" k="82" />
<hkern u1="&#xbf;" u2="p" k="82" />
<hkern u1="&#xbf;" u2="o" k="82" />
<hkern u1="&#xbf;" u2="n" k="82" />
<hkern u1="&#xbf;" u2="m" k="82" />
<hkern u1="&#xbf;" u2="l" k="82" />
<hkern u1="&#xbf;" u2="k" k="82" />
<hkern u1="&#xbf;" u2="j" k="-78" />
<hkern u1="&#xbf;" u2="i" k="82" />
<hkern u1="&#xbf;" u2="h" k="82" />
<hkern u1="&#xbf;" u2="f" k="66" />
<hkern u1="&#xbf;" u2="e" k="82" />
<hkern u1="&#xbf;" u2="d" k="82" />
<hkern u1="&#xbf;" u2="c" k="82" />
<hkern u1="&#xbf;" u2="b" k="82" />
<hkern u1="&#xbf;" u2="a" k="88" />
<hkern u1="&#xbf;" u2="Z" k="104" />
<hkern u1="&#xbf;" u2="Y" k="150" />
<hkern u1="&#xbf;" u2="X" k="100" />
<hkern u1="&#xbf;" u2="W" k="90" />
<hkern u1="&#xbf;" u2="V" k="106" />
<hkern u1="&#xbf;" u2="U" k="76" />
<hkern u1="&#xbf;" u2="T" k="188" />
<hkern u1="&#xbf;" u2="S" k="78" />
<hkern u1="&#xbf;" u2="R" k="78" />
<hkern u1="&#xbf;" u2="Q" k="72" />
<hkern u1="&#xbf;" u2="P" k="78" />
<hkern u1="&#xbf;" u2="O" k="72" />
<hkern u1="&#xbf;" u2="N" k="78" />
<hkern u1="&#xbf;" u2="M" k="78" />
<hkern u1="&#xbf;" u2="L" k="78" />
<hkern u1="&#xbf;" u2="K" k="78" />
<hkern u1="&#xbf;" u2="J" k="45" />
<hkern u1="&#xbf;" u2="I" k="78" />
<hkern u1="&#xbf;" u2="H" k="78" />
<hkern u1="&#xbf;" u2="G" k="72" />
<hkern u1="&#xbf;" u2="F" k="78" />
<hkern u1="&#xbf;" u2="E" k="78" />
<hkern u1="&#xbf;" u2="D" k="78" />
<hkern u1="&#xbf;" u2="C" k="74" />
<hkern u1="&#xbf;" u2="B" k="78" />
<hkern u1="&#xbf;" u2="A" k="109" />
<hkern u1="&#xc0;" u2="&#x2122;" k="98" />
<hkern u1="&#xc0;" u2="&#xf0;" k="10" />
<hkern u1="&#xc0;" u2="&#xae;" k="63" />
<hkern u1="&#xc0;" u2="&#x7d;" k="68" />
<hkern u1="&#xc0;" u2="v" k="41" />
<hkern u1="&#xc0;" u2="f" k="14" />
<hkern u1="&#xc0;" u2="]" k="78" />
<hkern u1="&#xc0;" u2="\" k="102" />
<hkern u1="&#xc0;" u2="V" k="59" />
<hkern u1="&#xc0;" u2="&#x3f;" k="66" />
<hkern u1="&#xc0;" u2="&#x2a;" k="86" />
<hkern u1="&#xc1;" u2="&#x2122;" k="98" />
<hkern u1="&#xc1;" u2="&#xf0;" k="10" />
<hkern u1="&#xc1;" u2="&#xae;" k="63" />
<hkern u1="&#xc1;" u2="&#x7d;" k="68" />
<hkern u1="&#xc1;" u2="v" k="41" />
<hkern u1="&#xc1;" u2="f" k="14" />
<hkern u1="&#xc1;" u2="]" k="78" />
<hkern u1="&#xc1;" u2="\" k="102" />
<hkern u1="&#xc1;" u2="V" k="59" />
<hkern u1="&#xc1;" u2="&#x3f;" k="66" />
<hkern u1="&#xc1;" u2="&#x2a;" k="86" />
<hkern u1="&#xc2;" u2="&#x2122;" k="98" />
<hkern u1="&#xc2;" u2="&#xf0;" k="10" />
<hkern u1="&#xc2;" u2="&#xae;" k="63" />
<hkern u1="&#xc2;" u2="&#x7d;" k="68" />
<hkern u1="&#xc2;" u2="v" k="41" />
<hkern u1="&#xc2;" u2="f" k="14" />
<hkern u1="&#xc2;" u2="]" k="78" />
<hkern u1="&#xc2;" u2="\" k="102" />
<hkern u1="&#xc2;" u2="V" k="59" />
<hkern u1="&#xc2;" u2="&#x3f;" k="66" />
<hkern u1="&#xc2;" u2="&#x2a;" k="86" />
<hkern u1="&#xc3;" u2="&#x2122;" k="98" />
<hkern u1="&#xc3;" u2="&#xf0;" k="10" />
<hkern u1="&#xc3;" u2="&#xae;" k="63" />
<hkern u1="&#xc3;" u2="&#x7d;" k="68" />
<hkern u1="&#xc3;" u2="v" k="41" />
<hkern u1="&#xc3;" u2="f" k="14" />
<hkern u1="&#xc3;" u2="]" k="78" />
<hkern u1="&#xc3;" u2="\" k="102" />
<hkern u1="&#xc3;" u2="V" k="59" />
<hkern u1="&#xc3;" u2="&#x3f;" k="66" />
<hkern u1="&#xc3;" u2="&#x2a;" k="86" />
<hkern u1="&#xc4;" u2="&#x2122;" k="98" />
<hkern u1="&#xc4;" u2="&#xf0;" k="10" />
<hkern u1="&#xc4;" u2="&#xae;" k="63" />
<hkern u1="&#xc4;" u2="&#x7d;" k="68" />
<hkern u1="&#xc4;" u2="v" k="41" />
<hkern u1="&#xc4;" u2="f" k="14" />
<hkern u1="&#xc4;" u2="]" k="78" />
<hkern u1="&#xc4;" u2="\" k="102" />
<hkern u1="&#xc4;" u2="V" k="59" />
<hkern u1="&#xc4;" u2="&#x3f;" k="66" />
<hkern u1="&#xc4;" u2="&#x2a;" k="86" />
<hkern u1="&#xc5;" u2="&#x2122;" k="98" />
<hkern u1="&#xc5;" u2="&#xf0;" k="10" />
<hkern u1="&#xc5;" u2="&#xae;" k="63" />
<hkern u1="&#xc5;" u2="&#x7d;" k="68" />
<hkern u1="&#xc5;" u2="v" k="41" />
<hkern u1="&#xc5;" u2="f" k="14" />
<hkern u1="&#xc5;" u2="]" k="78" />
<hkern u1="&#xc5;" u2="\" k="102" />
<hkern u1="&#xc5;" u2="V" k="59" />
<hkern u1="&#xc5;" u2="&#x3f;" k="66" />
<hkern u1="&#xc5;" u2="&#x2a;" k="86" />
<hkern u1="&#xc6;" u2="&#xf0;" k="18" />
<hkern u1="&#xc6;" u2="&#xec;" k="-66" />
<hkern u1="&#xc6;" u2="v" k="39" />
<hkern u1="&#xc6;" u2="f" k="10" />
<hkern u1="&#xc7;" u2="&#xf0;" k="16" />
<hkern u1="&#xc7;" u2="&#xec;" k="-51" />
<hkern u1="&#xc7;" u2="&#xae;" k="43" />
<hkern u1="&#xc7;" u2="v" k="47" />
<hkern u1="&#xc7;" u2="f" k="12" />
<hkern u1="&#xc8;" u2="&#xf0;" k="18" />
<hkern u1="&#xc8;" u2="&#xec;" k="-66" />
<hkern u1="&#xc8;" u2="v" k="39" />
<hkern u1="&#xc8;" u2="f" k="10" />
<hkern u1="&#xc9;" u2="&#xf0;" k="18" />
<hkern u1="&#xc9;" u2="&#xec;" k="-66" />
<hkern u1="&#xc9;" u2="v" k="39" />
<hkern u1="&#xc9;" u2="f" k="10" />
<hkern u1="&#xca;" u2="&#xf0;" k="18" />
<hkern u1="&#xca;" u2="&#xec;" k="-66" />
<hkern u1="&#xca;" u2="v" k="39" />
<hkern u1="&#xca;" u2="f" k="10" />
<hkern u1="&#xcb;" u2="&#xf0;" k="18" />
<hkern u1="&#xcb;" u2="&#xec;" k="-66" />
<hkern u1="&#xcb;" u2="v" k="39" />
<hkern u1="&#xcb;" u2="f" k="10" />
<hkern u1="&#xd0;" u2="&#xc6;" k="41" />
<hkern u1="&#xd0;" u2="&#x7d;" k="72" />
<hkern u1="&#xd0;" u2="x" k="10" />
<hkern u1="&#xd0;" u2="]" k="74" />
<hkern u1="&#xd0;" u2="X" k="47" />
<hkern u1="&#xd0;" u2="V" k="27" />
<hkern u1="&#xd0;" u2="&#x3f;" k="31" />
<hkern u1="&#xd0;" u2="&#x29;" k="23" />
<hkern u1="&#xd2;" u2="&#xc6;" k="35" />
<hkern u1="&#xd2;" u2="&#x7d;" k="68" />
<hkern u1="&#xd2;" u2="]" k="70" />
<hkern u1="&#xd2;" u2="X" k="43" />
<hkern u1="&#xd2;" u2="V" k="27" />
<hkern u1="&#xd2;" u2="&#x3f;" k="25" />
<hkern u1="&#xd3;" u2="&#xc6;" k="35" />
<hkern u1="&#xd3;" u2="&#x7d;" k="68" />
<hkern u1="&#xd3;" u2="]" k="70" />
<hkern u1="&#xd3;" u2="X" k="43" />
<hkern u1="&#xd3;" u2="V" k="27" />
<hkern u1="&#xd3;" u2="&#x3f;" k="25" />
<hkern u1="&#xd4;" u2="&#xc6;" k="35" />
<hkern u1="&#xd4;" u2="&#x7d;" k="68" />
<hkern u1="&#xd4;" u2="]" k="70" />
<hkern u1="&#xd4;" u2="X" k="43" />
<hkern u1="&#xd4;" u2="V" k="27" />
<hkern u1="&#xd4;" u2="&#x3f;" k="25" />
<hkern u1="&#xd5;" u2="&#xc6;" k="35" />
<hkern u1="&#xd5;" u2="&#x7d;" k="68" />
<hkern u1="&#xd5;" u2="]" k="70" />
<hkern u1="&#xd5;" u2="X" k="43" />
<hkern u1="&#xd5;" u2="V" k="27" />
<hkern u1="&#xd5;" u2="&#x3f;" k="25" />
<hkern u1="&#xd6;" u2="&#xc6;" k="35" />
<hkern u1="&#xd6;" u2="&#x7d;" k="68" />
<hkern u1="&#xd6;" u2="]" k="70" />
<hkern u1="&#xd6;" u2="X" k="43" />
<hkern u1="&#xd6;" u2="V" k="27" />
<hkern u1="&#xd6;" u2="&#x3f;" k="25" />
<hkern u1="&#xd8;" u2="&#xc6;" k="35" />
<hkern u1="&#xd8;" u2="&#x7d;" k="68" />
<hkern u1="&#xd8;" u2="]" k="70" />
<hkern u1="&#xd8;" u2="X" k="43" />
<hkern u1="&#xd8;" u2="V" k="27" />
<hkern u1="&#xd8;" u2="&#x3f;" k="25" />
<hkern u1="&#xd9;" u2="&#xc6;" k="23" />
<hkern u1="&#xda;" u2="&#xc6;" k="23" />
<hkern u1="&#xdb;" u2="&#xc6;" k="23" />
<hkern u1="&#xdc;" u2="&#xc6;" k="23" />
<hkern u1="&#xdd;" u2="&#xff;" k="82" />
<hkern u1="&#xdd;" u2="&#xf0;" k="98" />
<hkern u1="&#xdd;" u2="&#xef;" k="-55" />
<hkern u1="&#xdd;" u2="&#xec;" k="-135" />
<hkern u1="&#xdd;" u2="&#xeb;" k="145" />
<hkern u1="&#xdd;" u2="&#xe4;" k="135" />
<hkern u1="&#xdd;" u2="&#xe3;" k="135" />
<hkern u1="&#xdd;" u2="&#xc6;" k="96" />
<hkern u1="&#xdd;" u2="&#xae;" k="49" />
<hkern u1="&#xdd;" u2="x" k="90" />
<hkern u1="&#xdd;" u2="v" k="94" />
<hkern u1="&#xdd;" u2="f" k="31" />
<hkern u1="&#xdd;" u2="&#x40;" k="76" />
<hkern u1="&#xdd;" u2="&#x2f;" k="131" />
<hkern u1="&#xdd;" u2="&#x26;" k="88" />
<hkern u1="&#xde;" u2="&#x2122;" k="20" />
<hkern u1="&#xde;" u2="&#x2026;" k="82" />
<hkern u1="&#xde;" u2="&#x201e;" k="82" />
<hkern u1="&#xde;" u2="&#x201a;" k="82" />
<hkern u1="&#xde;" u2="&#x178;" k="86" />
<hkern u1="&#xde;" u2="&#xdd;" k="86" />
<hkern u1="&#xde;" u2="&#xc6;" k="53" />
<hkern u1="&#xde;" u2="&#xc5;" k="43" />
<hkern u1="&#xde;" u2="&#xc4;" k="43" />
<hkern u1="&#xde;" u2="&#xc3;" k="43" />
<hkern u1="&#xde;" u2="&#xc2;" k="43" />
<hkern u1="&#xde;" u2="&#xc1;" k="43" />
<hkern u1="&#xde;" u2="&#xc0;" k="43" />
<hkern u1="&#xde;" u2="&#x7d;" k="84" />
<hkern u1="&#xde;" u2="]" k="86" />
<hkern u1="&#xde;" u2="\" k="29" />
<hkern u1="&#xde;" u2="Z" k="59" />
<hkern u1="&#xde;" u2="Y" k="86" />
<hkern u1="&#xde;" u2="X" k="92" />
<hkern u1="&#xde;" u2="W" k="14" />
<hkern u1="&#xde;" u2="V" k="35" />
<hkern u1="&#xde;" u2="T" k="141" />
<hkern u1="&#xde;" u2="J" k="70" />
<hkern u1="&#xde;" u2="A" k="43" />
<hkern u1="&#xde;" u2="&#x3f;" k="59" />
<hkern u1="&#xde;" u2="&#x2f;" k="33" />
<hkern u1="&#xde;" u2="&#x2e;" k="82" />
<hkern u1="&#xde;" u2="&#x2c;" k="82" />
<hkern u1="&#xde;" u2="&#x29;" k="29" />
<hkern u1="&#xdf;" u2="&#x2122;" k="27" />
<hkern u1="&#xdf;" u2="&#x201d;" k="33" />
<hkern u1="&#xdf;" u2="&#x201c;" k="33" />
<hkern u1="&#xdf;" u2="&#x2019;" k="33" />
<hkern u1="&#xdf;" u2="&#x2018;" k="33" />
<hkern u1="&#xdf;" u2="&#x178;" k="92" />
<hkern u1="&#xdf;" u2="&#xff;" k="39" />
<hkern u1="&#xdf;" u2="&#xfd;" k="39" />
<hkern u1="&#xdf;" u2="&#xdd;" k="92" />
<hkern u1="&#xdf;" u2="&#xc6;" k="10" />
<hkern u1="&#xdf;" u2="&#xae;" k="27" />
<hkern u1="&#xdf;" u2="&#x7d;" k="57" />
<hkern u1="&#xdf;" u2="z" k="8" />
<hkern u1="&#xdf;" u2="y" k="39" />
<hkern u1="&#xdf;" u2="x" k="27" />
<hkern u1="&#xdf;" u2="w" k="31" />
<hkern u1="&#xdf;" u2="v" k="39" />
<hkern u1="&#xdf;" u2="t" k="23" />
<hkern u1="&#xdf;" u2="s" k="8" />
<hkern u1="&#xdf;" u2="g" k="12" />
<hkern u1="&#xdf;" u2="f" k="10" />
<hkern u1="&#xdf;" u2="]" k="57" />
<hkern u1="&#xdf;" u2="\" k="35" />
<hkern u1="&#xdf;" u2="Y" k="92" />
<hkern u1="&#xdf;" u2="X" k="12" />
<hkern u1="&#xdf;" u2="W" k="45" />
<hkern u1="&#xdf;" u2="V" k="68" />
<hkern u1="&#xdf;" u2="T" k="80" />
<hkern u1="&#xdf;" u2="S" k="18" />
<hkern u1="&#xdf;" u2="J" k="70" />
<hkern u1="&#xdf;" u2="&#x3f;" k="31" />
<hkern u1="&#xdf;" u2="&#x2a;" k="35" />
<hkern u1="&#xdf;" u2="&#x27;" k="31" />
<hkern u1="&#xdf;" u2="&#x22;" k="31" />
<hkern u1="&#xe0;" u2="&#x2122;" k="20" />
<hkern u1="&#xe0;" u2="&#x7d;" k="31" />
<hkern u1="&#xe0;" u2="v" k="8" />
<hkern u1="&#xe0;" u2="]" k="39" />
<hkern u1="&#xe0;" u2="\" k="61" />
<hkern u1="&#xe0;" u2="V" k="47" />
<hkern u1="&#xe0;" u2="&#x3f;" k="33" />
<hkern u1="&#xe1;" u2="&#x2122;" k="20" />
<hkern u1="&#xe1;" u2="&#x7d;" k="31" />
<hkern u1="&#xe1;" u2="v" k="8" />
<hkern u1="&#xe1;" u2="]" k="39" />
<hkern u1="&#xe1;" u2="\" k="61" />
<hkern u1="&#xe1;" u2="V" k="47" />
<hkern u1="&#xe1;" u2="&#x3f;" k="33" />
<hkern u1="&#xe2;" u2="&#x2122;" k="20" />
<hkern u1="&#xe2;" u2="&#x7d;" k="31" />
<hkern u1="&#xe2;" u2="v" k="8" />
<hkern u1="&#xe2;" u2="]" k="39" />
<hkern u1="&#xe2;" u2="\" k="61" />
<hkern u1="&#xe2;" u2="V" k="47" />
<hkern u1="&#xe2;" u2="&#x3f;" k="33" />
<hkern u1="&#xe3;" u2="&#x2122;" k="20" />
<hkern u1="&#xe3;" u2="&#x7d;" k="31" />
<hkern u1="&#xe3;" u2="v" k="8" />
<hkern u1="&#xe3;" u2="]" k="39" />
<hkern u1="&#xe3;" u2="\" k="61" />
<hkern u1="&#xe3;" u2="V" k="47" />
<hkern u1="&#xe3;" u2="&#x3f;" k="33" />
<hkern u1="&#xe4;" u2="&#x2122;" k="20" />
<hkern u1="&#xe4;" u2="&#x7d;" k="31" />
<hkern u1="&#xe4;" u2="v" k="8" />
<hkern u1="&#xe4;" u2="]" k="39" />
<hkern u1="&#xe4;" u2="\" k="61" />
<hkern u1="&#xe4;" u2="V" k="47" />
<hkern u1="&#xe4;" u2="&#x3f;" k="33" />
<hkern u1="&#xe5;" u2="&#x2122;" k="20" />
<hkern u1="&#xe5;" u2="&#x7d;" k="31" />
<hkern u1="&#xe5;" u2="v" k="8" />
<hkern u1="&#xe5;" u2="]" k="39" />
<hkern u1="&#xe5;" u2="\" k="61" />
<hkern u1="&#xe5;" u2="V" k="47" />
<hkern u1="&#xe5;" u2="&#x3f;" k="33" />
<hkern u1="&#xe6;" u2="&#x2122;" k="31" />
<hkern u1="&#xe6;" u2="&#xc6;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="80" />
<hkern u1="&#xe6;" u2="x" k="14" />
<hkern u1="&#xe6;" u2="v" k="18" />
<hkern u1="&#xe6;" u2="]" k="84" />
<hkern u1="&#xe6;" u2="\" k="66" />
<hkern u1="&#xe6;" u2="V" k="72" />
<hkern u1="&#xe6;" u2="&#x3f;" k="53" />
<hkern u1="&#xe7;" u2="&#xf0;" k="29" />
<hkern u1="&#xe7;" u2="&#x7d;" k="55" />
<hkern u1="&#xe7;" u2="]" k="66" />
<hkern u1="&#xe7;" u2="V" k="25" />
<hkern u1="&#xe7;" u2="&#x3f;" k="31" />
<hkern u1="&#xe8;" u2="&#x2122;" k="31" />
<hkern u1="&#xe8;" u2="&#xc6;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="80" />
<hkern u1="&#xe8;" u2="x" k="14" />
<hkern u1="&#xe8;" u2="v" k="18" />
<hkern u1="&#xe8;" u2="]" k="84" />
<hkern u1="&#xe8;" u2="\" k="66" />
<hkern u1="&#xe8;" u2="V" k="72" />
<hkern u1="&#xe8;" u2="&#x3f;" k="53" />
<hkern u1="&#xe9;" u2="&#x2122;" k="31" />
<hkern u1="&#xe9;" u2="&#xc6;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="80" />
<hkern u1="&#xe9;" u2="x" k="14" />
<hkern u1="&#xe9;" u2="v" k="18" />
<hkern u1="&#xe9;" u2="]" k="84" />
<hkern u1="&#xe9;" u2="\" k="66" />
<hkern u1="&#xe9;" u2="V" k="72" />
<hkern u1="&#xe9;" u2="&#x3f;" k="53" />
<hkern u1="&#xea;" u2="&#x2122;" k="31" />
<hkern u1="&#xea;" u2="&#xc6;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="80" />
<hkern u1="&#xea;" u2="x" k="14" />
<hkern u1="&#xea;" u2="v" k="18" />
<hkern u1="&#xea;" u2="]" k="84" />
<hkern u1="&#xea;" u2="\" k="66" />
<hkern u1="&#xea;" u2="V" k="72" />
<hkern u1="&#xea;" u2="&#x3f;" k="53" />
<hkern u1="&#xeb;" u2="&#x2122;" k="31" />
<hkern u1="&#xeb;" u2="&#xc6;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="80" />
<hkern u1="&#xeb;" u2="x" k="14" />
<hkern u1="&#xeb;" u2="v" k="18" />
<hkern u1="&#xeb;" u2="]" k="84" />
<hkern u1="&#xeb;" u2="\" k="66" />
<hkern u1="&#xeb;" u2="V" k="72" />
<hkern u1="&#xeb;" u2="&#x3f;" k="53" />
<hkern u1="&#xed;" u2="&#x7d;" k="-66" />
<hkern u1="&#xed;" u2="]" k="-63" />
<hkern u1="&#xed;" u2="\" k="-82" />
<hkern u1="&#xed;" u2="&#x3f;" k="-47" />
<hkern u1="&#xed;" u2="&#x29;" k="-47" />
<hkern u1="&#xed;" u2="&#x27;" k="-14" />
<hkern u1="&#xed;" u2="&#x22;" k="-14" />
<hkern u1="&#xee;" u2="&#x2a;" k="-18" />
<hkern u1="&#xef;" u2="\" k="-20" />
<hkern u1="&#xf0;" u2="&#x2122;" k="23" />
<hkern u1="&#xf0;" u2="&#x2026;" k="31" />
<hkern u1="&#xf0;" u2="&#x201e;" k="31" />
<hkern u1="&#xf0;" u2="&#x201d;" k="18" />
<hkern u1="&#xf0;" u2="&#x201c;" k="16" />
<hkern u1="&#xf0;" u2="&#x201a;" k="31" />
<hkern u1="&#xf0;" u2="&#x2019;" k="18" />
<hkern u1="&#xf0;" u2="&#x2018;" k="16" />
<hkern u1="&#xf0;" u2="&#x178;" k="98" />
<hkern u1="&#xf0;" u2="&#xff;" k="16" />
<hkern u1="&#xf0;" u2="&#xfd;" k="16" />
<hkern u1="&#xf0;" u2="&#xde;" k="10" />
<hkern u1="&#xf0;" u2="&#xdd;" k="98" />
<hkern u1="&#xf0;" u2="&#xd1;" k="10" />
<hkern u1="&#xf0;" u2="&#xd0;" k="10" />
<hkern u1="&#xf0;" u2="&#xcf;" k="10" />
<hkern u1="&#xf0;" u2="&#xce;" k="10" />
<hkern u1="&#xf0;" u2="&#xcd;" k="10" />
<hkern u1="&#xf0;" u2="&#xcc;" k="10" />
<hkern u1="&#xf0;" u2="&#xcb;" k="10" />
<hkern u1="&#xf0;" u2="&#xca;" k="10" />
<hkern u1="&#xf0;" u2="&#xc9;" k="10" />
<hkern u1="&#xf0;" u2="&#xc8;" k="10" />
<hkern u1="&#xf0;" u2="&#xc6;" k="23" />
<hkern u1="&#xf0;" u2="&#xc5;" k="18" />
<hkern u1="&#xf0;" u2="&#xc4;" k="18" />
<hkern u1="&#xf0;" u2="&#xc3;" k="18" />
<hkern u1="&#xf0;" u2="&#xc2;" k="18" />
<hkern u1="&#xf0;" u2="&#xc1;" k="18" />
<hkern u1="&#xf0;" u2="&#xc0;" k="18" />
<hkern u1="&#xf0;" u2="&#x7d;" k="53" />
<hkern u1="&#xf0;" u2="y" k="16" />
<hkern u1="&#xf0;" u2="x" k="12" />
<hkern u1="&#xf0;" u2="w" k="14" />
<hkern u1="&#xf0;" u2="v" k="14" />
<hkern u1="&#xf0;" u2="]" k="55" />
<hkern u1="&#xf0;" u2="\" k="39" />
<hkern u1="&#xf0;" u2="Z" k="27" />
<hkern u1="&#xf0;" u2="Y" k="98" />
<hkern u1="&#xf0;" u2="X" k="59" />
<hkern u1="&#xf0;" u2="W" k="29" />
<hkern u1="&#xf0;" u2="V" k="53" />
<hkern u1="&#xf0;" u2="T" k="143" />
<hkern u1="&#xf0;" u2="S" k="12" />
<hkern u1="&#xf0;" u2="R" k="10" />
<hkern u1="&#xf0;" u2="P" k="10" />
<hkern u1="&#xf0;" u2="N" k="10" />
<hkern u1="&#xf0;" u2="M" k="10" />
<hkern u1="&#xf0;" u2="L" k="10" />
<hkern u1="&#xf0;" u2="K" k="10" />
<hkern u1="&#xf0;" u2="J" k="72" />
<hkern u1="&#xf0;" u2="I" k="10" />
<hkern u1="&#xf0;" u2="H" k="10" />
<hkern u1="&#xf0;" u2="F" k="10" />
<hkern u1="&#xf0;" u2="E" k="10" />
<hkern u1="&#xf0;" u2="D" k="10" />
<hkern u1="&#xf0;" u2="B" k="10" />
<hkern u1="&#xf0;" u2="A" k="18" />
<hkern u1="&#xf0;" u2="&#x3f;" k="43" />
<hkern u1="&#xf0;" u2="&#x2e;" k="31" />
<hkern u1="&#xf0;" u2="&#x2c;" k="31" />
<hkern u1="&#xf0;" u2="&#x29;" k="27" />
<hkern u1="&#xf1;" u2="&#x2122;" k="35" />
<hkern u1="&#xf1;" u2="&#x7d;" k="82" />
<hkern u1="&#xf1;" u2="v" k="12" />
<hkern u1="&#xf1;" u2="]" k="88" />
<hkern u1="&#xf1;" u2="\" k="68" />
<hkern u1="&#xf1;" u2="V" k="63" />
<hkern u1="&#xf1;" u2="&#x3f;" k="55" />
<hkern u1="&#xf2;" u2="&#x2122;" k="33" />
<hkern u1="&#xf2;" u2="&#xc6;" k="14" />
<hkern u1="&#xf2;" u2="&#x7d;" k="88" />
<hkern u1="&#xf2;" u2="x" k="29" />
<hkern u1="&#xf2;" u2="v" k="20" />
<hkern u1="&#xf2;" u2="]" k="94" />
<hkern u1="&#xf2;" u2="\" k="72" />
<hkern u1="&#xf2;" u2="X" k="35" />
<hkern u1="&#xf2;" u2="V" k="70" />
<hkern u1="&#xf2;" u2="&#x3f;" k="59" />
<hkern u1="&#xf2;" u2="&#x29;" k="35" />
<hkern u1="&#xf3;" u2="&#x2122;" k="33" />
<hkern u1="&#xf3;" u2="&#xc6;" k="14" />
<hkern u1="&#xf3;" u2="&#x7d;" k="88" />
<hkern u1="&#xf3;" u2="x" k="29" />
<hkern u1="&#xf3;" u2="v" k="20" />
<hkern u1="&#xf3;" u2="]" k="94" />
<hkern u1="&#xf3;" u2="\" k="72" />
<hkern u1="&#xf3;" u2="X" k="35" />
<hkern u1="&#xf3;" u2="V" k="70" />
<hkern u1="&#xf3;" u2="&#x3f;" k="59" />
<hkern u1="&#xf3;" u2="&#x29;" k="35" />
<hkern u1="&#xf4;" u2="&#x2122;" k="33" />
<hkern u1="&#xf4;" u2="&#xc6;" k="14" />
<hkern u1="&#xf4;" u2="&#x7d;" k="88" />
<hkern u1="&#xf4;" u2="x" k="29" />
<hkern u1="&#xf4;" u2="v" k="20" />
<hkern u1="&#xf4;" u2="]" k="94" />
<hkern u1="&#xf4;" u2="\" k="72" />
<hkern u1="&#xf4;" u2="X" k="35" />
<hkern u1="&#xf4;" u2="V" k="70" />
<hkern u1="&#xf4;" u2="&#x3f;" k="59" />
<hkern u1="&#xf4;" u2="&#x29;" k="35" />
<hkern u1="&#xf5;" u2="&#x2122;" k="33" />
<hkern u1="&#xf5;" u2="&#xc6;" k="14" />
<hkern u1="&#xf5;" u2="&#x7d;" k="88" />
<hkern u1="&#xf5;" u2="x" k="29" />
<hkern u1="&#xf5;" u2="v" k="20" />
<hkern u1="&#xf5;" u2="]" k="94" />
<hkern u1="&#xf5;" u2="\" k="72" />
<hkern u1="&#xf5;" u2="X" k="35" />
<hkern u1="&#xf5;" u2="V" k="70" />
<hkern u1="&#xf5;" u2="&#x3f;" k="59" />
<hkern u1="&#xf5;" u2="&#x29;" k="35" />
<hkern u1="&#xf6;" u2="&#x2122;" k="33" />
<hkern u1="&#xf6;" u2="&#xc6;" k="14" />
<hkern u1="&#xf6;" u2="&#x7d;" k="88" />
<hkern u1="&#xf6;" u2="x" k="29" />
<hkern u1="&#xf6;" u2="v" k="20" />
<hkern u1="&#xf6;" u2="]" k="94" />
<hkern u1="&#xf6;" u2="\" k="72" />
<hkern u1="&#xf6;" u2="X" k="35" />
<hkern u1="&#xf6;" u2="V" k="70" />
<hkern u1="&#xf6;" u2="&#x3f;" k="59" />
<hkern u1="&#xf6;" u2="&#x29;" k="35" />
<hkern u1="&#xf8;" u2="&#x2122;" k="33" />
<hkern u1="&#xf8;" u2="&#xc6;" k="14" />
<hkern u1="&#xf8;" u2="&#x7d;" k="88" />
<hkern u1="&#xf8;" u2="x" k="29" />
<hkern u1="&#xf8;" u2="v" k="20" />
<hkern u1="&#xf8;" u2="]" k="94" />
<hkern u1="&#xf8;" u2="\" k="72" />
<hkern u1="&#xf8;" u2="X" k="35" />
<hkern u1="&#xf8;" u2="V" k="70" />
<hkern u1="&#xf8;" u2="&#x3f;" k="59" />
<hkern u1="&#xf8;" u2="&#x29;" k="35" />
<hkern u1="&#xf9;" u2="&#x2122;" k="27" />
<hkern u1="&#xf9;" u2="&#x7d;" k="78" />
<hkern u1="&#xf9;" u2="]" k="86" />
<hkern u1="&#xf9;" u2="\" k="41" />
<hkern u1="&#xf9;" u2="V" k="61" />
<hkern u1="&#xf9;" u2="&#x3f;" k="43" />
<hkern u1="&#xfa;" u2="&#x2122;" k="27" />
<hkern u1="&#xfa;" u2="&#x7d;" k="78" />
<hkern u1="&#xfa;" u2="]" k="86" />
<hkern u1="&#xfa;" u2="\" k="41" />
<hkern u1="&#xfa;" u2="V" k="61" />
<hkern u1="&#xfa;" u2="&#x3f;" k="43" />
<hkern u1="&#xfb;" u2="&#x2122;" k="27" />
<hkern u1="&#xfb;" u2="&#x7d;" k="78" />
<hkern u1="&#xfb;" u2="]" k="86" />
<hkern u1="&#xfb;" u2="\" k="41" />
<hkern u1="&#xfb;" u2="V" k="61" />
<hkern u1="&#xfb;" u2="&#x3f;" k="43" />
<hkern u1="&#xfc;" u2="&#x2122;" k="27" />
<hkern u1="&#xfc;" u2="&#x7d;" k="78" />
<hkern u1="&#xfc;" u2="]" k="86" />
<hkern u1="&#xfc;" u2="\" k="41" />
<hkern u1="&#xfc;" u2="V" k="61" />
<hkern u1="&#xfc;" u2="&#x3f;" k="43" />
<hkern u1="&#xfd;" u2="&#xf0;" k="29" />
<hkern u1="&#xfd;" u2="&#xc6;" k="47" />
<hkern u1="&#xfd;" u2="&#x7d;" k="68" />
<hkern u1="&#xfd;" u2="]" k="74" />
<hkern u1="&#xfd;" u2="X" k="59" />
<hkern u1="&#xfd;" u2="V" k="18" />
<hkern u1="&#xfd;" u2="&#x3f;" k="31" />
<hkern u1="&#xfd;" u2="&#x2f;" k="47" />
<hkern u1="&#xfe;" u2="&#x2122;" k="37" />
<hkern u1="&#xfe;" u2="&#xc6;" k="16" />
<hkern u1="&#xfe;" u2="&#x7d;" k="88" />
<hkern u1="&#xfe;" u2="x" k="29" />
<hkern u1="&#xfe;" u2="v" k="18" />
<hkern u1="&#xfe;" u2="]" k="92" />
<hkern u1="&#xfe;" u2="\" k="68" />
<hkern u1="&#xfe;" u2="X" k="39" />
<hkern u1="&#xfe;" u2="V" k="70" />
<hkern u1="&#xfe;" u2="&#x3f;" k="63" />
<hkern u1="&#xfe;" u2="&#x2a;" k="16" />
<hkern u1="&#xfe;" u2="&#x29;" k="35" />
<hkern u1="&#xff;" u2="&#xf0;" k="29" />
<hkern u1="&#xff;" u2="&#xc6;" k="47" />
<hkern u1="&#xff;" u2="&#x7d;" k="68" />
<hkern u1="&#xff;" u2="]" k="74" />
<hkern u1="&#xff;" u2="X" k="59" />
<hkern u1="&#xff;" u2="V" k="18" />
<hkern u1="&#xff;" u2="&#x3f;" k="31" />
<hkern u1="&#xff;" u2="&#x2f;" k="47" />
<hkern u1="&#x152;" u2="&#xf0;" k="18" />
<hkern u1="&#x152;" u2="&#xec;" k="-66" />
<hkern u1="&#x152;" u2="v" k="39" />
<hkern u1="&#x152;" u2="f" k="10" />
<hkern u1="&#x153;" u2="&#x2122;" k="31" />
<hkern u1="&#x153;" u2="&#xc6;" k="10" />
<hkern u1="&#x153;" u2="&#x7d;" k="80" />
<hkern u1="&#x153;" u2="x" k="14" />
<hkern u1="&#x153;" u2="v" k="18" />
<hkern u1="&#x153;" u2="]" k="84" />
<hkern u1="&#x153;" u2="\" k="66" />
<hkern u1="&#x153;" u2="V" k="72" />
<hkern u1="&#x153;" u2="&#x3f;" k="53" />
<hkern u1="&#x178;" u2="&#xff;" k="82" />
<hkern u1="&#x178;" u2="&#xf0;" k="98" />
<hkern u1="&#x178;" u2="&#xef;" k="-55" />
<hkern u1="&#x178;" u2="&#xec;" k="-135" />
<hkern u1="&#x178;" u2="&#xeb;" k="145" />
<hkern u1="&#x178;" u2="&#xe4;" k="135" />
<hkern u1="&#x178;" u2="&#xe3;" k="135" />
<hkern u1="&#x178;" u2="&#xc6;" k="96" />
<hkern u1="&#x178;" u2="&#xae;" k="49" />
<hkern u1="&#x178;" u2="x" k="90" />
<hkern u1="&#x178;" u2="v" k="94" />
<hkern u1="&#x178;" u2="f" k="31" />
<hkern u1="&#x178;" u2="&#x40;" k="76" />
<hkern u1="&#x178;" u2="&#x2f;" k="131" />
<hkern u1="&#x178;" u2="&#x26;" k="88" />
<hkern u1="&#x2013;" u2="&#xc6;" k="47" />
<hkern u1="&#x2013;" u2="x" k="74" />
<hkern u1="&#x2013;" u2="v" k="31" />
<hkern u1="&#x2013;" u2="f" k="25" />
<hkern u1="&#x2013;" u2="X" k="88" />
<hkern u1="&#x2013;" u2="V" k="72" />
<hkern u1="&#x2014;" u2="&#xc6;" k="47" />
<hkern u1="&#x2014;" u2="x" k="74" />
<hkern u1="&#x2014;" u2="v" k="31" />
<hkern u1="&#x2014;" u2="f" k="25" />
<hkern u1="&#x2014;" u2="X" k="88" />
<hkern u1="&#x2014;" u2="V" k="72" />
<hkern u1="&#x2018;" u2="&#xf0;" k="33" />
<hkern u1="&#x2018;" u2="&#xec;" k="-53" />
<hkern u1="&#x2018;" u2="&#xc6;" k="143" />
<hkern u1="&#x2019;" u2="&#xf0;" k="33" />
<hkern u1="&#x2019;" u2="&#xec;" k="-59" />
<hkern u1="&#x2019;" u2="&#xc6;" k="145" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x2f;" k="141" />
<hkern u1="&#x2019;" u2="&#x26;" k="76" />
<hkern u1="&#x201a;" u2="v" k="88" />
<hkern u1="&#x201a;" u2="f" k="18" />
<hkern u1="&#x201a;" u2="V" k="125" />
<hkern u1="&#x201c;" u2="&#xf0;" k="33" />
<hkern u1="&#x201c;" u2="&#xec;" k="-53" />
<hkern u1="&#x201c;" u2="&#xc6;" k="143" />
<hkern u1="&#x201d;" u2="&#xf0;" k="33" />
<hkern u1="&#x201d;" u2="&#xec;" k="-59" />
<hkern u1="&#x201d;" u2="&#xc6;" k="145" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x2f;" k="141" />
<hkern u1="&#x201d;" u2="&#x26;" k="76" />
<hkern u1="&#x201e;" u2="v" k="88" />
<hkern u1="&#x201e;" u2="f" k="18" />
<hkern u1="&#x201e;" u2="V" k="125" />
<hkern u1="&#x2039;" u2="V" k="51" />
<hkern u1="&#x203a;" u2="&#xc6;" k="23" />
<hkern u1="&#x203a;" u2="x" k="59" />
<hkern u1="&#x203a;" u2="v" k="27" />
<hkern u1="&#x203a;" u2="X" k="55" />
<hkern u1="&#x203a;" u2="V" k="63" />
<hkern u1="&#x2122;" u2="&#xee;" k="-16" />
<hkern u1="&#x2122;" u2="&#xc6;" k="82" />
<hkern u1="&#x2122;" u2="&#xc5;" k="66" />
<hkern u1="&#x2122;" u2="&#xc4;" k="66" />
<hkern u1="&#x2122;" u2="&#xc3;" k="66" />
<hkern u1="&#x2122;" u2="&#xc2;" k="66" />
<hkern u1="&#x2122;" u2="&#xc1;" k="66" />
<hkern u1="&#x2122;" u2="&#xc0;" k="66" />
<hkern u1="&#x2122;" u2="Z" k="16" />
<hkern u1="&#x2122;" u2="J" k="59" />
<hkern u1="&#x2122;" u2="A" k="66" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="D,Eth" 	g2="J" 	k="59" />
<hkern g1="D,Eth" 	g2="T" 	k="72" />
<hkern g1="D,Eth" 	g2="W" 	k="10" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="D,Eth" 	g2="Z" 	k="25" />
<hkern g1="D,Eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="G" 	g2="J" 	k="33" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="G" 	g2="g" 	k="12" />
<hkern g1="G" 	g2="t" 	k="18" />
<hkern g1="G" 	g2="w" 	k="16" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="J" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d,q" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="J" 	g2="d,q" 	k="10" />
<hkern g1="J" 	g2="g" 	k="16" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="45" />
<hkern g1="K" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="K" 	g2="d,q" 	k="29" />
<hkern g1="K" 	g2="g" 	k="18" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="88" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="K" 	g2="t" 	k="23" />
<hkern g1="K" 	g2="w" 	k="57" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="55" />
<hkern g1="L" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="L" 	g2="T" 	k="219" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="L" 	g2="W" 	k="92" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="186" />
<hkern g1="L" 	g2="d,q" 	k="18" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="133" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="184" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="203" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="203" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="203" />
<hkern g1="L" 	g2="t" 	k="45" />
<hkern g1="L" 	g2="w" 	k="96" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="111" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="49" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="55" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="59" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="R" 	g2="J" 	k="16" />
<hkern g1="R" 	g2="T" 	k="33" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="R" 	g2="d,q" 	k="20" />
<hkern g1="R" 	g2="g" 	k="18" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="S" 	g2="J" 	k="47" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="S" 	g2="g" 	k="16" />
<hkern g1="S" 	g2="t" 	k="23" />
<hkern g1="S" 	g2="w" 	k="23" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="23" />
<hkern g1="S" 	g2="z" 	k="10" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="T" 	g2="J" 	k="66" />
<hkern g1="T" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="T" 	g2="d,q" 	k="217" />
<hkern g1="T" 	g2="g" 	k="242" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="133" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="141" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="217" />
<hkern g1="T" 	g2="t" 	k="76" />
<hkern g1="T" 	g2="w" 	k="166" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="131" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="221" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="223" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="109" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="139" />
<hkern g1="T" 	g2="z" 	k="197" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="205" />
<hkern g1="T" 	g2="b,thorn" 	k="25" />
<hkern g1="T" 	g2="colon,semicolon" 	k="121" />
<hkern g1="T" 	g2="s" 	k="207" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d,q" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="23" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="10" />
<hkern g1="W" 	g2="J" 	k="57" />
<hkern g1="W" 	g2="d,q" 	k="45" />
<hkern g1="W" 	g2="g" 	k="53" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="45" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="33" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="49" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="84" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="W" 	g2="s" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="152" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="147" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="156" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="94" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="127" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="96" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="145" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="Z" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="Z" 	g2="d,q" 	k="27" />
<hkern g1="Z" 	g2="g" 	k="33" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="Z" 	g2="t" 	k="12" />
<hkern g1="Z" 	g2="w" 	k="31" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="12" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="176" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="125" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="b,p,thorn" 	g2="J" 	k="74" />
<hkern g1="b,p,thorn" 	g2="S" 	k="18" />
<hkern g1="b,p,thorn" 	g2="T" 	k="219" />
<hkern g1="b,p,thorn" 	g2="W" 	k="43" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="b,p,thorn" 	g2="t" 	k="10" />
<hkern g1="b,p,thorn" 	g2="w" 	k="16" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="16" />
<hkern g1="b,p,thorn" 	g2="z" 	k="10" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="10" />
<hkern g1="c,ccedilla" 	g2="J" 	k="23" />
<hkern g1="c,ccedilla" 	g2="T" 	k="246" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="c,ccedilla" 	g2="d,q" 	k="18" />
<hkern g1="c,ccedilla" 	g2="g" 	k="12" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="88" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="115" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="c,ccedilla" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="colon,semicolon" 	g2="T" 	k="123" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="96" />
<hkern g1="d" 	g2="J" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="213" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="178" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="10" />
<hkern g1="g" 	g2="T" 	k="182" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="g" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="133" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="121" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="121" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="125" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="63" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="51" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="133" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="182" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="188" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="72" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="76" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="68" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="141" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="156" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="160" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="170" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="68" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="104" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis" 	g2="J" 	k="57" />
<hkern g1="k" 	g2="C,Ccedilla" 	k="10" />
<hkern g1="k" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="k" 	g2="T" 	k="182" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="k" 	g2="d,q" 	k="33" />
<hkern g1="k" 	g2="g" 	k="25" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="92" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="l" 	g2="J" 	k="57" />
<hkern g1="h,m,n,ntilde" 	g2="J" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="12" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="227" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="39" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="145" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="74" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="221" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="145" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="39" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T" 	k="139" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W" 	k="84" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="158" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="115" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="328" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="328" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="332" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t" 	k="45" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="88" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="80" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="121" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="344" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="33" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="207" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="176" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="139" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="121" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="346" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="49" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="188" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="172" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="125" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="334" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="18" />
<hkern g1="r" 	g2="J" 	k="76" />
<hkern g1="r" 	g2="T" 	k="176" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="r" 	g2="d,q" 	k="43" />
<hkern g1="r" 	g2="g" 	k="23" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="109" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="76" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="r" 	g2="Z" 	k="43" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="119" />
<hkern g1="s" 	g2="J" 	k="74" />
<hkern g1="s" 	g2="T" 	k="207" />
<hkern g1="s" 	g2="W" 	k="27" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="s" 	g2="g" 	k="16" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="s" 	g2="w" 	k="14" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="t" 	g2="T" 	k="190" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="53" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="88" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="80" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="59" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="57" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="225" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="33" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="w" 	g2="J" 	k="70" />
<hkern g1="w" 	g2="T" 	k="168" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="98" />
<hkern g1="w" 	g2="d,q" 	k="18" />
<hkern g1="w" 	g2="g" 	k="18" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="w" 	g2="Z" 	k="41" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="w" 	g2="s" 	k="12" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="72" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="162" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="43" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="88" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="14" />
<hkern g1="z" 	g2="J" 	k="23" />
<hkern g1="z" 	g2="T" 	k="197" />
<hkern g1="z" 	g2="W" 	k="10" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="z" 	g2="d,q" 	k="12" />
<hkern g1="z" 	g2="g" 	k="8" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="88" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
</font>
</defs></svg> 