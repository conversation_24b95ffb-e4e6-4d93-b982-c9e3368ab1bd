!function(a){"use strict";function e(e){var f,g,h;if(!a.find.tokenize)return e.isCombinatorial=!0,e.isFraternal=!0,e.isComplex=!0,void 0;for(e.isCombinatorial=!1,e.isFraternal=!1,e.isComplex=!1,f=a.find.tokenize(e.selector),g=0;g<f.length;g++)for(h=0;h<f[g].length;h++)-1!=b.indexOf(f[g][h].type)&&(e.isCombinatorial=!0),-1!=c.indexOf(f[g][h].type)&&(e.isFraternal=!0),-1!=d.indexOf(f[g][h].type)&&(e.isComplex=!0)}var f,g,b=[" ",">","+","~"],c=["+","~"],d=["ATTR","PSEUDO","ID","CLASS"];Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector),f=function(a,b,c){this.selector=a.trim(),this.callback=b,this.options=c,e(this)},g=[],g.initialize=function(b,c,d){var h,i,j,e=[],g=function(){-1==e.indexOf(this)&&(e.push(this),a(this).each(c))};return a(d.target).find(b).each(g),h=new f(b,g,d),this.push(h),i=new MutationObserver(function(b){var d,e,f,c=[];for(d=0;d<b.length;d++)if("attributes"==b[d].type&&(b[d].target.matches(h.selector)&&c.push(b[d].target),h.isFraternal?c.push.apply(c,b[d].target.parentElement.querySelectorAll(h.selector)):c.push.apply(c,b[d].target.querySelectorAll(h.selector))),"childList"==b[d].type)for(e=0;e<b[d].addedNodes.length;e++)b[d].addedNodes[e]instanceof Element&&(b[d].addedNodes[e].matches(h.selector)&&c.push(b[d].addedNodes[e]),h.isFraternal?c.push.apply(c,b[d].addedNodes[e].parentElement.querySelectorAll(h.selector)):c.push.apply(c,b[d].addedNodes[e].querySelectorAll(h.selector)));for(f=0;f<c.length;f++)a(c[f]).each(h.callback)}),j={childList:!0,subtree:!0,attributes:h.isComplex},i.observe(d.target,d.observer||j),i},a.fn.initialize=function(b,c){return g.initialize(this.selector,b,a.extend({},a.initialize.defaults,c))},a.initialize=function(b,c,d){return g.initialize(b,c,a.extend({},a.initialize.defaults,d))},a.initialize.defaults={target:document.documentElement,observer:null}}(jQuery);