<template>
  <view class="mt-2.5 p-4 bg-white rounded-xl">
    <view class="flex justify-between items-center mb-4">
      <text class="text-base font-bold text-gray-800">{{ title }}</text>
      <view class="flex items-center" @click="onViewMore">
        <text class="text-xs text-gray-500 mr-0.5">查看更多</text>
        <text class="i-carbon-chevron-right text-sm"></text>
      </view>
    </view>

    <view class="flex flex-wrap mx-[-8px]">
      <view
        v-for="(product, index) in products"
        :key="index"
        class="w-[calc(50%-16px)] mx-2 mb-4 rounded-lg overflow-hidden bg-white shadow-sm"
        @click="handleProductClick(product)"
      >
        <image :src="product.imageUrl" mode="aspectFill" class="w-full h-40 rounded-t-lg" />
        <view class="p-2.5">
          <text
            class="text-sm text-gray-800 overflow-hidden text-ellipsis line-clamp-2 leading-snug h-9 mb-1"
          >
            {{ product.title }}
          </text>
          <view class="flex items-center mb-1">
            <text class="text-base font-bold text-orange-500 mr-1.5">
              ¥{{ product.price.toFixed(2) }}
            </text>
            <text class="text-xs text-gray-400 line-through" v-if="product.originalPrice">
              ¥{{ product.originalPrice.toFixed(2) }}
            </text>
          </view>
          <view class="flex flex-wrap" v-if="product.tags && product.tags.length">
            <wd-tag
              v-for="(tag, tagIndex) in product.tags"
              :key="tagIndex"
              type="primary"
              plain
              size="small"
              class="mr-1 mb-1"
              @click.stop="handleTagClick(tag, product)"
            >
              {{ tag }}
            </wd-tag>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Product {
  id: number
  title: string
  imageUrl: string
  price: number
  originalPrice?: number
  tags?: string[]
}

const props = defineProps<{
  title: string
  products: Product[]
}>()

const emit = defineEmits(['click', 'viewMore', 'tagClick'])

const handleProductClick = (product: Product) => {
  emit('click', product)
}

const onViewMore = () => {
  emit('viewMore')
}

// 处理标签点击事件
const handleTagClick = (tag: string, product: Product) => {
  emit('tagClick', { tag, product })
  // 阻止事件冒泡，避免触发整个产品项的点击事件
  // 注意：已在模板中使用 @click.stop 处理
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ProductRecommend',
})
</script>
