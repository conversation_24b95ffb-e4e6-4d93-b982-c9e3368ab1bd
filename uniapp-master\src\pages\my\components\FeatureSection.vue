<template>
  <view class="bg-white dark:bg-gray-800 rounded-2xl p-5 shadow-md mb-4">
    <view class="space-y-4">
      <view
        v-for="(item, index) in features"
        :key="index"
        class="flex items-center justify-between py-1"
        @click="handleFeatureClick(item, index)"
      >
        <view class="flex items-center">
          <view :class="`w-8 h-8 rounded-lg ${item.bgColor} flex items-center justify-center`">
            <text :class="`${item.icon} ${item.iconColor}`"></text>
          </view>
          <text class="ml-3 text-sm text-gray-700 dark:text-gray-200">{{ item.title }}</text>
        </view>
        <text class="i-carbon-chevron-right text-gray-400 dark:text-gray-500"></text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Feature {
  icon: string
  title: string
  bgColor: string
  iconColor: string
}

const props = defineProps<{
  features: Feature[]
}>()

const emit = defineEmits(['featureClick'])

const handleFeatureClick = (item: Feature, index: number) => {
  emit('featureClick', { item, index })
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'FeatureSection',
})
</script>
