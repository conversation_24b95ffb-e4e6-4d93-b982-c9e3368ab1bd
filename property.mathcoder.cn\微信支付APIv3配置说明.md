# 微信支付APIv3配置说明

## 已配置的参数

✅ **小程序AppID**: wxb26876dbb9b2f639
✅ **商户号**: 1723014182
✅ **APIv2密钥**: HpcTDYtUPvdVvD5QTMfnZG26TBHzbDej
✅ **APIv3密钥**: RPvw2n8kvC5Vfkrwe4TafFB6dFSV7wGd

## 需要补充的小程序配置

❌ **小程序Secret**: 需要从微信公众平台获取

### 获取小程序Secret
1. 登录 [微信公众平台](https://mp.weixin.qq.com)
2. 进入 **开发** → **开发管理** → **开发设置**
3. 在"开发者ID"部分可以看到AppSecret
4. 如果忘记了，可以重置AppSecret

## 需要补充的证书信息

为了完成APIv3配置，你还需要从微信商户平台获取以下信息：

### 1. 下载商户证书

1. 登录 [微信商户平台](https://pay.weixin.qq.com)
2. 进入 **账户中心** → **API安全** → **API证书**
3. 下载证书文件，会得到一个压缩包，包含：
   - `apiclient_cert.pem` (商户证书)
   - `apiclient_key.pem` (商户私钥)

### 2. 获取证书序列号

方法一：从商户平台获取
- 在API证书页面可以直接看到证书序列号

方法二：从证书文件中提取
```bash
openssl x509 -in apiclient_cert.pem -noout -serial
```

### 3. 配置证书信息

有两种方式配置证书：

#### 方式一：直接在.env中配置证书内容（推荐）
```env
WECHAT_PAYMENT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
你的私钥内容
-----END PRIVATE KEY-----"

WECHAT_PAYMENT_CERTIFICATE="-----BEGIN CERTIFICATE-----
你的证书内容
-----END CERTIFICATE-----"

WECHAT_PAYMENT_SERIAL_NO=你的证书序列号
```

#### 方式二：使用证书文件路径
1. 将证书文件上传到服务器的 `storage/wechat/` 目录
2. 配置文件路径：
```env
WECHAT_PAYMENT_CERT_PATH=/path/to/storage/wechat/apiclient_cert.pem
WECHAT_PAYMENT_KEY_PATH=/path/to/storage/wechat/apiclient_key.pem
```

## 当前配置状态

```env
# 小程序登录
WECHAT_MINI_PROGRAM_APPID=wxb26876dbb9b2f639
WECHAT_MINI_PROGRAM_SECRET=your_miniprogram_secret

# 微信支付
WECHAT_PAYMENT_MCH_ID=1723014182
# APIv2密钥（兼容性保留）
WECHAT_PAYMENT_KEY=HpcTDYtUPvdVvD5QTMfnZG26TBHzbDej
# APIv3密钥
WECHAT_PAYMENT_V3_KEY=RPvw2n8kvC5Vfkrwe4TafFB6dFSV7wGd
# 商户私钥和证书（需要从微信商户平台下载）
WECHAT_PAYMENT_PRIVATE_KEY=
WECHAT_PAYMENT_CERTIFICATE=
WECHAT_PAYMENT_SERIAL_NO=
# 证书文件路径（可选，如果使用文件方式）
WECHAT_PAYMENT_CERT_PATH=
WECHAT_PAYMENT_KEY_PATH=
WECHAT_PAYMENT_NOTIFY_URL=http://property.oneself.icu/api/payment/notify
```

## 代码修改完成

✅ 已将支付控制器修改为使用APIv3格式  
✅ 已更新配置文件支持APIv3参数  
✅ 已修改支付回调处理逻辑  

## 下一步

1. **获取小程序Secret** - 从微信公众平台获取并填入.env文件
2. **下载商户证书文件** - 从微信商户平台下载证书
3. **获取证书序列号** - 从商户平台或证书文件中获取
4. **配置证书信息** - 将证书信息填入.env文件
5. **测试支付功能** - 完成配置后测试支付流程

## 注意事项

- APIv3需要证书才能正常工作
- 证书序列号必须正确
- 私钥和证书内容不能有格式错误
- 建议使用方式一（直接配置证书内容）更安全
