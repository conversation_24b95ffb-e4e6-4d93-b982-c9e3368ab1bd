<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('account_applications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['open', 'close']);
            $table->string('name');
            $table->string('id_number');
            $table->string('phone');
            $table->string('address');
            $table->text('reason')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected']);
            $table->text('admin_remark')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('account_applications');
    }
}; 