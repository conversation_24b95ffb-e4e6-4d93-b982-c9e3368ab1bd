import { http } from '@/utils/http'

// 获取合同列表
export const getContracts = (params: {
  page?: number
  page_size?: number
  status?: string
}) => {
  return http.get('/contracts', params)
}

// 获取合同统计信息
export const getContractStatistics = () => {
  return http.get('/contracts/statistics')
}

// 获取合同详情
export const getContractDetail = (id: number) => {
  return http.get(`/contracts/${id}`)
}

// 签署合同
export const signContract = (id: number, data: { signature_name: string }) => {
  return http.post(`/contracts/${id}/sign`, data)
}

// 拒绝签署合同
export const rejectContract = (id: number, data: { reject_reason: string }) => {
  return http.post(`/contracts/${id}/reject`, data)
}

// 根据水表号获取合同
export const getContractByWaterMeter = (waterMeterNo: string) => {
  return http.get(`/contracts/water-meter/${waterMeterNo}`)
}
