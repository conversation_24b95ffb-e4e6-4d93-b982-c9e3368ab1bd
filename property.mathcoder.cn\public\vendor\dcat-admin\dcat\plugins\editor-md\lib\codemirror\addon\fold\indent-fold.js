// CodeMirror, copyright (c) by <PERSON><PERSON> and others
// Distributed under an MIT license: http://codemirror.net/LICENSE

(function(mod) {
  if (typeof exports == "object" && typeof module == "object") // CommonJS
    mod(require("../../lib/codemirror"));
  else if (typeof define == "function" && define.amd) // AMD
    define(["../../lib/codemirror"], mod);
  else // Plain browser env
    mod(CodeMirror);
})(function(CodeMirror) {
"use strict";

CodeMirror.registerHelper("fold", "indent", function(cm, start) {
  var tabSize = cm.getOption("tabSize"), firstLine = cm.getLine(start.line);
  if (!/\S/.test(firstLine)) return;
  var getIndent = function(line) {
    return CodeMirror.countColumn(line, null, tabSize);
  };
  var myIndent = getIndent(firstLine);
  var lastLineInFold = null;
  // Go through lines until we find a line that definitely doesn't belong in
  // the block we're folding, or to the end.
  for (var i = start.line + 1, end = cm.lastLine(); i <= end; ++i) {
    var curLine = cm.getLine(i);
    var curIndent = getIndent(curLine);
    if (curIndent > myIndent) {
      // Lines with a greater indent are considered part of the block.
      lastLineInFold = i;
    } else if (!/\S/.test(curLine)) {
      // Empty lines might be breaks within the block we're trying to fold.
    } else {
      // A non-empty line at an indent equal to or less than ours marks the
      // start of another block.
      break;
    }
  }
  if (lastLineInFold) return {
    from: CodeMirror.Pos(start.line, firstLine.length),
    to: CodeMirror.Pos(lastLineInFold, cm.getLine(lastLineInFold).length)
  };
});

});
