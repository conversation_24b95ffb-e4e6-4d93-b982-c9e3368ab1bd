<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\ActiveAccount;
use App\Models\AccountOpenRequest;
use App\Models\AccountCancellationRequest;

class UserController extends Controller
{
    /**
     * 获取用户个人信息
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
            ]
        ]);
    }

    /**
     * 获取用户账户概览信息
     */
    public function accountOverview(Request $request)
    {
        $user = $request->user();
        
        // 获取已开户数量
        $activeAccountCount = ActiveAccount::where('user_id', $user->id)->count();
        
        // 获取待审核的开户申请数量
        $pendingOpenCount = AccountOpenRequest::where('user_id', $user->id)
            ->where('status', AccountOpenRequest::STATUS_PENDING)
            ->count();
            
        // 获取待审核的销户申请数量
        $pendingCancelCount = AccountCancellationRequest::where('user_id', $user->id)
            ->where('status', AccountCancellationRequest::STATUS_PENDING)
            ->count();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'active_account_count' => $activeAccountCount,
                'pending_open_count' => $pendingOpenCount,
                'pending_cancel_count' => $pendingCancelCount
            ]
        ]);
    }

    /**
     * 更新用户信息
     */
    public function updateProfile(Request $request)
    {
        $data = $request->validate([
            'name' => 'sometimes|string|max:50',
            'avatar' => 'sometimes|string|max:255'
        ]);

        $user = $request->user();
        $user->update($data);

        return response()->json([
            'code' => 0,
            'message' => '更新成功',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
            ]
        ]);
    }

    /**
     * 上传头像文件
     */
    public function uploadAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            $file = $request->file('avatar');
            $fileName = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('avatars', $fileName, 'public');
            
            // 返回完整的URL
            $url = config('app.url') . '/storage/' . $path;
            
            // 同时更新用户头像
            $user = $request->user();
            $user->update(['avatar' => $url]);
            
            return response()->json([
                'code' => 0,
                'message' => '头像上传成功',
                'data' => [
                    'avatar' => $url
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '上传失败: ' . $e->getMessage()
            ], 200);
        }
    }

    /**
     * 更新头像URL
     */
    public function updateAvatar(Request $request)
    {
        $data = $request->validate([
            'avatar' => 'required|string|max:255'
        ]);

        $user = $request->user();
        $user->update(['avatar' => $data['avatar']]);

        return response()->json([
            'code' => 0,
            'message' => '头像更新成功',
            'data' => [
                'avatar' => $user->avatar
            ]
        ]);
    }
} 