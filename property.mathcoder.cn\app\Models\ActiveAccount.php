<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class ActiveAccount extends Model
{
    use HasDateTimeFormatter;
    // 对应数据表
    protected $table = 'active_accounts';

    // 可批量赋值字段
    protected $fillable = [
        'user_id',
        'community',
        'building_number',
        'name',
        'phone',
        'water_meter_number',
        'opened_at',
    ];

    /**
     * 关联开户用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联销户申请
     */
    public function cancellationRequests()
    {
        return $this->hasMany(
            AccountCancellationRequest::class,
            'water_meter_number',
            'water_meter_number'
        );
    }
}
