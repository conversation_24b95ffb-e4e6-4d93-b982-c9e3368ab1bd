<!doctype html>

<title>CodeMirror: Forth mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link href='http://fonts.googleapis.com/css?family=Droid+Sans+Mono' rel='stylesheet' type='text/css'>
<link rel="stylesheet" href="../../lib/codemirror.css">
<link rel=stylesheet href="../../theme/colorforth.css">
<script src="../../lib/codemirror.js"></script>
<script src="forth.js"></script>
<style>
.CodeMirror {
    font-family: 'Droid Sans Mono', monospace;
    font-size: 14px;
}
</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Forth</a>
  </ul>
</div>

<article>

<h2>Forth mode</h2>

<form><textarea id="code" name="code">
\ Insertion sort

: cell-  1 cells - ;

: insert ( start end -- start )
  dup @ >r ( r: v )
  begin
    2dup <
  while
    r@ over cell- @ <
  while
    cell-
    dup @ over cell+ !
  repeat then
  r> swap ! ;

: sort ( array len -- )
  1 ?do
    dup i cells + insert
  loop drop ;</textarea>
  </form>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    lineNumbers: true,
    lineWrapping: true,
    indentUnit: 2,
    tabSize: 2,
    autofocus: true,
    theme: "colorforth",
    mode: "text/x-forth"
  });
</script>

<p>Simple mode that handle Forth-Syntax (<a href="http://en.wikipedia.org/wiki/Forth_%28programming_language%29">Forth on WikiPedia</a>).</p>

<p><strong>MIME types defined:</strong> <code>text/x-forth</code>.</p>

</article>
