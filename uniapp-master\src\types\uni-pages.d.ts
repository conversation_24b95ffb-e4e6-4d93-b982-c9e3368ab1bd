/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/account/index" |
       "/pages/contract/detail" |
       "/pages/contract/index" |
       "/pages/login/index" |
       "/pages/my/index" |
       "/pages/payment/detail" |
       "/pages/payment/history" |
       "/pages/payment/index" |
       "/pages/usage/detail" |
       "/pages/usage/index" |
       "/pages/webview/index" |
       "/pages/account/cancel/index" |
       "/pages/account/open/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/contract/index" | "/pages/my/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
