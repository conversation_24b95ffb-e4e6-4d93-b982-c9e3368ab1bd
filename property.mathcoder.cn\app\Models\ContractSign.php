<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class ContractSign extends Model
{
    use HasDateTimeFormatter;
    protected $table = 'contract_signs';

    protected $fillable = [
        'contract_template_id',
        'user_id',
        'water_meter_number',
        'status',
        'signed_at',
        'signature_name',
        'signature_ip',
        'reject_reason',
        'esign_flow_id',
        'esign_url',
        'esign_status',
        'esign_callback_payload',
    ];

    public $timestamps = true;

    // 状态映射
    public static array $statuses = [
        'pending'  => '待发送',
        'sent'     => '已发送',
        'signed'   => '已签署',
        'rejected' => '已拒签',
    ];
    
    protected $casts = [
        'params'                => 'array',
        'signed_at'             => 'datetime',
    ];

    public function template()
    {
        return $this->belongsTo(ContractTemplate::class, 'contract_template_id');
    }

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }
}
