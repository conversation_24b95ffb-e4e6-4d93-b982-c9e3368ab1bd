<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('contracts', function (Blueprint $table) {
            $table->dropColumn('expires_at');
            $table->date('valid_from')->after('signed_at');
            $table->date('valid_until')->after('valid_from');
        });
    }

    public function down(): void
    {
        Schema::table('contracts', function (Blueprint $table) {
            $table->dropColumn(['valid_from', 'valid_until']);
            $table->timestamp('expires_at')->nullable()->after('signed_at');
        });
    }
}; 