<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class AdCarousel extends Model
{
    use HasDateTimeFormatter;
    // 对应数据表
    protected $table = 'ad_carousel';

    // 可批量赋值字段
    protected $fillable = [
        'image_url',
        'link_type',
        'link_target',
        'link_params',
        'sort_order',
        'status',
        'start_at',
        'end_at',
    ];

    // 链接类型常量
    public const LINK_NONE         = 'none';
    public const LINK_MINI_PAGE    = 'mini_page';
    public const LINK_MINI_PROGRAM = 'mini_program';
    public const LINK_EXTERNAL_URL = 'external_url';

    // 状态常量
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_ACTIVE   = 'active';

    // 类型与状态选项映射
    public static array $linkTypes = [
        self::LINK_NONE         => '不跳转',
        self::LINK_MINI_PAGE    => '小程序内页',
        self::LINK_MINI_PROGRAM => '跳转小程序',
        self::LINK_EXTERNAL_URL => '外部链接',
    ];

    public static array $statuses = [
        self::STATUS_INACTIVE => '禁用',
        self::STATUS_ACTIVE   => '启用',
    ];

    // 时间与 JSON 类型转换
    protected $casts = [
        'link_params' => 'array',
        'start_at'    => 'datetime',
        'end_at'      => 'datetime',
    ];

    /**
     * 默认排序：按 sort_order 降序
     */
    protected static function booted()
    {
        static::addGlobalScope('sorted', function (Builder $builder) {
            $builder->orderByDesc('sort_order');
        });
    }

    /* ─────────── 查询作用域 ─────────── */

    /**
     * 仅查询启用状态的记录
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 仅查询当前时间在生效期内的记录
     */
    public function scopeEffective(Builder $query): Builder
    {
        $now = now();
        return $query
            ->where(function ($q) use ($now) {
                $q->whereNull('start_at')->orWhere('start_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('end_at')->orWhere('end_at', '>=', $now);
            });
    }
}
