tinymce.addI18n('bg_BG',{
"Redo": "\u041e\u0442\u043c\u0435\u043d\u044f\u043d\u0435",
"Undo": "\u0412\u0440\u044a\u0449\u0430\u043d\u0435",
"Cut": "\u0418\u0437\u0440\u044f\u0437\u0432\u0430\u043d\u0435",
"Copy": "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435",
"Paste": "\u041f\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435",
"Select all": "\u041c\u0430\u0440\u043a\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0446\u044f\u043b\u043e\u0442\u043e \u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435",
"New document": "\u041d\u043e\u0432 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442",
"Ok": "\u0414\u043e\u0431\u0440\u0435",
"Cancel": "\u041e\u0442\u043a\u0430\u0437",
"Visual aids": "\u0412\u0438\u0437\u0443\u0430\u043b\u043d\u043e \u043e\u0442\u043a\u0440\u043e\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u0430\u0431\u043b\u0438\u0446\u0438 \u0431\u0435\u0437 \u043a\u0430\u043d\u0442\u043e\u0432\u0435 (\u0440\u0430\u043c\u043a\u0438)",
"Bold": "\u0423\u0434\u0435\u0431\u0435\u043b\u0435\u043d (\u043f\u043e\u043b\u0443\u0447\u0435\u0440)",
"Italic": "\u041d\u0430\u043a\u043b\u043e\u043d\u0435\u043d (\u043a\u0443\u0440\u0441\u0438\u0432)",
"Underline": "\u041f\u043e\u0434\u0447\u0435\u0440\u0442\u0430\u0432\u0430\u043d\u0435",
"Strikethrough": "\u0417\u0430\u0447\u0435\u0440\u0442\u0430\u0432\u0430\u043d\u0435",
"Superscript": "\u0413\u043e\u0440\u0435\u043d \u0438\u043d\u0434\u0435\u043a\u0441",
"Subscript": "\u0414\u043e\u043b\u0435\u043d \u0438\u043d\u0434\u0435\u043a\u0441",
"Clear formatting": "\u0418\u0437\u0447\u0438\u0441\u0442\u0432\u0430\u043d\u0435 \u043d\u0430 \u0444\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d\u0435\u0442\u043e",
"Align left": "\u041f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435 \u043e\u0442\u043b\u044f\u0432\u043e",
"Align center": "\u0426\u0435\u043d\u0442\u0440\u0438\u0440\u0430\u043d\u0435",
"Align right": "\u041f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435 \u043e\u0442\u0434\u044f\u0441\u043d\u043e",
"Justify": "\u0414\u0432\u0443\u0441\u0442\u0440\u0430\u043d\u043d\u043e \u043f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435",
"Bullet list": "\u0421\u043f\u0438\u0441\u044a\u043a \u0441 \u0432\u043e\u0434\u0430\u0447\u0438",
"Numbered list": "\u041d\u043e\u043c\u0435\u0440\u0438\u0440\u0430\u043d \u0441\u043f\u0438\u0441\u044a\u043a",
"Decrease indent": "\u041d\u0430\u043c\u0430\u043b\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0441\u0442\u044a\u043f\u0430",
"Increase indent": "\u0423\u0432\u0435\u043b\u0438\u0447\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0441\u0442\u044a\u043f\u0430",
"Close": "\u0417\u0430\u0442\u0432\u0430\u0440\u044f\u043d\u0435",
"Formats": "\u0424\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d\u0435",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0412\u0430\u0448\u0438\u044f\u0442 \u0431\u0440\u0430\u0443\u0437\u044a\u0440 \u043d\u0435 \u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u0435\u043d \u0434\u043e\u0441\u0442\u044a\u043f \u0434\u043e \u043a\u043b\u0438\u043f\u0431\u043e\u0440\u0434\u0430. \u0412\u043c\u0435\u0441\u0442\u043e \u0442\u043e\u0432\u0430 \u0438\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u0439\u0442\u0435 \u043a\u043b\u0430\u0432\u0438\u0448\u043d\u0438\u0442\u0435 \u043a\u043e\u043c\u0431\u0438\u043d\u0430\u0446\u0438\u0438 Ctrl+X (\u0437\u0430 \u0438\u0437\u0440\u044f\u0437\u0432\u0430\u043d\u0435), Ctrl+C (\u0437\u0430 \u043a\u043e\u043f\u0438\u0440\u0430\u043d\u0435) \u0438 Ctrl+V (\u0437\u0430 \u043f\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435).",
"Headers": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u044f",
"Header 1": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 1",
"Header 2": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 2",
"Header 3": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 3",
"Header 4": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 4",
"Header 5": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 5",
"Header 6": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 6",
"Headings": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u044f",
"Heading 1": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 1",
"Heading 2": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 2",
"Heading 3": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 3",
"Heading 4": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 4",
"Heading 5": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 5",
"Heading 6": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 6",
"Preformatted": "\u041f\u0440\u0435\u0444\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d",
"Div": "\u0411\u043b\u043e\u043a",
"Pre": "\u041f\u0440\u0435\u0434\u0432\u0430\u0440\u0438\u0442\u0435\u043b\u043d\u043e \u043e\u0444\u043e\u0440\u043c\u0435\u043d \u0442\u0435\u043a\u0441\u0442",
"Code": "\u041a\u043e\u0434",
"Paragraph": "\u041f\u0430\u0440\u0430\u0433\u0440\u0430\u0444",
"Blockquote": "\u0426\u0438\u0442\u0430\u0442",
"Inline": "\u041d\u0430 \u0435\u0434\u0438\u043d \u0440\u0435\u0434",
"Blocks": "\u0411\u043b\u043e\u043a\u043e\u0432\u0435",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u041f\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435\u0442\u043e \u0432 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u0435 \u0432 \u043e\u0431\u0438\u043a\u043d\u043e\u0432\u0435\u043d \u0440\u0435\u0436\u0438\u043c. \u0421\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435\u0442\u043e \u0449\u0435 \u0431\u044a\u0434\u0435 \u043f\u043e\u0441\u0442\u0430\u0432\u0435\u043d\u043e \u043a\u0430\u0442\u043e \u043d\u0435\u0444\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d \u0442\u0435\u043a\u0441\u0442, \u0434\u043e\u043a\u0430\u0442\u043e \u0438\u0437\u043a\u043b\u044e\u0447\u0438\u0442\u0435 \u0442\u0430\u0437\u0438 \u043e\u043f\u0446\u0438\u044f.",
"Fonts": "\u0428\u0440\u0438\u0444\u0442\u043e\u0432\u0435",
"Font Sizes": "\u0420\u0430\u0437\u043c\u0435\u0440 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430",
"Class": "\u041a\u043b\u0430\u0441",
"Browse for an image": "\u041f\u043e\u0442\u044a\u0440\u0441\u0435\u0442\u0435 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435",
"OR": "\u0418\u041b\u0418",
"Drop an image here": "\u041f\u0443\u0441\u043d\u0435\u0442\u0435 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435\u0442\u043e \u0442\u0443\u043a",
"Upload": "\u041a\u0430\u0447\u0432\u0430\u043d\u0435",
"Block": "\u0411\u043b\u043e\u043a",
"Align": "\u041f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435",
"Default": "\u041f\u043e \u043f\u043e\u0434\u0440\u0430\u0437\u0431\u0438\u0440\u0430\u043d\u0435",
"Circle": "\u041e\u043a\u0440\u044a\u0436\u043d\u043e\u0441\u0442\u0438",
"Disc": "\u041a\u0440\u044a\u0433\u0447\u0435\u0442\u0430",
"Square": "\u0417\u0430\u043f\u044a\u043b\u043d\u0435\u043d\u0438 \u043a\u0432\u0430\u0434\u0440\u0430\u0442\u0438",
"Lower Alpha": "\u041c\u0430\u043b\u043a\u0438 \u0431\u0443\u043a\u0432\u0438",
"Lower Greek": "\u041c\u0430\u043b\u043a\u0438 \u0433\u0440\u044a\u0446\u043a\u0438 \u0431\u0443\u043a\u0432\u0438",
"Lower Roman": "\u0420\u0438\u043c\u0441\u043a\u0438 \u0447\u0438\u0441\u043b\u0430 \u0441 \u043c\u0430\u043b\u043a\u0438 \u0431\u0443\u043a\u0432\u0438",
"Upper Alpha": "\u0413\u043b\u0430\u0432\u043d\u0438 \u0431\u0443\u043a\u0432\u0438",
"Upper Roman": "\u0420\u0438\u043c\u0441\u043a\u0438 \u0447\u0438\u0441\u043b\u0430 \u0441 \u0433\u043b\u0430\u0432\u043d\u0438 \u0431\u0443\u043a\u0432\u0438",
"Anchor...": "\u041a\u043e\u0442\u0432\u0430...",
"Name": "\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435",
"Id": "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 (id)",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440\u0430 (id) \u0442\u0440\u044f\u0431\u0432\u0430 \u0434\u0430 \u0437\u0430\u043f\u043e\u0447\u0432\u0430 \u0441 \u0431\u0443\u043a\u0432\u0430, \u043f\u043e\u0441\u043b\u0435\u0434\u0432\u0430\u043d \u043e\u0442 \u0431\u0443\u043a\u0432\u0438, \u0447\u0438\u0444\u0440\u0438, \u0442\u0438\u0440\u0435\u0442\u0430, \u0442\u043e\u0447\u043a\u0438, \u0434\u0432\u043e\u0435\u0442\u043e\u0447\u0438\u0435 \u0438 \u0434\u043e\u043b\u043d\u043e \u0442\u0438\u0440\u0435.",
"You have unsaved changes are you sure you want to navigate away?": "\u0412 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0438\u043c\u0430 \u043d\u0435\u0437\u0430\u043f\u0430\u0437\u0435\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438. \u0429\u0435 \u043f\u0440\u043e\u0434\u044a\u043b\u0436\u0438\u0442\u0435 \u043b\u0438?",
"Restore last draft": "\u0412\u044a\u0437\u0441\u0442\u0430\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0430\u0442\u0430 \u0447\u0435\u0440\u043d\u043e\u0432\u0430",
"Special character...": "\u0421\u043f\u0435\u0446\u0438\u0430\u043b\u0435\u043d \u0441\u0438\u043c\u0432\u043e\u043b...",
"Source code": "\u0418\u0437\u0445\u043e\u0434\u0435\u043d \u043a\u043e\u0434 \u043d\u0430 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 HTML",
"Insert\/Edit code sample": "\u0412\u043c\u044a\u043a\u043d\u0438\/ \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u0439 \u043f\u0440\u0438\u043c\u0435\u0440\u0435\u043d \u043a\u043e\u0434",
"Language": "\u0415\u0437\u0438\u043a",
"Code sample...": "\u041f\u0440\u0438\u043c\u0435\u0440\u0435\u043d \u043a\u043e\u0434...",
"Color Picker": "\u0418\u0437\u0431\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0446\u0432\u044f\u0442",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "\u041e\u0442\u043b\u044f\u0432\u043e \u043d\u0430\u0434\u044f\u0441\u043d\u043e",
"Right to left": "\u041e\u0442\u0434\u044f\u0441\u043d\u043e \u043d\u0430\u043b\u044f\u0432\u043e",
"Emoticons...": "\u0415\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0438...",
"Metadata and Document Properties": "\u041c\u0435\u0442\u0430\u0434\u0430\u043d\u043d\u0438 \u0438 \u0441\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430",
"Title": "\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435",
"Keywords": "\u041a\u043b\u044e\u0447\u043e\u0432\u0438 \u0434\u0443\u043c\u0438",
"Description": "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435",
"Robots": "\u0420\u043e\u0431\u043e\u0442\u0438 \u043d\u0430 \u0443\u0435\u0431 \u0442\u044a\u0440\u0441\u0430\u0447\u043a\u0438",
"Author": "\u0410\u0432\u0442\u043e\u0440",
"Encoding": "\u041a\u043e\u0434\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0437\u043d\u0430\u0446\u0438\u0442\u0435",
"Fullscreen": "\u041d\u0430 \u0446\u044f\u043b \u0435\u043a\u0440\u0430\u043d",
"Action": "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u0435",
"Shortcut": "\u0411\u044a\u0440\u0437 \u043a\u043b\u0430\u0432\u0438\u0448",
"Help": "\u041f\u043e\u043c\u043e\u0449",
"Address": "\u0410\u0434\u0440\u0435\u0441",
"Focus to menubar": "Focus to menubar",
"Focus to toolbar": "Focus to toolbar",
"Focus to element path": "Focus to element path",
"Focus to contextual toolbar": "Focus to contextual toolbar",
"Insert link (if link plugin activated)": "\u041f\u043e\u0441\u0442\u0430\u0432\u0438 \u0432\u0440\u044a\u0437\u043a\u0430 (\u0430\u043a\u043e \u043f\u043b\u044a\u0433\u0438\u043d\u0430 \u0437\u0430 \u0432\u0440\u044a\u0437\u043a\u0438 \u0435 \u0430\u043a\u0442\u0438\u0432\u0438\u0440\u0430\u043d)",
"Save (if save plugin activated)": "\u0417\u0430\u043f\u0438\u0448\u0438 (\u0430\u043a\u043e \u043f\u043b\u044a\u0433\u0438\u043d\u0430 \u0437\u0430 \u0437\u0430\u043f\u0438\u0441 \u0435 \u0430\u043a\u0442\u0438\u0432\u0438\u0440\u0430\u043d)",
"Find (if searchreplace plugin activated)": "\u041d\u0430\u043c\u0435\u0440\u0438 (\u0430\u043a\u043e \u043f\u043b\u044a\u0433\u0438\u043d\u0430 \u0437\u0430 \u0442\u044a\u0440\u0441\u0435\u043d\u0435\/\u0437\u0430\u043c\u044f\u043d\u0430 \u0435 \u0430\u043a\u0442\u0438\u0432\u0438\u0440\u0430\u043d)",
"Plugins installed ({0}):": "\u0418\u043d\u0441\u0442\u0430\u043b\u0438\u0440\u0430\u043d\u0438 \u043f\u043b\u044a\u0433\u0438\u043d\u0438 ({0}):",
"Premium plugins:": "\u041f\u0440\u0435\u043c\u0438\u0439\u043d\u0438 \u043f\u043b\u044a\u0433\u0438\u043d\u0438:",
"Learn more...": "\u041d\u0430\u0443\u0447\u0435\u0442\u0435 \u043f\u043e\u0432\u0435\u0447\u0435...",
"You are using {0}": "\u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u0442\u0435 {0}",
"Plugins": "Plugins",
"Handy Shortcuts": "Handy Shortcuts",
"Horizontal line": "\u0425\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u043d\u0430 \u0447\u0435\u0440\u0442\u0430",
"Insert\/edit image": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\/\u043a\u043e\u0440\u0435\u043a\u0446\u0438\u044f \u043d\u0430 \u043a\u0430\u0440\u0442\u0438\u043d\u043a\u0430",
"Image description": "\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u043d\u0430 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435\u0442\u043e",
"Source": "\u0410\u0434\u0440\u0435\u0441",
"Dimensions": "\u0420\u0430\u0437\u043c\u0435\u0440",
"Constrain proportions": "\u0417\u0430\u0432\u0430\u0437\u043d\u0430\u0432\u0435 \u043d\u0430 \u043f\u0440\u043e\u043f\u043e\u0440\u0446\u0438\u0438\u0442\u0435",
"General": "\u041e\u0431\u0449\u043e",
"Advanced": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u043e",
"Style": "\u0421\u0442\u0438\u043b",
"Vertical space": "\u0412\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u043d\u043e \u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0441\u0442\u0432\u043e",
"Horizontal space": "\u0425\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u043d\u043e \u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u0441\u0442\u0432\u043e",
"Border": "\u041a\u0430\u043d\u0442 (\u0440\u0430\u043c\u043a\u0430)",
"Insert image": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435",
"Image...": "\u0418\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435...",
"Image list": "\u0421\u043f\u0438\u0441\u044a\u043a \u0441 \u043a\u0430\u0440\u0442\u0438\u043d\u043a\u0438",
"Rotate counterclockwise": "\u0417\u0430\u0432\u044a\u0440\u0442\u0430\u043d\u0435 \u043e\u0431\u0440\u0430\u0442\u043d\u043e \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a\u0430",
"Rotate clockwise": "\u0417\u0430\u0432\u044a\u0440\u0442\u0430\u043d\u0435 \u043f\u043e \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a\u0430",
"Flip vertically": "\u041e\u0431\u044a\u0440\u043d\u0438 \u0432\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u043d\u043e",
"Flip horizontally": "\u041e\u0431\u044a\u0440\u043d\u0438 \u0445\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u043d\u043e",
"Edit image": "\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435\u0442\u043e",
"Image options": "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043d\u0430 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435\u0442\u043e",
"Zoom in": "\u041f\u0440\u0438\u0431\u043b\u0438\u0436\u0438",
"Zoom out": "\u041e\u0442\u0434\u0430\u043b\u0435\u0447\u0438",
"Crop": "\u0418\u0437\u0440\u044f\u0437\u0432\u0430\u043d\u0435",
"Resize": "\u041f\u0440\u0435\u043e\u0440\u0430\u0437\u043c\u0435\u0440\u044f\u0432\u0430\u043d\u0435",
"Orientation": "\u041e\u0440\u0438\u0435\u043d\u0442\u0430\u0446\u0438\u044f",
"Brightness": "\u042f\u0440\u043a\u043e\u0441\u0442",
"Sharpen": "\u0418\u0437\u043e\u0441\u0442\u0440\u044f\u043d\u0435",
"Contrast": "\u041a\u043e\u043d\u0442\u0440\u0430\u0441\u0442",
"Color levels": "\u0426\u0432\u0435\u0442\u043d\u0438 \u043d\u0438\u0432\u0430",
"Gamma": "\u0413\u0430\u043c\u0430",
"Invert": "\u0418\u043d\u0432\u0435\u0440\u0441\u0438\u044f",
"Apply": "\u041f\u0440\u0438\u043b\u043e\u0436\u0438",
"Back": "\u041d\u0430\u0437\u0430\u0434",
"Insert date\/time": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0434\u0430\u0442\u0430\/\u0447\u0430\u0441",
"Date\/time": "\u0414\u0430\u0442\u0430\/\u0447\u0430\u0441",
"Insert\/Edit Link": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\/\u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0445\u0438\u043f\u0435\u0440\u0432\u0440\u044a\u0437\u043a\u0430 (\u043b\u0438\u043d\u043a)",
"Insert\/edit link": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\/\u043a\u043e\u0440\u0435\u043a\u0446\u0438\u044f \u043d\u0430 \u0445\u0438\u043f\u0435\u0440\u0432\u0440\u044a\u0437\u043a\u0430 (\u043b\u0438\u043d\u043a)",
"Text to display": "\u0422\u0435\u043a\u0441\u0442",
"Url": "\u0410\u0434\u0440\u0435\u0441 (URL)",
"Open link in...": "\u041e\u0442\u0432\u0430\u0440\u044f\u043d\u0435 \u043d\u0430 \u0445\u0438\u043f\u0435\u0440\u0432\u0440\u044a\u0437\u043a\u0430 \u0432...",
"Current window": "\u0422\u0435\u043a\u0443\u0449 \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446",
"None": "\u0411\u0435\u0437",
"New window": "\u0412 \u043d\u043e\u0432 \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446 (\u043f\u043e\u0434\u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446)",
"Remove link": "\u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435 \u043d\u0430 \u0445\u0438\u043f\u0435\u0440\u0432\u0440\u044a\u0437\u043a\u0430",
"Anchors": "\u041a\u043e\u0442\u0432\u0438",
"Link...": "\u0425\u0438\u043f\u0435\u0440\u0432\u0440\u044a\u0437\u043a\u0430...",
"Paste or type a link": "\u041f\u043e\u0441\u0442\u0430\u0432\u0435\u0442\u0435 \u0438\u043b\u0438 \u043d\u0430\u043f\u0438\u0448\u0435\u0442\u0435 \u0432\u0440\u044a\u0437\u043a\u0430(\u043b\u0438\u043d\u043a)",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "URL \u0430\u0434\u0440\u0435\u0441\u044a\u0442, \u043a\u043e\u0439\u0442\u043e \u0432\u044a\u0432\u0434\u043e\u0445\u0442\u0435 \u043f\u0440\u0438\u043b\u0438\u0447\u0430 \u043d\u0430 \u0435-\u043c\u0435\u0439\u043b \u0430\u0434\u0440\u0435\u0441. \u0418\u0441\u043a\u0430\u0442\u0435 \u043b\u0438 \u0434\u0430 \u0434\u043e\u0431\u0430\u0432\u0438\u0442\u0435 \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u0438\u044f mailto: \u043f\u0440\u0435\u0444\u0438\u043a\u0441?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "URL \u0430\u0434\u0440\u0435\u0441\u044a\u0442, \u043a\u043e\u0439\u0442\u043e \u0432\u044a\u0432\u0434\u043e\u0445\u0442\u0435 \u043f\u0440\u0438\u043b\u0438\u0447\u0430 \u0432\u044a\u043d\u0448\u0435\u043d \u0430\u0434\u0440\u0435\u0441. \u0418\u0441\u043a\u0430\u0442\u0435 \u043b\u0438 \u0434\u0430 \u0434\u043e\u0431\u0430\u0432\u0438\u0442\u0435 \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u0438\u044f http:\/\/ \u043f\u0440\u0435\u0444\u0438\u043a\u0441?",
"Link list": "\u0421\u043f\u0438\u0441\u044a\u043a \u0441 \u0432\u0440\u044a\u0437\u043a\u0438",
"Insert video": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0432\u0438\u0434\u0435\u043e",
"Insert\/edit video": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\/\u043a\u043e\u0440\u0435\u043a\u0446\u0438\u044f \u043d\u0430 \u0432\u0438\u0434\u0435\u043e",
"Insert\/edit media": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\/\u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043c\u0435\u0434\u0438\u044f",
"Alternative source": "\u0410\u043b\u0442\u0435\u0440\u043d\u0430\u0442\u0438\u0432\u0435\u043d \u0430\u0434\u0440\u0435\u0441",
"Alternative source URL": "\u0410\u043b\u0442\u0435\u0440\u043d\u0430\u0442\u0438\u0432\u0435\u043d \u0430\u0434\u0440\u0435\u0441 URL",
"Media poster (Image URL)": "\u041c\u0435\u0434\u0438\u0435\u043d \u043f\u043b\u0430\u043a\u0430\u0442 (\u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435 URL)",
"Paste your embed code below:": "\u041f\u043e\u0441\u0442\u0430\u0432\u0435\u0442\u0435 \u043a\u043e\u0434\u0430 \u0437\u0430 \u0432\u0433\u0440\u0430\u0436\u0434\u0430\u043d\u0435 \u0432 \u043f\u043e\u043b\u0435\u0442\u043e \u043f\u043e-\u0434\u043e\u043b\u0443:",
"Embed": "\u0412\u0433\u0440\u0430\u0436\u0434\u0430\u043d\u0435",
"Media...": "\u041c\u0435\u0434\u0438\u044f...",
"Nonbreaking space": "\u0418\u043d\u0442\u0435\u0440\u0432\u0430\u043b",
"Page break": "\u041d\u043e\u0432\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0430",
"Paste as text": "\u041f\u043e\u0441\u0442\u0430\u0432\u0438 \u043a\u0430\u0442\u043e \u0442\u0435\u043a\u0441\u0442",
"Preview": "\u041f\u0440\u0435\u0434\u0432\u0430\u0440\u0438\u0442\u0435\u043b\u0435\u043d \u0438\u0437\u0433\u043b\u0435\u0434",
"Print...": "\u041e\u0442\u043f\u0435\u0447\u0430\u0442\u0432\u0430\u043d\u0435...",
"Save": "\u0421\u044a\u0445\u0440\u0430\u043d\u044f\u0432\u0430\u043d\u0435",
"Find": "\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u0437\u0430",
"Replace with": "\u0417\u0430\u043c\u044f\u043d\u0430 \u0441",
"Replace": "\u0417\u0430\u043c\u044f\u043d\u0430",
"Replace all": "\u0417\u0430\u043c\u044f\u043d\u0430 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u0438 \u0441\u0440\u0435\u0449\u0430\u043d\u0438\u044f",
"Previous": "\u041f\u0440\u0435\u0434\u0438\u0448\u0435\u043d",
"Next": "\u0421\u043b\u0435\u0434\u0432\u0430\u0449",
"Find and replace...": "\u041d\u0430\u043c\u0438\u0440\u0430\u043d\u0435 \u0438 \u0437\u0430\u043c\u044f\u043d\u0430...",
"Could not find the specified string.": "\u0422\u044a\u0440\u0441\u0435\u043d\u0438\u044f\u0442 \u0442\u0435\u043a\u0441\u0442 \u043d\u0435 \u0435 \u043d\u0430\u043c\u0435\u0440\u0435\u043d.",
"Match case": "\u0421\u044a\u0432\u043f\u0430\u0434\u0435\u043d\u0438\u0435 \u043d\u0430 \u0440\u0435\u0433\u0438\u0441\u0442\u044a\u0440\u0430 (\u043c\u0430\u043b\u043a\u0438\/\u0433\u043b\u0430\u0432\u043d\u0438 \u0431\u0443\u043a\u0432\u0438)",
"Find whole words only": "\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u0441\u0430\u043c\u043e \u043d\u0430 \u0446\u0435\u043b\u0438 \u0434\u0443\u043c\u0438",
"Spell check": "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430",
"Ignore": "\u0418\u0433\u043d\u043e\u0440\u0438\u0440\u0430\u043d\u0435",
"Ignore all": "\u0418\u0433\u043d\u043e\u0440\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0432\u0441\u0438\u0447\u043a\u043e",
"Finish": "\u041a\u0440\u0430\u0439",
"Add to Dictionary": "\u0414\u043e\u0431\u0430\u0432\u0438 \u0432 \u0440\u0435\u0447\u043d\u0438\u043a\u0430",
"Insert table": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0442\u0430\u0431\u043b\u0438\u0446\u0430",
"Table properties": "\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u0442\u0430\u0431\u043b\u0438\u0446\u0430\u0442\u0430",
"Delete table": "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u0430\u0431\u043b\u0438\u0446\u0430\u0442\u0430",
"Cell": "\u041a\u043b\u0435\u0442\u043a\u0430",
"Row": "\u0420\u0435\u0434",
"Column": "\u041a\u043e\u043b\u043e\u043d\u0430",
"Cell properties": "\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u043a\u043b\u0435\u0442\u043a\u0430\u0442\u0430",
"Merge cells": "\u0421\u043b\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u0435\u0442\u043a\u0438\u0442\u0435",
"Split cell": "\u0420\u0430\u0437\u0434\u0435\u043b\u044f\u043d\u0435 \u043d\u0430 \u043a\u043b\u0435\u0442\u043a\u0430",
"Insert row before": "\u0412\u043c\u044a\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434 \u043f\u0440\u0435\u0434\u0438",
"Insert row after": "\u0412\u043c\u044a\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434 \u0441\u043b\u0435\u0434",
"Delete row": "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434\u0430",
"Row properties": "\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u0440\u0435\u0434\u0430",
"Cut row": "\u0418\u0437\u0440\u044f\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434",
"Copy row": "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434",
"Paste row before": "\u041f\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434 \u043f\u0440\u0435\u0434\u0438",
"Paste row after": "\u041f\u043e\u0441\u0442\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0440\u0435\u0434 \u0441\u043b\u0435\u0434",
"Insert column before": "\u0412\u043c\u044a\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043b\u043e\u043d\u0430 \u043f\u0440\u0435\u0434\u0438",
"Insert column after": "\u0412\u043c\u044a\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043b\u043e\u043d\u0430 \u0441\u043b\u0435\u0434",
"Delete column": "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043b\u043e\u043d\u0430\u0442\u0430",
"Cols": "\u041a\u043e\u043b\u043e\u043d\u0438",
"Rows": "\u0420\u0435\u0434\u043e\u0432\u0435",
"Width": "\u0428\u0438\u0440\u0438\u043d\u0430",
"Height": "\u0412\u0438\u0441\u043e\u0447\u0438\u043d\u0430",
"Cell spacing": "\u0420\u0430\u0437\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u043c\u0435\u0436\u0434\u0443 \u043a\u043b\u0435\u0442\u043a\u0438\u0442\u0435",
"Cell padding": "\u0420\u0430\u0437\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u0434\u043e \u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435\u0442\u043e",
"Show caption": "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u0430\u0434\u043f\u0438\u0441",
"Left": "\u041b\u044f\u0432\u043e",
"Center": "\u0426\u0435\u043d\u0442\u0440\u0438\u0440\u0430\u043d\u043e",
"Right": "\u0414\u044f\u0441\u043d\u043e",
"Cell type": "\u0422\u0438\u043f \u043d\u0430 \u043a\u043b\u0435\u0442\u043a\u0430\u0442\u0430",
"Scope": "\u041e\u0431\u0445\u0432\u0430\u0442",
"Alignment": "\u041f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435",
"H Align": "\u0425\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u043d\u043e \u043f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435",
"V Align": "\u0412\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u043d\u043e \u043f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435",
"Top": "\u0413\u043e\u0440\u0435",
"Middle": "\u041f\u043e \u0441\u0440\u0435\u0434\u0430\u0442\u0430",
"Bottom": "\u0414\u043e\u043b\u0443",
"Header cell": "\u0417\u0430\u0433\u043b\u0430\u0432\u043d\u0430 \u043a\u043b\u0435\u0442\u043a\u0430 (\u0430\u043d\u0442\u0435\u0442\u043a\u0430)",
"Row group": "Row group",
"Column group": "Column group",
"Row type": "\u0422\u0438\u043f \u043d\u0430 \u0440\u0435\u0434\u0430",
"Header": "\u0413\u043e\u0440\u0435\u043d \u043a\u043e\u043b\u043e\u043d\u0442\u0438\u0442\u0443\u043b (header)",
"Body": "\u0421\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435 (body)",
"Footer": "\u0414\u043e\u043b\u0435\u043d \u043a\u043e\u043b\u043e\u043d\u0442\u0438\u0442\u0443\u043b (footer)",
"Border color": "\u0426\u0432\u044f\u0442 \u043d\u0430 \u0440\u0430\u043c\u043a\u0430\u0442\u0430",
"Insert template...": "\u0412\u043c\u044a\u043a\u0432\u0430\u043d\u0435 \u043d\u0430 \u0448\u0430\u0431\u043b\u043e\u043d...",
"Templates": "\u0428\u0430\u0431\u043b\u043e\u043d\u0438",
"Template": "\u0428\u0430\u0431\u043b\u043e\u043d",
"Text color": "\u0426\u0432\u044f\u0442 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430",
"Background color": "\u0424\u043e\u043d\u043e\u0432 \u0446\u0432\u044f\u0442",
"Custom...": "\u0418\u0437\u0431\u0440\u0430\u043d...",
"Custom color": "\u0426\u0432\u044f\u0442 \u043f\u043e \u0438\u0437\u0431\u043e\u0440",
"No color": "\u0411\u0435\u0437 \u0446\u0432\u044f\u0442",
"Remove color": "\u041f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u043d\u0435 \u043d\u0430 \u0446\u0432\u0435\u0442\u0430",
"Table of Contents": "\u0421\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435",
"Show blocks": "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0431\u043b\u043e\u043a\u043e\u0432\u0435\u0442\u0435",
"Show invisible characters": "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u0435\u043f\u0435\u0447\u0430\u0442\u0430\u0435\u043c\u0438 \u0437\u043d\u0430\u0446\u0438",
"Word count": "\u0411\u0440\u043e\u0435\u043d\u0435 \u043d\u0430 \u0434\u0443\u043c\u0438",
"Count": "\u0411\u0440\u043e\u0439",
"Document": "\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442",
"Selection": "\u0418\u0437\u0431\u0440\u0430\u043d\u043e",
"Words": "\u0414\u0443\u043c\u0438",
"Words: {0}": "\u0411\u0440\u043e\u0439 \u0434\u0443\u043c\u0438: {0}",
"{0} words": "{0} \u0431\u0440\u043e\u0439 \u0434\u0443\u043c\u0438",
"File": "\u0424\u0430\u0439\u043b",
"Edit": "\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435",
"Insert": "\u0412\u043c\u044a\u043a\u0432\u0430\u043d\u0435",
"View": "\u0418\u0437\u0433\u043b\u0435\u0434",
"Format": "\u0424\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d\u0435",
"Table": "\u0422\u0430\u0431\u043b\u0438\u0446\u0430",
"Tools": "\u0418\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442\u0438",
"Powered by {0}": "\u0421\u044a\u0437\u0434\u0430\u0434\u0435\u043d\u043e \u0441 {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u041f\u043e\u043b\u0435 \u0437\u0430 \u0444\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d \u0442\u0435\u043a\u0441\u0442. \u041d\u0430\u0442\u0438\u0441\u043d\u0435\u0442\u0435 Alt+F9 \u0437\u0430 \u043c\u0435\u043d\u044e; Alt+F10 \u0437\u0430 \u043b\u0435\u043d\u0442\u0430 \u0441 \u0438\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442\u0438; Alt+0 \u0437\u0430 \u043f\u043e\u043c\u043e\u0449.",
"Image title": "\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 \u043d\u0430 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435\u0442\u043e",
"Border width": "\u0428\u0438\u0440\u0438\u043d\u0430 \u043d\u0430 \u0440\u0430\u043c\u043a\u0430\u0442\u0430",
"Border style": "\u0421\u0442\u0438\u043b \u043d\u0430 \u0440\u0430\u043c\u043a\u0430\u0442\u0430",
"Error": "\u0413\u0440\u0435\u0448\u043a\u0430",
"Warn": "\u041f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435",
"Valid": "\u0412\u0430\u043b\u0438\u0434\u043d\u043e",
"To open the popup, press Shift+Enter": "\u0417\u0430 \u0434\u0430 \u043e\u0442\u0432\u043e\u0440\u0438\u0442\u0435 \u0438\u0437\u0441\u043a\u0430\u0447\u0430\u0449\u0438\u044f \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446, \u043d\u0430\u0442\u0438\u0441\u043d\u0435\u0442\u0435 Shift+Enter",
"Rich Text Area. Press ALT-0 for help.": "\u041f\u043e\u043b\u0435 \u0437\u0430 \u043e\u0431\u043e\u0433\u0430\u0442\u0435\u043d \u0442\u0435\u043a\u0441\u0442. \u041d\u0430\u0442\u0438\u0441\u043d\u0435\u0442\u0435 ALT+0 \u0437\u0430 \u043f\u043e\u043c\u043e\u0449.",
"System Font": "\u0421\u0438\u0441\u0442\u0435\u043c\u0435\u043d \u0448\u0440\u0438\u0444\u0442",
"Failed to upload image: {0}": "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043a\u0430\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435: {0}",
"Failed to load plugin: {0} from url {1}": "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u043f\u043b\u044a\u0433\u0438\u043d {0} \u043e\u0442 URL {1}",
"Failed to load plugin url: {0}": "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 URL \u043d\u0430 \u043f\u043b\u044a\u0433\u0438\u043d: {0}",
"Failed to initialize plugin: {0}": "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0438\u043d\u0438\u0446\u0438\u0430\u043b\u0438\u0437\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u043b\u044a\u0433\u0438\u043d: {0}",
"example": "\u043f\u0440\u0438\u043c\u0435\u0440",
"Search": "\u0422\u044a\u0440\u0441\u0435\u043d\u0435",
"All": "\u0412\u0441\u0438\u0447\u043a\u0438",
"Currency": "\u0412\u0430\u043b\u0443\u0442\u0430",
"Text": "\u0422\u0435\u043a\u0441\u0442",
"Quotations": "\u0426\u0438\u0442\u0430\u0442\u0438",
"Mathematical": "\u041c\u0430\u0442\u0435\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438",
"Extended Latin": "\u0420\u0430\u0437\u0448\u0438\u0440\u0435\u043d\u0438 \u043b\u0430\u0442\u0438\u043d\u0441\u043a\u0438 \u0431\u0443\u043a\u0432\u0438",
"Symbols": "\u0421\u0438\u043c\u0432\u043e\u043b\u0438",
"Arrows": "\u0421\u0442\u0440\u0435\u043b\u043a\u0438",
"User Defined": "\u0417\u0430\u0434\u0430\u0434\u0435\u043d\u0438 \u043e\u0442 \u043f\u043e\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043b\u044f",
"dollar sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0434\u043e\u043b\u0430\u0440",
"currency sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0432\u0430\u043b\u0443\u0442\u0430",
"euro-currency sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0435\u0432\u0440\u043e \u0432\u0430\u043b\u0443\u0442\u0430",
"colon sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043a\u043e\u043b\u043e\u043d",
"cruzeiro sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043a\u0440\u0443\u0437\u0435\u0439\u0440\u043e",
"french franc sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0444\u0440\u0435\u043d\u0441\u043a\u0438 \u0444\u0440\u0430\u043d\u043a",
"lira sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043b\u0438\u0440\u0430",
"mill sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043c\u0438\u043b",
"naira sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043d\u0430\u0439\u0440\u0430",
"peseta sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043f\u0435\u0441\u0435\u0442\u0430",
"rupee sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0440\u0443\u043f\u0438\u044f",
"won sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043a\u043e\u0440\u0435\u0439\u0441\u043a\u0438 \u0432\u043e\u043d",
"new sheqel sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043d\u043e\u0432 \u0448\u0435\u043a\u0435\u043b",
"dong sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0432\u0438\u0435\u0442\u043d\u0430\u043c\u0441\u043a\u0438 \u0434\u043e\u043d\u0433",
"kip sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043b\u0430\u043e\u0441\u043a\u0438 \u043a\u0438\u043f",
"tugrik sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043c\u043e\u043d\u0433\u043e\u043b\u0441\u043a\u0438 \u0442\u0443\u0433\u0440\u0438\u043a",
"drachma sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0434\u0440\u0430\u0445\u043c\u0430",
"german penny symbol": "\u0441\u0438\u043c\u0432\u043e\u043b \u0437\u0430 \u0433\u0435\u0440\u043c\u0430\u043d\u0441\u043a\u043e \u043f\u0435\u043d\u0438",
"peso sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043f\u0435\u0441\u043e",
"guarani sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0433\u0443\u0430\u0440\u0430\u043d\u0438",
"austral sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0430\u0443\u0441\u0442\u0440\u0430\u043b",
"hryvnia sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0433\u0440\u0438\u0432\u043d\u044f",
"cedi sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0441\u0435\u0434\u0438",
"livre tournois sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043b\u0438\u0432\u0440 \u0442\u0443\u0440\u043d\u0443\u0430",
"spesmilo sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0441\u043f\u0435\u0441\u043c\u0438\u043b\u043e",
"tenge sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0442\u0435\u043d\u0433\u0435",
"indian rupee sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0438\u043d\u0434\u0438\u0439\u0441\u043a\u0430 \u0440\u0443\u043f\u0438\u044f",
"turkish lira sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0442\u0443\u0440\u0441\u043a\u0430 \u043b\u0438\u0440\u0430",
"nordic mark sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043d\u043e\u0440\u0434\u0441\u043a\u0430 \u043c\u0430\u0440\u043a\u0430",
"manat sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u043c\u0430\u043d\u0430\u0442",
"ruble sign": "\u0437\u043d\u0430\u043a \u0437\u0430 \u0440\u0443\u0431\u043b\u0430",
"yen character": "\u0441\u0438\u043c\u0432\u043e\u043b \u0437\u0430 \u0439\u0435\u043d\u0430",
"yuan character": "\u0441\u0438\u043c\u0432\u043e\u043b \u0437\u0430 \u044e\u0430\u043d",
"yuan character, in hong kong and taiwan": "\u0441\u0438\u043c\u0432\u043e\u043b \u0437\u0430 \u044e\u0430\u043d \u0432 \u0425\u043e\u043d\u043a\u043e\u043d\u0433 \u0438 \u0422\u0430\u0439\u0432\u0430\u043d",
"yen\/yuan character variant one": "\u0441\u0438\u043c\u0432\u043e\u043b \u0437\u0430 \u0439\u0435\u043d\u0430\/\u044e\u0430\u043d \u0432\u0430\u0440\u0438\u0430\u043d\u0442 \u0435\u0434\u043d\u043e",
"Loading emoticons...": "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0435\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0438...",
"Could not load emoticons": "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0435\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0438",
"People": "\u0425\u043e\u0440\u0430",
"Animals and Nature": "\u0416\u0438\u0432\u043e\u0442\u043d\u0438 \u0438 \u043f\u0440\u0438\u0440\u043e\u0434\u0430",
"Food and Drink": "\u0425\u0440\u0430\u043d\u0430 \u0438 \u043d\u0430\u043f\u0438\u0442\u043a\u0438",
"Activity": "\u0414\u0435\u0439\u043d\u043e\u0441\u0442\u0438",
"Travel and Places": "\u041f\u044a\u0442\u0443\u0432\u0430\u043d\u0435 \u0438 \u043c\u0435\u0441\u0442\u0430",
"Objects": "\u041f\u0440\u0435\u0434\u043c\u0435\u0442\u0438",
"Flags": "\u0417\u043d\u0430\u043c\u0435\u043d\u0430",
"Characters": "\u0421\u0438\u043c\u0432\u043e\u043b\u0438",
"Characters (no spaces)": "\u0421\u0438\u043c\u0432\u043e\u043b\u0438 (\u0431\u0435\u0437 \u0438\u043d\u0442\u0435\u0440\u0432\u0430\u043b\u0438)",
"{0} characters": "{0} \u0441\u0438\u043c\u0432\u043e\u043b\u0430",
"Error: Form submit field collision.": "\u0413\u0440\u0435\u0448\u043a\u0430: \u041d\u0435\u0441\u044a\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0435 \u043d\u0430 \u043f\u043e\u043b\u0435 \u043f\u0440\u0438 \u0438\u0437\u043f\u0440\u0430\u0449\u0430\u043d\u0435 \u043d\u0430 \u0444\u043e\u0440\u043c\u0443\u043b\u044f\u0440.",
"Error: No form element found.": "\u0413\u0440\u0435\u0448\u043a\u0430: \u041d\u0435 \u0435 \u043e\u0442\u043a\u0440\u0438\u0442 \u0435\u043b\u0435\u043c\u0435\u043d\u0442 \u043d\u0430 \u0444\u043e\u0440\u043c\u0443\u043b\u044f\u0440\u0430.",
"Update": "\u0410\u043a\u0442\u0443\u0430\u043b\u0438\u0437\u0438\u0440\u0430\u043d\u0435",
"Color swatch": "\u0426\u0432\u0435\u0442\u043d\u0430 \u043c\u043e\u0441\u0442\u0440\u0430",
"Turquoise": "\u0422\u044e\u0440\u043a\u043e\u0430\u0437\u0435\u043d\u043e",
"Green": "\u0417\u0435\u043b\u0435\u043d\u043e",
"Blue": "\u0421\u0438\u043d\u044c\u043e",
"Purple": "\u041b\u0438\u043b\u0430\u0432\u043e",
"Navy Blue": "\u041c\u043e\u0440\u0441\u043a\u043e\u0441\u0438\u043d\u044c\u043e",
"Dark Turquoise": "\u0422\u044a\u043c\u043d\u043e\u0442\u044e\u0440\u043a\u043e\u0430\u0437\u0435\u043d\u043e",
"Dark Green": "\u0422\u044a\u043c\u043d\u043e\u0437\u0435\u043b\u0435\u043d\u043e",
"Medium Blue": "\u0421\u0440\u0435\u0434\u043d\u043e\u0441\u0438\u043d\u044c\u043e",
"Medium Purple": "\u0421\u0440\u0435\u0434\u043d\u043e\u043b\u0438\u043b\u0430\u0432\u043e",
"Midnight Blue": "\u0421\u0440\u0435\u0434\u043d\u043e\u0449\u043d\u043e \u0441\u0438\u043d\u044c\u043e",
"Yellow": "\u0416\u044a\u043b\u0442\u043e",
"Orange": "\u041e\u0440\u0430\u043d\u0436\u0435\u0432\u043e",
"Red": "\u0427\u0435\u0440\u0432\u0435\u043d\u043e",
"Light Gray": "\u0421\u0432\u0435\u0442\u043b\u043e\u0441\u0438\u0432\u043e",
"Gray": "\u0421\u0438\u0432\u043e",
"Dark Yellow": "\u0422\u044a\u043c\u043d\u043e\u0436\u044a\u043b\u0442\u043e",
"Dark Orange": "\u0422\u044a\u043c\u043d\u043e\u043e\u0440\u0430\u043d\u0436\u0435\u0432\u043e",
"Dark Red": "\u0422\u044a\u043c\u043d\u043e\u0447\u0435\u0440\u0432\u0435\u043d\u043e",
"Medium Gray": "\u0421\u0440\u0435\u0434\u043d\u043e\u0441\u0438\u0432\u043e",
"Dark Gray": "\u0422\u044a\u043c\u043d\u043e\u0441\u0438\u0432\u043e",
"Light Green": "\u0421\u0432\u0435\u0442\u043b\u043e\u0437\u0435\u043b\u0435\u043d",
"Light Yellow": "\u0421\u0432\u0435\u0442\u043b\u043e\u0436\u044a\u043b\u0442",
"Light Red": "\u0421\u0432\u0435\u0442\u043b\u043e\u0447\u0435\u0440\u0432\u0435\u043d",
"Light Purple": "\u0421\u0432\u0435\u0442\u043b\u043e\u043b\u0438\u043b\u0430\u0432",
"Light Blue": "\u0421\u0432\u0435\u0442\u043b\u043e\u0441\u0438\u043d",
"Dark Purple": "\u0422\u044a\u043c\u043d\u043e\u043b\u0438\u043b\u0430\u0432",
"Dark Blue": "\u0422\u044a\u043c\u043d\u043e\u0441\u0438\u043d",
"Black": "\u0427\u0435\u0440\u043d\u043e",
"White": "\u0411\u044f\u043b\u043e",
"Switch to or from fullscreen mode": "\u041f\u0440\u0435\u0432\u043a\u043b\u044e\u0447\u0432\u0430\u043d\u0435 \u043a\u044a\u043c \u0438\u043b\u0438 \u043e\u0442 \u0440\u0435\u0436\u0438\u043c \u043d\u0430 \u0446\u044f\u043b \u0435\u043a\u0440\u0430\u043d",
"Open help dialog": "\u041e\u0442\u0432\u0430\u0440\u044f\u043d\u0435 \u043d\u0430 \u0434\u0438\u0430\u043b\u043e\u0433\u043e\u0432 \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446 \u0437\u0430 \u043f\u043e\u043c\u043e\u0449",
"history": "\u0438\u0441\u0442\u043e\u0440\u0438\u044f",
"styles": "\u0441\u0442\u0438\u043b\u043e\u0432\u0435",
"formatting": "\u0444\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d\u0435",
"alignment": "\u043f\u043e\u0434\u0440\u0430\u0432\u043d\u044f\u0432\u0430\u043d\u0435",
"indentation": "\u043e\u0442\u0441\u0442\u044a\u043f",
"permanent pen": "\u043f\u043e\u0441\u0442\u043e\u044f\u043d\u043d\u0430 \u0445\u0438\u043c\u0438\u043a\u0430\u043b\u043a\u0430",
"comments": "\u043a\u043e\u043c\u0435\u043d\u0442\u0430\u0440\u0438",
"Format Painter": "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0444\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u0430\u043d\u0435\u0442\u043e",
"Insert\/edit iframe": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435\/\u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043d\u0435 \u043d\u0430 iframe",
"Capitalization": "\u0413\u043b\u0430\u0432\u043d\u0430 \u0431\u0443\u043a\u0432\u0430",
"lowercase": "\u043c\u0430\u043b\u043a\u0438 \u0431\u0443\u043a\u0432\u0438",
"UPPERCASE": "\u0413\u041e\u041b\u0415\u041c\u0418 \u0411\u0423\u041a\u0412\u0418",
"Title Case": "\u0417\u0430\u0433\u043b\u0430\u0432\u043d\u0438 \u0411\u0443\u043a\u0432\u0438",
"Permanent Pen Properties": "\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u041f\u0435\u0440\u043c\u0430\u043d\u0435\u043d\u0442\u043d\u0438\u044f \u041c\u0430\u0440\u043a\u0435\u0440",
"Permanent pen properties...": "\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u043f\u0435\u0440\u043c\u0430\u043d\u0435\u043d\u0442\u043d\u0438\u044f \u043c\u0430\u0440\u043a\u0435\u0440...",
"Font": "\u0428\u0440\u0438\u0444\u0442",
"Size": "\u0420\u0430\u0437\u043c\u0435\u0440",
"More...": "\u041e\u0449\u0435...",
"Spellcheck Language": "\u041f\u0440\u043e\u0432\u0435\u0440\u0438 \u041f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430",
"Select...": "\u0418\u0437\u0431\u0435\u0440\u0438...",
"Preferences": "\u041f\u0440\u0435\u0434\u043f\u043e\u0447\u0438\u0442\u0430\u043d\u0438\u044f",
"Yes": "\u0414\u0430",
"No": "\u041d\u0435",
"Keyboard Navigation": "\u041d\u0430\u0432\u0438\u0433\u0430\u0446\u0438\u044f \u0441 \u043a\u043b\u0430\u0432\u0438\u0430\u0442\u0443\u0440\u0430",
"Version": "\u0412\u0435\u0440\u0441\u0438\u044f",
"Anchor": "\u041a\u043e\u0442\u0432\u0430 (\u0432\u0440\u044a\u0437\u043a\u0430 \u0432 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430)",
"Special character": "\u0421\u043f\u0435\u0446\u0438\u0430\u043b\u0435\u043d \u0437\u043d\u0430\u043a",
"Code sample": "\u041f\u0440\u0438\u043c\u0435\u0440\u0435\u043d \u043a\u043e\u0434",
"Color": "\u0426\u0432\u044f\u0442",
"Emoticons": "\u0415\u043c\u043e\u0442\u0438\u043a\u043e\u043d\u0438",
"Document properties": "\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u0430 \u043d\u0430 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430",
"Image": "\u041a\u0430\u0440\u0442\u0438\u043d\u043a\u0430",
"Insert link": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0445\u0438\u043f\u0435\u0440\u0432\u0440\u044a\u0437\u043a\u0430 (\u043b\u0438\u043d\u043a)",
"Target": "\u0426\u0435\u043b \u0432 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430",
"Link": "\u0412\u0440\u044a\u0437\u043a\u0430(\u043b\u0438\u043d\u043a)",
"Poster": "\u041f\u043e\u0441\u0442\u0435\u0440",
"Media": "\u041c\u0435\u0434\u0438\u044f",
"Print": "\u041f\u0435\u0447\u0430\u0442",
"Prev": "\u041f\u0440\u0435\u0434\u0438\u0448\u0435\u043d",
"Find and replace": "\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u0438 \u0437\u0430\u043c\u044f\u043d\u0430",
"Whole words": "\u0421\u0430\u043c\u043e \u0446\u0435\u043b\u0438 \u0434\u0443\u043c\u0438",
"Spellcheck": "\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u043f\u0440\u0430\u0432\u043e\u043f\u0438\u0441\u0430",
"Caption": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0437\u0430\u0433\u043b\u0430\u0432\u0438\u0435 \u043f\u0440\u0435\u0434\u0438 \u0442\u0430\u0431\u043b\u0438\u0446\u0430\u0442\u0430",
"Insert template": "\u0414\u043e\u0431\u0430\u0432\u044f\u043d\u0435 \u043d\u0430 \u0448\u0430\u0431\u043b\u043e\u043d"
});