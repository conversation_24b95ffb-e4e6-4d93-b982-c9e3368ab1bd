<?php

namespace App\Admin\Controllers;

use App\Models\ContractTemplate;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ContractTemplateController extends AdminController
{
    protected $title = '合同模板管理';

    protected function grid(): Grid
    {
        return Grid::make(new ContractTemplate(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '模板名称');
            $grid->column('file_type', '文件类型')
                 ->using(ContractTemplate::$fileTypes)
                 ->badge([
                     'pdf'   => 'primary',
                     'html'  => 'info',
                     'word'  => 'warning',
                     'other' => 'default',
                 ]);
            $grid->column('status', '状态')
                 ->using(ContractTemplate::$statuses)
                 ->badge([
                     0 => 'danger',
                     1 => 'success',
                 ]);
            $grid->column('preview', '预览')
                 ->display(function () {
                     $url = \Storage::url($this->file_path);
                     return "<a href=\"javascript:window.open('{$url}','预览','width=900,height=600')\">点击预览</a>";
                 });
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '模板名称');
                $filter->equal('file_type', '文件类型')->select(ContractTemplate::$fileTypes);
                $filter->equal('status', '状态')->select(ContractTemplate::$statuses);
            });
        });
    }

    protected function detail($id): Show
    {
        return Show::make($id, new ContractTemplate(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('name', '模板名称');
            $show->field('file_type', '文件类型')->using(ContractTemplate::$fileTypes);
            $show->field('mime_type', 'MIME 类型');
            $show->field('description', '描述');
            $show->field('status', '状态')->using(ContractTemplate::$statuses);
            $show->field('file_path', '文件')->unescape()->as(function ($path) {
                $url = \Storage::url($path);
                return "<a href=\"{$url}\" target=\"_blank\">下载 / 预览</a>";
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(ContractTemplate::class, function (Form $form) {
            $form->display('id', 'ID');
            $form->text('name', '模板名称')->required();
            $form->textarea('description', '描述');
            $form->file('file_path', '模板文件')
                 ->disk('public')
                 ->required()
                 ->uniqueName()
                 ->help('支持 PDF、HTML、DOC、DOCX 等格式');
            $form->radio('status', '状态')
                 ->options(ContractTemplate::$statuses)
                 ->default(1);

            // 隐藏字段：由 saving 回调自动填充
            $form->hidden('file_type');
            $form->hidden('mime_type');

            // 上传时自动检测文件类型和 MIME
            $form->saving(function (Form $form) {
                if ($form->file_path) {
                    $ext = strtolower(pathinfo($form->file_path, PATHINFO_EXTENSION));
                    if ($ext === 'pdf') {
                        $form->file_type = 'pdf';
                    } elseif (in_array($ext, ['html', 'htm'])) {
                        $form->file_type = 'html';
                    } elseif (in_array($ext, ['doc', 'docx'])) {
                        $form->file_type = 'word';
                    } else {
                        $form->file_type = 'other';
                    }
                    // 通过 Storage 获取 MIME 类型
                    $form->mime_type = \Storage::disk('public')->mimeType($form->file_path);
                }
            });
        });
    }
}
