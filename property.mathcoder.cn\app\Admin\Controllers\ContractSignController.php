<?php

namespace App\Admin\Controllers;

use App\Models\ContractSign;
use App\Models\ContractTemplate;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\SendContract;

class ContractSignController extends AdminController
{
    protected $title = '合同签署管理';

    protected function grid(): Grid
    {
        return Grid::make(new ContractSign(), function (Grid $grid) {
            $grid->model()->with(['template', 'user']);
            $grid->column('id', 'ID')->sortable();
            $grid->column('template.name', '合同模板');
            $grid->column('user.name', '签署用户');
            $grid->column('water_meter_number', '水表号');
            $grid->column('status', '签署状态')
                 ->using(ContractSign::$statuses)
                 ->badge([
                     'pending'  => 'default',
                     'sent'     => 'info',
                     'signed'   => 'success',
                     'rejected' => 'danger',
                 ]);
            $grid->column('signature_name', '签名人');
            $grid->column('signed_at', '签署时间')->sortable()->display(function ($signed_at) {
                     if(empty($signed_at)||is_null($signed_at)){
                         return '暂未签署';
                     }else{
                         return $signed_at;
                     }
                 });
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();
            $grid->disableViewButton();
            
           // 注册“发送给用户”按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                 if ($actions->row->status === 'pending') {
                     $actions->append(new SendContract());
                 }elseif($actions->row->status === 'signed'){
                     $actions->disableDelete();
                     $actions->disableEdit();
                 }else{
                     $actions->disableEdit();
                 }
                
            });

            $grid->filter(function ($filter) {
                $filter->equal('contract_template_id', '合同模板')
                       ->select(ContractTemplate::pluck('name', 'id'));
                $filter->equal('user_id', '签署用户')
                       ->select(User::pluck('name', 'id'));
                $filter->equal('status', '签署状态')->select(ContractSign::$statuses);
            });
        });
    }

    protected function detail($id): Show
    {
        return Show::make($id, new ContractSign(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('template.name', '合同模板');
            $show->field('user.name', '签署用户');
            $show->field('status', '签署状态')->using(ContractSign::$statuses);
            $show->field('signed_at', '签署时间');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(ContractSign::class, function (Form $form) {
            $form->display('id', 'ID');

            $form->select('contract_template_id', '合同模板')
                 ->options(ContractTemplate::where('status',1)->pluck('name', 'id'))
                 ->required();

          // 签署用户（远程搜索）
            $form->select('user_id', '签署用户')
                 // 指定 Model、valueField、textField
                 ->options(User::class, 'id', 'name')
                 // ajax 搜索接口（会请求 /admin/api/users?q=关键词）
                 ->ajax('api/users')
                 ->required();

            $form->text('water_meter_number', '水表号');
            $form->creating(function (Form $form) {
                $form->hidden('status', '签署状态');
                $form->saving(function (Form $form) {
                    $form->status = 'pending';
                });
                $form->hidden('signed_at', '签署时间')->default(null);
            });
            
            $form->editing(function (Form $form) {
                 $form->datetime('signed_at', '签署时间')
                 ->disable();
            });
        });
    }
    
    /**
     * 发送给用户操作
     */
    public function send($id)
    {
        $model = ContractSign::findOrFail($id);
    
        if ($model->status !== 'pending') {
            return $this->response()->error('只有「待发送」状态才能发送')->refresh();
        }
    
        $model->status = 'sent';
        $model->save();
    
        return $this->response()->success('发送成功')->refresh();
    }

}
