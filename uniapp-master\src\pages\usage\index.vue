<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '用量分析'
  }
}
</route>

<template>
  <view class="usage-page">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @tap="showAccountFilter">
        <text class="filter-text">{{ currentAccountText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" @tap="showPeriodFilter">
        <text class="filter-text">{{ currentPeriodText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>

    <!-- 用量统计卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">本月用量统计</text>
        <text class="stats-date">{{ currentMonth }}</text>
      </view>
      <view class="stats-content">
        <view class="stat-item">
          <text class="stat-value">{{ monthlyStats.currentUsage }}</text>
          <text class="stat-label">本月用量(m³)</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ monthlyStats.lastMonthUsage }}</text>
          <text class="stat-label">上月用量(m³)</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value" :class="{ 'increase': monthlyStats.changeRate > 0, 'decrease': monthlyStats.changeRate < 0 }">
            {{ monthlyStats.changeRate > 0 ? '+' : '' }}{{ monthlyStats.changeRate }}%
          </text>
          <text class="stat-label">环比变化</text>
        </view>
      </view>
    </view>

    <!-- 用量记录列表 -->
    <scroll-view 
      class="usage-list"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="usageRecords.length === 0 && !loading" class="empty">
        <text class="empty-text">暂无用量记录</text>
      </view>
      
      <view v-else class="list-content">
        <view 
          v-for="record in usageRecords" 
          :key="record.id"
          class="usage-item"
          @tap="viewDetail(record.id)"
        >
          <view class="usage-left">
            <view class="usage-header">
              <text class="usage-period">{{ record.period }}</text>
              <text class="usage-status" :class="record.status">
                {{ getStatusText(record.status) }}
              </text>
            </view>
            <text class="usage-meter">水表号：{{ record.water_meter_no }}</text>
            <text class="usage-address">{{ record.address }}</text>
            <view class="usage-readings">
              <text class="reading-item">起始：{{ record.start_reading }}m³</text>
              <text class="reading-item">结束：{{ record.end_reading }}m³</text>
            </view>
          </view>
          <view class="usage-right">
            <text class="usage-amount">{{ record.usage }}</text>
            <text class="usage-unit">m³</text>
            <text class="usage-cost">¥{{ record.cost }}</text>
          </view>
        </view>
      </view>

      <view v-if="loading && usageRecords.length > 0" class="loading-more">
        <text>加载更多...</text>
      </view>

      <view v-if="noMore && usageRecords.length > 0" class="no-more">
        <text>没有更多了</text>
      </view>
    </scroll-view>

    <!-- 账户筛选弹窗 -->
    <wd-popup
      v-model="showAccountPicker"
      position="bottom"
      custom-style="border-radius: 20rpx 20rpx 0 0;"
    >
      <view class="account-picker">
        <view class="picker-header">
          <text class="picker-title">选择账户</text>
          <text class="picker-close" @tap="showAccountPicker = false">✕</text>
        </view>
        <view class="account-options">
          <view 
            v-for="account in accountOptions"
            :key="account.value"
            class="account-option"
            :class="{ active: currentAccount === account.value }"
            @tap="selectAccount(account.value)"
          >
            <view class="account-info">
              <text class="account-meter">{{ account.water_meter_no }}</text>
              <text class="account-address">{{ account.address }}</text>
            </view>
            <text v-if="currentAccount === account.value" class="option-check">✓</text>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 时间筛选弹窗 -->
    <wd-popup
      v-model="showPeriodPicker"
      position="bottom"
      custom-style="border-radius: 20rpx 20rpx 0 0;"
    >
      <view class="period-picker">
        <view class="picker-header">
          <text class="picker-title">选择时间</text>
          <text class="picker-close" @tap="showPeriodPicker = false">✕</text>
        </view>
        <view class="period-options">
          <view 
            v-for="period in periodOptions"
            :key="period.value"
            class="period-option"
            :class="{ active: currentPeriod === period.value }"
            @tap="selectPeriod(period.value)"
          >
            <text class="option-text">{{ period.label }}</text>
            <text v-if="currentPeriod === period.value" class="option-check">✓</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const usageRecords = ref([])
const loading = ref(false)
const refreshing = ref(false)
const noMore = ref(false)
const currentPage = ref(1)
const currentAccount = ref('')
const currentPeriod = ref('recent6')
const showAccountPicker = ref(false)
const showPeriodPicker = ref(false)

// 页面参数
const pageParams = ref({
  account_id: '',
  account_name: ''
})

onLoad((options) => {
  if (options.account_id) {
    pageParams.value.account_id = options.account_id
    pageParams.value.account_name = decodeURIComponent(options.account_name || '')
  }
})

// Mock数据
const accountOptions = ref([
  { value: '', label: '全部账户', water_meter_no: '全部账户', address: '' },
  { value: '1', label: 'WM001234567', water_meter_no: 'WM001234567', address: '阳光小区 1栋2单元301' },
  { value: '2', label: 'WM001234568', water_meter_no: 'WM001234568', address: '阳光小区 2栋1单元201' }
])

const periodOptions = ref([
  { value: 'recent3', label: '最近3个月' },
  { value: 'recent6', label: '最近6个月' },
  { value: 'recent12', label: '最近12个月' },
  { value: 'thisYear', label: '本年度' },
  { value: 'lastYear', label: '上年度' }
])

const monthlyStats = ref({
  currentUsage: 25.6,
  lastMonthUsage: 23.2,
  changeRate: 10.3
})

const currentMonth = computed(() => {
  const now = new Date()
  return `${now.getFullYear()}年${now.getMonth() + 1}月`
})

const currentAccountText = computed(() => {
  const option = accountOptions.value.find(item => item.value === currentAccount.value)
  return option ? option.water_meter_no : '全部账户'
})

const currentPeriodText = computed(() => {
  const option = periodOptions.value.find(item => item.value === currentPeriod.value)
  return option ? option.label : '最近6个月'
})

// Mock用量记录数据
const mockUsageRecords = [
  {
    id: 1,
    period: '2024年1月',
    water_meter_no: 'WM001234567',
    address: '阳光小区 1栋2单元301',
    start_reading: 1245.6,
    end_reading: 1271.2,
    usage: 25.6,
    cost: 89.60,
    status: 'confirmed',
    reading_date: '2024-01-31'
  },
  {
    id: 2,
    period: '2023年12月',
    water_meter_no: 'WM001234567',
    address: '阳光小区 1栋2单元301',
    start_reading: 1222.4,
    end_reading: 1245.6,
    usage: 23.2,
    cost: 81.20,
    status: 'confirmed',
    reading_date: '2023-12-31'
  },
  {
    id: 3,
    period: '2023年11月',
    water_meter_no: 'WM001234567',
    address: '阳光小区 1栋2单元301',
    start_reading: 1198.7,
    end_reading: 1222.4,
    usage: 23.7,
    cost: 82.95,
    status: 'confirmed',
    reading_date: '2023-11-30'
  }
]

const fetchUsageRecords = async (page = 1, isRefresh = false) => {
  if (loading.value) return

  try {
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock数据筛选
    let filteredRecords = [...mockUsageRecords]
    
    if (currentAccount.value) {
      const account = accountOptions.value.find(acc => acc.value === currentAccount.value)
      if (account && account.water_meter_no !== '全部账户') {
        filteredRecords = filteredRecords.filter(record => 
          record.water_meter_no === account.water_meter_no
        )
      }
    }
    
    if (isRefresh || page === 1) {
      usageRecords.value = filteredRecords
    } else {
      usageRecords.value = [...usageRecords.value, ...filteredRecords]
    }
    
    noMore.value = true // Mock数据没有更多
    currentPage.value = page
    
  } catch (error) {
    console.error('获取用量记录失败:', error)
    uni.showToast({
      title: '获取记录失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const loadMore = () => {
  if (!noMore.value && !loading.value) {
    fetchUsageRecords(currentPage.value + 1)
  }
}

const onRefresh = () => {
  refreshing.value = true
  noMore.value = false
  fetchUsageRecords(1, true)
}

const showAccountFilter = () => {
  showAccountPicker.value = true
}

const selectAccount = (accountId: string) => {
  currentAccount.value = accountId
  showAccountPicker.value = false
  
  // 重新加载数据
  noMore.value = false
  fetchUsageRecords(1, true)
}

const showPeriodFilter = () => {
  showPeriodPicker.value = true
}

const selectPeriod = (period: string) => {
  currentPeriod.value = period
  showPeriodPicker.value = false
  
  // 重新加载数据
  noMore.value = false
  fetchUsageRecords(1, true)
}

const viewDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/usage/detail?id=${id}`
  })
}

const getStatusText = (status: string) => {
  const statusMap = {
    confirmed: '已确认',
    pending: '待确认',
    estimated: '预估值'
  }
  return statusMap[status] || '未知'
}

onMounted(() => {
  // 如果页面传入了账户ID，设置为默认选中
  if (pageParams.value.account_id) {
    const targetAccount = accountOptions.value.find(account => account.value === pageParams.value.account_id)
    if (targetAccount) {
      currentAccount.value = pageParams.value.account_id
    }
  }

  fetchUsageRecords()
})
</script>

<style scoped>
.usage-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.filter-bar {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  gap: 16rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  flex: 1;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
}

.filter-arrow {
  font-size: 24rpx;
  color: #666;
}

.stats-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-date {
  font-size: 26rpx;
  color: #666;
}

.stats-content {
  display: flex;
  align-items: center;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value.increase {
  color: #ff3b30;
}

.stat-value.decrease {
  color: #34c759;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: #f0f0f0;
  margin: 0 24rpx;
}

.usage-list {
  flex: 1;
  padding: 0 20rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.usage-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.usage-period {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.usage-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.usage-status.confirmed {
  background: #e8f5e8;
  color: #34c759;
}

.usage-status.pending {
  background: #fff3e0;
  color: #ff9500;
}

.usage-status.estimated {
  background: #e3f2fd;
  color: #007aff;
}

.usage-meter {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.usage-address {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
}

.usage-readings {
  display: flex;
  gap: 24rpx;
}

.reading-item {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
}

.usage-right {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.usage-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #007aff;
}

.usage-unit {
  font-size: 24rpx;
  color: #666;
  margin-top: -8rpx;
}

.usage-cost {
  font-size: 28rpx;
  color: #333;
  background: #f0f9ff;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.loading-more,
.no-more {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 26rpx;
}

.account-picker,
.period-picker {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 32rpx;
  color: #666;
}

.account-options,
.period-options {
  padding: 0 32rpx 32rpx;
}

.account-option,
.period-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.account-option:last-child,
.period-option:last-child {
  border-bottom: none;
}

.account-option.active,
.period-option.active {
  color: #007aff;
}

.account-info {
  flex: 1;
}

.account-meter {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.account-address {
  font-size: 26rpx;
  color: #666;
}

.option-text {
  font-size: 30rpx;
}

.option-check {
  font-size: 32rpx;
  color: #007aff;
}
</style>
