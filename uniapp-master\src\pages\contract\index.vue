<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '合同管理',
  },
}
</route>

<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <view class="navbar" style="padding-top: 64px;">
      <text class="navbar-title">合同管理</text>
    </view>

    <!-- 状态切换 -->
    <view class="tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'sent' }" 
        @click="changeStatus('sent')"
      >
        <text class="tab-count">{{ stats.pending || 0 }}</text>
        <text class="tab-label">待签署</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'signed' }" 
        @click="changeStatus('signed')"
      >
        <text class="tab-count">{{ stats.signed || 0 }}</text>
        <text class="tab-label">已签署</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'rejected' }" 
        @click="changeStatus('rejected')"
      >
        <text class="tab-count">{{ stats.rejected || 0 }}</text>
        <text class="tab-label">已拒绝</text>
      </view>
    </view>

    <!-- 合同列表 -->
    <scroll-view class="list-container" scroll-y @scrolltolower="loadMore">
      <view v-if="loading && contracts.length === 0" class="loading">
        <text>加载中...</text>
      </view>
      
      <view v-else-if="contracts.length === 0" class="empty">
        <text>暂无{{ getStatusText(currentStatus) }}合同</text>

      </view>
      
      <view v-else class="contract-list">
        <view v-for="contract in contracts" :key="contract.id" class="contract-item">
          <view class="contract-header">
            <text class="contract-title">{{ contract.template?.name || '合同' }}</text>
            <text class="contract-status" :class="'status-' + contract.status">
              {{ getStatusText(contract.status) }}
            </text>
          </view>
          
          <view class="contract-info">
            <text class="info-text">编号：{{ contract.id }}</text>
            <text class="info-text">时间：{{ formatDate(contract.created_at) }}</text>
          </view>
          
          <view v-if="contract.status === 'sent'" class="contract-actions">
            <button class="btn btn-primary" @click="viewContract(contract.id)">查看合同</button>
          </view>
        </view>
      </view>
      
      <view v-if="loading && contracts.length > 0" class="loading-more">
        <text>加载更多...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getContracts, getContractStatistics } from '@/api/contract'

// 状态栏高度
const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 20

// 响应式数据
const currentStatus = ref('sent')
const contracts = ref([])
const stats = ref({
  pending: 0,
  signed: 0,
  rejected: 0
})
const loading = ref(false)
const page = ref(1)
const hasMore = ref(true)

// 页面加载
onMounted(() => {
  loadStats()
  loadContracts()

  // 监听合同操作完成事件，刷新数据
  uni.$on('refreshContractList', () => {
    console.log('收到刷新合同列表事件')
    loadStats()
    loadContracts(true) // 重新加载第一页
  })
})

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshContractList')
})

// 页面显示时刷新数据
onShow(() => {
  loadStats()
  loadContracts(true)
})

// 加载统计数据
const loadStats = async () => {
  try {
    const res = await getContractStatistics()
    if (res.success || res.code === 0) {
      // 后端返回：{pending, signed, rejected, total}
      // 前端需要：{pending, signed, rejected}
      const data = res.data.data || res.data
      stats.value = {
        pending: data.pending || 0,
        signed: data.signed || 0,
        rejected: data.rejected || 0
      }
    }
  } catch (error) {
    console.error('加载统计失败:', error)
  }
}

// 加载合同列表
const loadContracts = async (reset = false) => {
  if (loading.value) return

  loading.value = true

  try {
    const currentPage = reset ? 1 : page.value
    const res = await getContracts({
      page: currentPage,
      page_size: 10,
      status: currentStatus.value
    })
    
    if ((res.success || res.code === 0) && res.data) {
      // 后端返回结构：{code: 0, data: {success: true, data: {data: [...], has_more: true}}}
      const responseData = res.data.data || res.data
      const newContracts = responseData.data || []

      if (Array.isArray(newContracts)) {
        if (reset) {
          contracts.value = newContracts
          page.value = 1
        } else {
          contracts.value.push(...newContracts)
        }

        hasMore.value = responseData.has_more || false
        if (hasMore.value) {
          page.value++
        }
      }
    }
  } catch (error) {
    console.error('加载合同列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 切换状态
const changeStatus = (status) => {
  if (currentStatus.value === status) return

  currentStatus.value = status
  page.value = 1
  hasMore.value = true
  loadContracts(true)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadContracts()
  }
}

// 查看合同详情
const viewContract = (id) => {
  uni.navigateTo({
    url: `/pages/contract/detail?id=${id}`
  })
}

// 签署合同
const signContract = async (id) => {
  try {
    uni.showLoading({ title: '签署中...' })
    const res = await apiSignContract(id)
    
    if (res.success || res.code === 0) {
      uni.showToast({
        title: '签署成功',
        icon: 'success'
      })
      loadStats()
      loadContracts(true)
    } else {
      uni.showToast({
        title: res.message || '签署失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('签署失败:', error)
    uni.showToast({
      title: '签署失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}



// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    sent: '待签署',
    signed: '已签署',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''

  // 将 "2025-07-16 14:55:37" 格式转换为 iOS 兼容的格式
  const isoDateStr = dateStr.replace(' ', 'T')
  const date = new Date(isoDateStr)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateStr)
    return dateStr
  }

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.status-bar {
  background-color: #ffffff;
}

.navbar {
  background-color: #ffffff;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  padding-top: var(--status-bar-height);
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.tabs {
  display: flex;
  background-color: #fff;
  margin: 15px;
  border-radius: 10px;
  padding: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background-color: #e6f7ff;
  }

  &:active {
    background-color: #f0f0f0;
    transform: scale(0.95);
  }
}

.tab-count {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.tab-label {
  font-size: 14px;
  color: #666;
}

.list-container {
  height: calc(100vh - 200px);
  padding: 0 !important;
  margin: 0 !important;
}

.loading, .empty {
  text-align: center;
  padding: 50px 0;
  color: #999;
}

.contract-list {
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.contract-item {
  background-color: #fff !important;
  border-radius: 10px !important;
  padding: 15px !important;
  margin: 0 15px 15px 15px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  width: calc(100% - 30px) !important;
  max-width: 600px !important;
  display: block !important;
  box-sizing: border-box !important;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.contract-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.contract-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;

  &.status-sent {
    background-color: #fff7e6;
    color: #fa8c16;
  }

  &.status-signed {
    background-color: #f6ffed;
    color: #52c41a;
  }

  &.status-rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
  }
}

.contract-info {
  margin-bottom: 15px;
}

.info-text {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.contract-actions {
  display: flex;
  gap: 10px;
}

.btn {
  flex: 1;
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
  border: none;

  &.btn-primary {
    background-color: #1890ff;
    color: #fff;
  }

  &.btn-secondary {
    background-color: #f5f5f5;
    color: #666;
  }
}

.loading-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}
</style>
