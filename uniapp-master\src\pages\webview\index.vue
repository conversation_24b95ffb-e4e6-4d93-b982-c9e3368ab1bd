<template>
  <view class="webview-container">
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'

const url = ref('')
const contractId = ref('')

onLoad((options) => {
  if (options.url) {
    url.value = decodeURIComponent(options.url)
    console.log('加载URL:', url.value)
  }

  // 获取合同ID，用于签署完成后的状态检查
  if (options.contractId) {
    contractId.value = options.contractId
    console.log('合同ID:', contractId.value)
  }

  // 设置页面标题
  if (options.title) {
    uni.setNavigationBarTitle({
      title: decodeURIComponent(options.title)
    })
  }
})

const handleMessage = (event) => {
  console.log('收到webview消息:', event)

  // 处理e签宝签署完成的消息
  if (event.detail && event.detail.data) {
    const data = event.detail.data

    // 检查是否为签署完成的消息
    if (data.type === 'sign_complete' || data.status === 'signed' || data.action === 'sign_success') {
      handleSignComplete()
    }
  }
}

// 处理签署完成
const handleSignComplete = () => {
  uni.showToast({
    title: '签署成功',
    icon: 'success'
  })

  // 延迟返回并刷新列表
  setTimeout(() => {
    uni.navigateBack({
      delta: 2, // 返回到列表页（跳过详情页）
      success: () => {
        // 通知列表页刷新数据
        uni.$emit('refreshContractList')
      }
    })
  }, 1500)
}

// 页面显示时检查签署状态（备用方案）
onShow(() => {
  // 如果有合同ID，可以定期检查签署状态
  if (contractId.value) {
    // 这里可以添加定期检查逻辑，但要注意性能
    console.log('页面显示，合同ID:', contractId.value)
  }
})
</script>

<style lang="scss" scoped>
.webview-container {
  width: 100%;
  height: 100vh;
}
</style>
