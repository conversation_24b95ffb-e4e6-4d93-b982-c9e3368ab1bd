<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WaterPayment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'payment_no',
        'water_meter_no',
        'amount',
        'payment_method',
        'status',
        'transaction_no',
        'paid_at',
        'remark'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_at' => 'datetime'
    ];

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 生成缴费单号
    public static function generatePaymentNo()
    {
        return 'WP' . date('YmdHis') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
    }

    // 状态选项
    public static function getStatusOptions()
    {
        return [
            'pending' => '待支付',
            'paid' => '已支付',
            'failed' => '支付失败',
            'refunded' => '已退款'
        ];
    }

    // 支付方式选项
    public static function getPaymentMethodOptions()
    {
        return [
            'wechat' => '微信支付',
            'bank_transfer' => '云闪付',
            'other' => '其他'
        ];
    }

    /**
     * 生成缴费单号
     */
    public static function generatePaymentNo()
    {
        return 'WP' . date('YmdHis') . rand(1000, 9999);
    }

    // 状态标签颜色
    public function getStatusLabelAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'paid' => 'success',
            'failed' => 'danger',
            'refunded' => 'info'
        ];
        
        $text = self::getStatusOptions()[$this->status] ?? '未知';
        $color = $colors[$this->status] ?? 'secondary';
        
        return "<span class='label label-{$color}'>{$text}</span>";
    }

    // 支付方式标签
    public function getPaymentMethodLabelAttribute()
    {
        return self::getPaymentMethodOptions()[$this->payment_method] ?? '未知';
    }
}
