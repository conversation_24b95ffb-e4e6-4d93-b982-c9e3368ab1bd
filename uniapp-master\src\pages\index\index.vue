<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '物业管理',
  },
}
</route>
<template>
  <view class="page-container" style="min-height: auto;">

    <!-- 头部区域 -->
    <view class="header-section">
      <view class="header-content">
        <view class="user-info">
          <view class="avatar">
            <text class="avatar-text">用</text>
          </view>
          <view class="user-text">
            <text class="greeting">早上好</text>
            <text class="user-name">尊敬的用户</text>
          </view>
        </view>
      </view>

      <!-- 账户卡片 -->
      <view class="account-card" v-if="hasAccounts">
        <view class="card-header">
          <view class="account-selector" @tap="showAccountSelector">
            <text class="account-title">{{ currentAccount?.community }} {{ currentAccount?.building_number }}</text>
            <text class="account-number" :class="{ 'no-meter': !currentAccount?.water_meter_number }">
              {{ currentAccount?.water_meter_number || '暂无水表号' }}
            </text>
            <text class="selector-arrow">▼</text>
          </view>
          <text class="card-status" :class="getStatusClass(currentAccount?.status)">
            {{ getStatusText(currentAccount?.status) }}
          </text>
        </view>
        <view class="card-content">
          <view class="balance-info">
            <text class="balance-label">账户余额</text>
            <text class="balance-amount">¥ {{ currentAccount?.balance || '0.00' }}</text>
          </view>
          <view class="account-actions">
            <view
              class="quick-action"
              :class="{ disabled: !currentAccount?.water_meter_number }"
              @tap="goToPayment"
            >
              <text class="quick-icon">💰</text>
              <text class="quick-text">充值</text>
            </view>
            <view class="quick-action" @tap="goToPaymentHistoryWithAccount">
              <text class="quick-icon">📊</text>
              <text class="quick-text">账单</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 无账户状态 -->
      <view class="no-account-card" v-else>
        <view class="no-account-content">
          <text class="no-account-icon">🏠</text>
          <text class="no-account-title">暂无账户</text>
          <text class="no-account-desc">请先申请开户，开通水费缴纳服务</text>
        </view>
        <view class="no-account-action" @tap="navigateTo('/pages/account/index')">
          <text class="action-text">立即开户</text>
        </view>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-container">
      <BannerSwiper ref="bannerSwiperRef" />
    </view>

    <!-- 快捷服务 -->
    <view class="quick-services">
      <view class="services-grid">
        <view class="service-item primary" @tap="navigateTo('/pages/payment/index')">
          <view class="service-icon">
            <text class="icon">💰</text>
          </view>
          <text class="service-title">水费缴纳</text>
        </view>

        <view class="service-item" @tap="navigateTo('/pages/account/index')">
          <view class="service-icon">
            <text class="icon">👤</text>
          </view>
          <text class="service-title">账户管理</text>
        </view>

        <view class="service-item" @tap="goToUsageAnalysis">
          <view class="service-icon">
            <text class="icon">📈</text>
          </view>
          <text class="service-title">用量分析</text>
        </view>
      </view>
    </view>

    <!-- 功能卡片区 -->
    <view class="feature-cards">
      <view class="feature-card" @tap="goToPaymentHistoryWithAccount">
        <view class="feature-left">
          <view class="feature-icon record">
            <text class="icon">📊</text>
          </view>
          <view class="feature-info">
            <text class="feature-title">缴费记录</text>
            <text class="feature-desc">查看水费缴纳记录</text>
          </view>
        </view>
        <view class="feature-arrow">
          <text class="arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 服务保障 -->
    <view class="guarantee-section">
      <view class="guarantee-grid">
        <view class="guarantee-item">
          <text class="guarantee-icon">🔒</text>
          <text class="guarantee-text">安全保障</text>
        </view>
        <view class="guarantee-item">
          <text class="guarantee-icon">⚡</text>
          <text class="guarantee-text">极速到账</text>
        </view>
        <view class="guarantee-item">
          <text class="guarantee-icon">🎯</text>
          <text class="guarantee-text">精准计费</text>
        </view>
        <view class="guarantee-item">
          <text class="guarantee-icon">📞</text>
          <text class="guarantee-text">24h客服</text>
        </view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import BannerSwiper from './components/BannerSwiper.vue'
import { getBoundAccounts } from '@/api/account'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 账户相关数据
const accounts = ref([])
const currentAccountIndex = ref(0)

// 计算属性
const hasAccounts = computed(() => accounts.value.length > 0)
const currentAccount = computed(() => {
  return hasAccounts.value ? accounts.value[currentAccountIndex.value] : null
})

// 获取账户列表
const fetchAccounts = async () => {
  try {
    const res = await getBoundAccounts()
    if (res.code === 0 && res.data) {
      accounts.value = res.data

      // 默认选择第一个有水表号的账户
      if (accounts.value.length > 0) {
        const firstValidAccountIndex = accounts.value.findIndex(
          account => account.water_meter_number
        )

        if (firstValidAccountIndex !== -1) {
          currentAccountIndex.value = firstValidAccountIndex
        } else {
          // 如果没有账户有水表号，默认选择第一个，但会在UI中显示提示
          currentAccountIndex.value = 0
        }
      }
    }
  } catch (error) {
    console.error('获取账户列表失败:', error)
    accounts.value = []
  }
}

// 显示账户选择器
const showAccountSelector = () => {
  // 只显示有水表号的账户
  const validAccounts = accounts.value.filter(account => account.water_meter_number)

  if (validAccounts.length <= 1) return

  const accountNames = validAccounts.map((account) => {
    return `${account.community} ${account.building_number} (${account.water_meter_number})`
  })

  uni.showActionSheet({
    itemList: accountNames,
    success: (res) => {
      const selectedAccount = validAccounts[res.tapIndex]

      // 在原始数组中找到对应的索引
      const originalIndex = accounts.value.findIndex(
        account => account.id === selectedAccount.id
      )

      if (originalIndex !== -1) {
        currentAccountIndex.value = originalIndex
      }
    }
  })
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
    case '正常':
      return 'status-active'
    case 'suspended':
    case '暂停':
      return 'status-suspended'
    case 'arrears':
    case '欠费':
      return 'status-arrears'
    default:
      return 'status-normal'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '正常'
    case 'suspended':
      return '暂停'
    case 'arrears':
      return '欠费'
    default:
      return '正常'
  }
}

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 跳转到缴费页面，并传递当前账户信息
const goToPayment = () => {
  if (currentAccount.value && currentAccount.value.water_meter_number) {
    const url = `/pages/payment/index?water_meter_no=${currentAccount.value.water_meter_number}`
    uni.navigateTo({ url })
  } else if (currentAccount.value && !currentAccount.value.water_meter_number) {
    // 如果当前账户没有水表号，提示用户
    uni.showToast({
      title: '当前账户暂无水表号，无法缴费',
      icon: 'none',
      duration: 2000
    })
  } else {
    // 如果没有当前账户，直接跳转到缴费页面
    uni.navigateTo({ url: '/pages/payment/index' })
  }
}

// 跳转到缴费记录
const goToPaymentHistory = () => {
  uni.navigateTo({
    url: '/pages/payment/history'
  })
}

// 跳转到缴费记录（带当前账户参数）
const goToPaymentHistoryWithAccount = () => {
  if (currentAccount.value && currentAccount.value.water_meter_number) {
    // 如果有当前账户且有水表号，带上账户ID参数
    const url = `/pages/payment/history?account_id=${currentAccount.value.id}&account_name=${encodeURIComponent(currentAccount.value.water_meter_number)}`
    uni.navigateTo({ url })
  } else if (hasAccounts.value) {
    // 如果有账户但当前账户没有水表号，跳转到全部记录
    uni.navigateTo({
      url: '/pages/payment/history'
    })
  } else {
    // 没有账户时提示
    uni.showToast({
      title: '请先绑定账户',
      icon: 'none'
    })
  }
}

// 跳转到用量分析
const goToUsageAnalysis = () => {
  if (currentAccount.value && currentAccount.value.water_meter_number) {
    // 如果有当前账户且有水表号，带上账户ID参数
    const url = `/pages/usage/index?account_id=${currentAccount.value.id}&account_name=${encodeURIComponent(currentAccount.value.water_meter_number)}`
    uni.navigateTo({ url })
  } else if (hasAccounts.value) {
    // 如果有账户但当前账户没有水表号，跳转到全部记录
    uni.navigateTo({
      url: '/pages/usage/index'
    })
  } else {
    // 没有账户时提示
    uni.showToast({
      title: '请先绑定账户',
      icon: 'none'
    })
  }
}

// 显示敬请期待
const showComingSoon = () => {
  uni.showToast({
    title: '功能开发中，敬请期待',
    icon: 'none'
  })
}

// 页面加载时获取账户信息
onMounted(() => {
  fetchAccounts()
})
</script>

<style lang="scss" scoped>
.page-container {
  background: #f5f7fa;
}

// 头部区域
.header-section {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 20px;
  padding-top: calc(var(--status-bar-height) + 20px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.avatar-text {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.user-text {
  display: flex;
  flex-direction: column;
}

.greeting {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  color: white;
}



// 账户卡片
.account-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.account-selector {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;

  &:active {
    opacity: 0.7;
  }
}

.account-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.account-number {
  font-size: 12px;
  color: #8c8c8c;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.account-number.no-meter {
  color: #ff4d4f;
  background: #fff2f0;
  border: 1px solid #ffccc7;
}

.selector-arrow {
  font-size: 10px;
  color: #8c8c8c;
  margin-left: 4px;
}

.card-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;

  &.status-active {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  &.status-suspended {
    color: #faad14;
    background: #fffbe6;
    border: 1px solid #ffe58f;
  }

  &.status-arrears {
    color: #ff4d4f;
    background: #fff2f0;
    border: 1px solid #ffccc7;
  }

  &.status-normal {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }
}

// 无账户卡片
.no-account-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.no-account-content {
  margin-bottom: 20px;
}

.no-account-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 12px;
}

.no-account-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  display: block;
}

.no-account-desc {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
  display: block;
}

.no-account-action {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 8px;
  padding: 12px 24px;
  transition: all 0.2s;

  &:active {
    transform: scale(0.95);
  }
}

.action-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-info {
  flex: 1;
}

.balance-label {
  font-size: 14px;
  color: #8c8c8c;
  display: block;
  margin-bottom: 4px;
}

.balance-amount {
  font-size: 28px;
  font-weight: bold;
  color: #262626;
  display: block;
}

.account-actions {
  display: flex;
  gap: 16px;
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
}

.quick-action:active {
  background: #f5f5f5;
}

.quick-action.disabled {
  opacity: 0.5;
}

.quick-action.disabled:active {
  background: transparent;
}

.quick-action.disabled .quick-text {
  color: #bfbfbf;
}

.quick-icon {
  font-size: 20px;
}

.quick-text {
  font-size: 12px;
  color: #595959;
}

// 轮播图容器
.banner-container {
  margin: -8px 20px 20px;
  position: relative;
  z-index: 2;
}

// 快捷服务
.quick-services {
  padding: 0 20px 20px;
}

.services-grid {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;

  &:active {
    transform: scale(0.95);
  }

  &.primary .service-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
    transform: scale(1.1);
  }
}

.service-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.service-title {
  font-size: 13px;
  color: #262626;
  font-weight: 500;
  text-align: center;
}

// 功能卡片区
.feature-cards {
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.2s;

  &:active {
    background: #fafafa;
    transform: scale(0.98);
  }
}

.feature-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.feature-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;

  &.query {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  }

  &.record {
    background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
  }
}

.feature-info {
  flex: 1;
}

.feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  display: block;
}

.feature-desc {
  font-size: 13px;
  color: #8c8c8c;
  display: block;
}

.feature-arrow {
  color: #d9d9d9;
  font-size: 18px;
  font-weight: bold;
}

.icon {
  color: white;
}

// 服务保障
.guarantee-section {
  margin: 0 20px 20px;
}

.guarantee-grid {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
}

.guarantee-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.guarantee-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.guarantee-text {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}

// 底部间距
.bottom-spacing {
  height: 40px;
}
</style>
