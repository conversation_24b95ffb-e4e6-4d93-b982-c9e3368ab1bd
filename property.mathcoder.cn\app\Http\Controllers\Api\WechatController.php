<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use EasyWeChat\MiniApp\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WechatController extends Controller
{
    /**
     * 微信小程序一键登录
     */
    public function login(Request $request)
    {
        $data = $request->validate([
            'code' => 'required|string',
            'phoneCode' => 'required|string',
        ]);

        try {
            $config = config('wechat.mini_program.default');
            $app = new Application($config);

            // 获取openid和session_key
            $utils = $app->getUtils();
            $code2session = $utils->codeToSession($data['code']);
            
            if (!isset($code2session['openid'])) {
                return response()->json([
                    'code' => 401,
                    'message' => '微信登录失败'
                ], 200);  // 使用 200 状态码，让错误信息能正确显示
            }

            // 解密手机号
            try {
                $phoneData = json_decode($data['phoneCode'], true);
                if (!$phoneData || !isset($phoneData['encryptedData']) || !isset($phoneData['iv'])) {
                    Log::error('手机号数据格式错误: ' . $data['phoneCode']);
                    return response()->json([
                        'code' => 401,
                        'message' => '手机号数据格式错误'
                    ], 200);
                }

                // Base64 解码数据
                $encryptedData = base64_decode($phoneData['encryptedData']);
                $iv = base64_decode($phoneData['iv']);
                $sessionKey = base64_decode($code2session['session_key']);

                // 使用 openssl 解密
                $decrypted = openssl_decrypt(
                    $encryptedData,
                    'AES-128-CBC',
                    $sessionKey,
                    OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING,
                    $iv
                );

                // 移除 PKCS#7 填充
                $pad = ord(substr($decrypted, -1));
                $decrypted = substr($decrypted, 0, -$pad);

                // 解析 JSON
                $phoneInfo = json_decode($decrypted, true);
                
                if (!is_array($phoneInfo) || !isset($phoneInfo['phoneNumber'])) {
                    Log::error('解密后的数据格式错误: ' . $decrypted);
                    return response()->json([
                        'code' => 401,
                        'message' => '获取手机号失败'
                    ], 200);
                }
            } catch (\Exception $e) {
                Log::error('解密手机号失败：' . $e->getMessage());
                return response()->json([
                    'code' => 401,
                    'message' => '获取手机号失败'
                ], 200);
            }

            // 查找或创建用户
            $user = User::firstOrCreate(
                ['phone' => $phoneInfo['phoneNumber']],
                [
                    'openid' => $code2session['openid'],
                    'name' => '用户' . substr($phoneInfo['phoneNumber'], -4),
                    'password' => bcrypt(Str::random(16)),
                ]
            );

            // 生成token
            $token = $user->createToken('wechat-mini')->plainTextToken;

            return response()->json([
                'code' => 0,  // 成功状态码为 0
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'userInfo' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'phone' => $user->phone,
                        'avatar' => $user->avatar,
                    ],
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('微信登录失败：' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '登录失败，请稍后重试'
            ], 200);
        }
    }

    /**
     * 退出登录
     */
    public function logout(Request $request)
    {
        try {
            // 删除当前用户的当前token
            $request->user()->currentAccessToken()->delete();
            
            return response()->json([
                'code' => 0,
                'message' => '退出成功'
            ], 200);
        } catch (\Exception $e) {
            Log::error('退出登录失败：' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '退出失败，请稍后重试'
            ], 200);
        }
    }
} 