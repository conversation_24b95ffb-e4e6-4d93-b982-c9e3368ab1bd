/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.8.0 (2021-05-06)
 */
!function(){"use strict";var e,t,n,r,i,o,u,a,s,c,l=function(t){var e=t;return{get:function(){return e},set:function(t){e=t}}},f=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=tinymce.util.Tools.resolve("tinymce.util.Tools"),h=function(t){return!(null===(e=t)||e===undefined);var e},m=function(t){return typeof t===e},g=function(){},v=function(t){return function(){return t}},p=v(!(e="function")),y=v(!0),w=function(){return b},b=(t=function(t){return t.isNone()},{fold:function(t,e){return t()},is:p,isSome:p,isNone:y,getOr:r=function(t){return t},getOrThunk:n=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:v(null),getOrUndefined:v(undefined),or:r,orThunk:n,map:w,each:g,bind:w,exists:p,forall:y,filter:w,equals:t,equals_:t,toArray:function(){return[]},toString:v("none()")}),I=function(n){var t=v(n),e=function(){return o},r=function(t){return t(n)},o={fold:function(t,e){return e(n)},is:function(t){return n===t},isSome:y,isNone:p,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(t){return I(t(n))},each:function(t){t(n)},bind:r,exists:r,forall:r,filter:function(t){return t(n)?o:b},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(t){return t.is(n)},equals_:function(t,e){return t.fold(p,function(t){return e(n,t)})}};return o},T={some:I,none:w,from:function(t){return null===t||t===undefined?b:I(t)}},_=function(t,e){return A(document.createElement("canvas"),t,e)},R=function(t){var e=_(t.width,t.height);return U(e).drawImage(t,0,0),e},U=function(t){return t.getContext("2d")},A=function(t,e,n){return t.width=e,t.height=n,t},x=window.Promise?window.Promise:(i=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],c(t,a(M,this),a(L,this))},o=window,u=i.immediateFn||"function"==typeof o.setImmediate&&o.setImmediate||function(t){return setTimeout(t,1)},a=function(n,r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n.apply(r,t)}},s=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},c=function(t,e,n){var r=!1;try{t(function(t){r||(r=!0,e(t))},function(t){r||(r=!0,n(t))})}catch(o){if(r)return;r=!0,n(o)}},i.prototype["catch"]=function(t){return this.then(null,t)},i.prototype.then=function(n,r){var o=this;return new i(function(t,e){E.call(o,new k(n,r,t,e))})},i.all=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var c=Array.prototype.slice.call(1===t.length&&s(t[0])?t[0]:t);return new i(function(o,i){if(0===c.length)return o([]);for(var u=c.length,a=function(e,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void n.call(t,function(t){a(e,t)},i)}c[e]=t,0==--u&&o(c)}catch(r){i(r)}},t=0;t<c.length;t++)a(t,c[t])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(t){t(e)})},i.reject=function(n){return new i(function(t,e){e(n)})},i.race=function(o){return new i(function(t,e){for(var n=0,r=o;n<r.length;n++)r[n].then(t,e)})},i);function E(r){var o=this;null!==this._state?u(function(){var t,e=o._state?r.onFulfilled:r.onRejected;if(null!==e){try{t=e(o._value)}catch(n){return void r.reject(n)}r.resolve(t)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function M(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void c(a(e,t),a(M,this),a(L,this))}this._state=!0,this._value=t,j.call(this)}catch(n){L.call(this,n)}}function L(t){this._state=!1,this._value=t,j.call(this)}function j(){for(var t=0,e=this._deferreds;t<e.length;t++){var n=e[t];E.call(this,n)}this._deferreds=[]}function k(t,e,n,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.resolve=n,this.reject=r}var C=function(a){return new x(function(t,e){var n=URL.createObjectURL(a),r=new Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",u)},i=function(){o(),t(r)},u=function(){o(),e("Unable to load data of type "+a.type+": "+n)};r.addEventListener("load",i),r.addEventListener("error",u),r.src=n,r.complete&&setTimeout(i,0)})},O=function(r){return new x(function(t,n){var e=new XMLHttpRequest;e.open("GET",r,!0),e.responseType="blob",e.onload=function(){200===this.status&&t(this.response)},e.onerror=function(){var t,e=this;n(0===this.status?((t=new Error("No access to download image")).code=18,t.name="SecurityError",t):new Error("Error "+e.status+" downloading image"))},e.send()})},P=function(n){return new x(function(t,e){(function(t){var e=t.split(","),n=/data:([^;]+)/.exec(e[0]);if(!n)return T.none();for(var r=n[1],o=e[1],i=atob(o),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,h=0;m<l;++h,++m)d[h]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return T.some(new Blob(c,{type:r}))})(n).fold(function(){e("uri is not base64: "+n)},t)})},S=function(t,r,o){return r=r||"image/png",m(HTMLCanvasElement.prototype.toBlob)?new x(function(e,n){t.toBlob(function(t){t?e(t):n()},r,o)}):P(t.toDataURL(r,o))},B=function(t){return C(t).then(function(t){N(t);var e,n,r=_((n=t).naturalWidth||n.width,(e=t).naturalHeight||e.height);return U(r).drawImage(t,0,0),r})},N=function(t){URL.revokeObjectURL(t.src)},D=C,F=function(t){return(0===(e=t.src).indexOf("data:")?P:O)(e);var e},H=function(t,e){return function(t,e,n){for(var r=0,o=t.length;r<o;r++){var i=t[r];if(e(i,r))return T.some(i);if(n(i,r))break}return T.none()}(t,e,p)},q=function(t,e,n){var r=e.type,o=v(r),i=v(n),u=function(r,o){return t.then(function(t){return n=o,e=(e=r)||"image/png",t.toDataURL(e,n);var e,n})};return{getType:o,toBlob:function(){return x.resolve(e)},toDataURL:i,toBase64:function(){return n.split(",")[1]},toAdjustedBlob:function(e,n){return t.then(function(t){return S(t,e,n)})},toAdjustedDataURL:u,toAdjustedBase64:function(t,e){return u(t,e).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(R)}}},z=function(e){return n=e,new x(function(t){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(n)}).then(function(t){return q(B(e),e,t)});var n},$=function(e,t){return S(e,t).then(function(t){return q(x.resolve(e),t,e.toDataURL())})},G=function(t,e){void 0===e&&(e=2);var n=Math.pow(10,e),r=Math.round(t*n);return Math.ceil(r/n)},J=function(t,e,n){var r=(n<0?360+n:n)*Math.PI/180,o=t.width,i=t.height,u=Math.sin(r),a=Math.cos(r),c=G(Math.abs(o*a)+Math.abs(i*u)),s=G(Math.abs(o*u)+Math.abs(i*a)),f=_(c,s),l=U(f);return l.translate(c/2,s/2),l.rotate(r),l.drawImage(t,-o/2,-i/2),$(f,e)},K=function(t,e,n){var r=_(t.width,t.height),o=U(r);return"v"===n?(o.scale(1,-1),o.drawImage(t,0,-r.height)):(o.scale(-1,1),o.drawImage(t,-r.width,0)),$(r,e)},V=function(t,e){return r=e,(n=t).toCanvas().then(function(t){return K(t,n.getType(),r)});var n,r},W=function(t,e){return r=e,(n=t).toCanvas().then(function(t){return J(t,n.getType(),r)});var n,r},X=Object.keys,Q=function(e,r,o){return void 0===o&&(o=!1),new x(function(t){var n=new XMLHttpRequest;n.onreadystatechange=function(){4===n.readyState&&t({status:n.status,blob:n.response})},n.open("GET",e,!0),n.withCredentials=o,function(t,e){for(var n=X(t),r=0,o=n.length;r<o;r++){var i=n[r];e(t[i],i)}}(r,function(t,e){n.setRequestHeader(e,t)}),n.responseType="blob",n.send()})},Y=[{code:404,message:"Could not find Image Proxy"},{code:403,message:"Rejected request"},{code:0,message:"Incorrect Image Proxy URL"}],Z=[{type:"not_found",message:"Failed to load image."},{type:"key_missing",message:"The request did not include an api key."},{type:"key_not_found",message:"The provided api key could not be found."},{type:"domain_not_trusted",message:"The api key is not valid for the request origins."}],tt=function(t,e){var n,r,o=(n=function(t,e){return h(t)?t[e]:undefined},r=t,function(t,e){for(var n=0,r=t.length;n<r;n++)e(t[n],n)}(e,function(t){r=n(r,t)}),r);return T.from(o)},et=function(t){var e,n=(e=t,"ImageProxy HTTP error: "+H(Y,function(t){return e===t.code}).fold(v("Unknown ImageProxy error"),function(t){return t.message}));return x.reject(n)},nt=function(e){return H(Z,function(t){return t.type===e}).fold(v("Unknown service error"),function(t){return t.message})},rt=function(t){return"ImageProxy Service error: "+function(t){try{return T.some(JSON.parse(t))}catch(e){return T.none()}}(t).bind(function(t){return tt(t,["error","type"]).map(nt)}).getOr("Invalid JSON in service error message")},ot=function(t){return r=t,new x(function(t,e){var n=new FileReader;n.onload=function(){t(n.result)},n.onerror=function(t){e(t)},n.readAsText(r)}).then(function(t){var e=rt(t);return x.reject(e)});var r},it=function(t){return t<200||300<=t},ut=function(t,e){var n,r,o,i={"Content-Type":"application/json;charset=UTF-8","tiny-api-key":e};return Q((r=e,o=-1===(n=t).indexOf("?")?"?":"&",/[?&]apiKey=/.test(n)?n:n+o+"apiKey="+encodeURIComponent(r)),i).then(function(t){return it(t.status)?(e=t.status,n=t.blob,r=e,"application/json"!==(null==(o=n)?void 0:o.type)||400!==r&&403!==r&&404!==r&&500!==r?et(e):ot(n)):x.resolve(t.blob);var e,n,r,o})},at=function(t,e,n){return void 0===n&&(n=!1),e?ut(t,e):Q(t,{},n).then(function(t){return it(t.status)?et(t.status):x.resolve(t.blob)})},ct=z,st=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:t}},ft={fromHtml:function(t,e){var n=(e||document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return st(n.childNodes[0])},fromTag:function(t,e){var n=(e||document).createElement(t);return st(n)},fromText:function(t,e){var n=(e||document).createTextNode(t);return st(n)},fromDom:st,fromPoint:function(t,e,n){return T.from(t.dom.elementFromPoint(e,n)).map(st)}},lt=("undefined"!=typeof window||Function("return this;")(),function(t,e){return n=function(t){return function(t,e){var n=t.dom;if(1!==n.nodeType)return!1;var r=n;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}(t,e)},H(t.dom.childNodes,function(t){return n(ft.fromDom(t))}).map(ft.fromDom);var n}),dt=tinymce.util.Tools.resolve("tinymce.util.Delay"),mt=tinymce.util.Tools.resolve("tinymce.util.Promise"),ht=tinymce.util.Tools.resolve("tinymce.util.URI"),gt=function(t){return t.getParam("imagetools_proxy")},vt=function(t){var e=function(t){return/^[0-9\.]+px$/.test(t)},n=t.style.width,r=t.style.height;return n||r?e(n)&&e(r)?{w:parseInt(n,10),h:parseInt(r,10)}:null:(n=t.width,r=t.height,n&&r?{w:parseInt(n,10),h:parseInt(r,10)}:null)},pt=function(t){return{w:t.naturalWidth,h:t.naturalHeight}},yt=0,wt=function(t){return lt(ft.fromDom(t),"img")},bt=function(t,e){return t.dom.is(e,"figure")},It=function(t,e){return t.dom.is(e,"img:not([data-mce-object],[data-mce-placeholder])")},Tt=function(e,t){var n=function(t){return It(e,t)&&(At(e,t)||xt(e,t)||h(gt(e)))};return bt(e,t)?wt(t).bind(function(t){return n(t.dom)?T.some(t.dom):T.none()}):n(t)?T.some(t):T.none()},_t=function(t,e){t.notificationManager.open({text:e,type:"error"})},Rt=function(t){var e=t.selection.getNode(),n=t.dom.getParent(e,"figure.image");return null!==n&&bt(t,n)?wt(n):It(t,e)?T.some(ft.fromDom(e)):T.none()},Ut=function(t,e,n){var r=e.match(/(?:\/|^)(([^\/\?]+)\.(?:[a-z0-9.]+))(?:\?|$)/i);return h(r)?t.dom.encode(r[n]):null},At=function(t,e){var n=e.src;return 0===n.indexOf("data:")||0===n.indexOf("blob:")||new ht(n).host===t.documentBaseURI.host},xt=function(t,e){return-1!==d.inArray(t.getParam("imagetools_cors_hosts",[],"string[]"),new ht(e.src).host)},Et=function(t,e){if(xt(t,e))return at(e.src,null,(n=t,r=e,-1!==d.inArray(n.getParam("imagetools_credentials_hosts",[],"string[]"),new ht(r.src).host)));var n,r,o;if(At(t,e))return F(e);var i=gt(t),u=i+(-1===i.indexOf("?")?"?":"&")+"url="+encodeURIComponent(e.src),a=(o=t).getParam("api_key",o.getParam("imagetools_api_key","","string"),"string");return at(u,a,!1)},Mt=function(t,e){return n=t,T.from(n.getParam("imagetools_fetch_image",null,"function")).fold(function(){return Et(t,e)},function(t){return t(e)});var n},Lt=function(t,e){var n=t.editorUpload.blobCache.getByUri(e.src);return n?mt.resolve(n.blob()):Mt(t,e)},jt=function(t){dt.clearTimeout(t.get())},kt=function(a,c,s,f,l,d,m){return s.toBlob().then(function(t){var e,n,o,r=a.editorUpload.blobCache,i=d.src,u=c.type===t.type;return a.getParam("images_reuse_filename",!1,"boolean")&&(o=r.getByUri(i),n=h(o)?(i=o.uri(),e=o.name(),o.filename()):(e=Ut(a,i,2),Ut(a,i,1))),o=r.create({id:"imagetools"+yt++,blob:t,base64:s.toBase64(),uri:i,name:e,filename:u?n:undefined}),r.add(o),a.undoManager.transact(function(){var r=function(){var t,e,n;a.$(d).off("load",r),a.nodeChanged(),f?a.editorUpload.uploadImagesAuto():(jt(l),t=a,e=l,n=dt.setEditorTimeout(t,function(){t.editorUpload.uploadImagesAuto()},t.getParam("images_upload_timeout",3e4,"number")),e.set(n))};a.$(d).on("load",r),m&&a.$(d).attr({width:m.w,height:m.h}),a.$(d).attr({src:o.blobUri()}).removeAttr("data-mce-src")}),o})},Ct=function(r,o,t,i){return function(){return Rt(r).fold(function(){_t(r,"Could not find selected image")},function(n){return r._scanForImages().then(function(){return Lt(r,n.dom)}).then(function(e){return ct(e).then(t).then(function(t){return kt(r,e,t,!1,o,n.dom,i)})})["catch"](function(t){_t(r,t)})})}},Ot=function(e,n,r){return function(){var t=Rt(e).fold(function(){return null},function(t){var e=vt(t.dom);return e?{w:e.h,h:e.w}:null});return Ct(e,n,function(t){return W(t,r)},t)()}},Pt=function(t,e,n){return function(){return Ct(t,e,function(t){return V(t,n)})()}},St=function(e,n,u,a,c){return D(c).then(function(t){var e,n,r,o,i=pt(t);return a.w===i.w&&a.h===i.h||vt(u)&&(e=u,(n=i)&&(r=e.style.width,o=e.style.height,(r||o)&&(e.style.width=n.w+"px",e.style.height=n.h+"px",e.removeAttribute("data-mce-style")),r=e.width,o=e.height,(r||o)&&(e.setAttribute("width",String(n.w)),e.setAttribute("height",String(n.h))))),URL.revokeObjectURL(t.src),c}).then(ct).then(function(t){return kt(e,c,t,!0,n,u)})},Bt=function(i,u){return function(){var r=Rt(i),o=r.map(function(t){return pt(t.dom)});r.each(function(e){Tt(i,e.dom).each(function(t){Lt(i,e.dom).then(function(t){var e,n={blob:e=t,url:URL.createObjectURL(e)};i.windowManager.open({title:"Edit Image",size:"large",body:{type:"panel",items:[{type:"imagetools",name:"imagetools",label:"Edit Image",currentState:n}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0,disabled:!0}],onSubmit:function(t){var n=t.getData().imagetools.blob;r.each(function(e){o.each(function(t){St(i,u,e.dom,t,n)})}),t.close()},onCancel:g,onAction:function(t,e){switch(e.name){case"save-state":e.value?t.enable("save"):t.disable("save");break;case"disable":t.disable("save"),t.disable("cancel");break;case"enable":t.enable("cancel")}}})})})})}};f.add("imagetools",function(t){var n,e,r,o,i,u,a,c,s=l(0),f=l(null);n=t,e=s,d.each({mceImageRotateLeft:Ot(n,e,-90),mceImageRotateRight:Ot(n,e,90),mceImageFlipVertical:Pt(n,e,"v"),mceImageFlipHorizontal:Pt(n,e,"h"),mceEditImage:Bt(n,e)},function(t,e){n.addCommand(e,t)}),o=function(t){return function(){return r.execCommand(t)}},(r=t).ui.registry.addButton("rotateleft",{tooltip:"Rotate counterclockwise",icon:"rotate-left",onAction:o("mceImageRotateLeft")}),r.ui.registry.addButton("rotateright",{tooltip:"Rotate clockwise",icon:"rotate-right",onAction:o("mceImageRotateRight")}),r.ui.registry.addButton("flipv",{tooltip:"Flip vertically",icon:"flip-vertically",onAction:o("mceImageFlipVertical")}),r.ui.registry.addButton("fliph",{tooltip:"Flip horizontally",icon:"flip-horizontally",onAction:o("mceImageFlipHorizontal")}),r.ui.registry.addButton("editimage",{tooltip:"Edit image",icon:"edit-image",onAction:o("mceEditImage"),onSetup:function(e){var t=function(){var t=Rt(r).forall(function(t){return Tt(r,t.dom).isNone()});e.setDisabled(t)};return r.on("NodeChange",t),function(){r.off("NodeChange",t)}}}),r.ui.registry.addButton("imageoptions",{tooltip:"Image options",icon:"image",onAction:o("mceImage")}),r.ui.registry.addContextMenu("imagetools",{update:function(t){return Tt(r,t).fold(function(){return[]},function(t){return[{text:"Edit image",icon:"edit-image",onAction:o("mceEditImage")}]})}}),(i=t).ui.registry.addContextToolbar("imagetools",{items:i.getParam("imagetools_toolbar","rotateleft rotateright flipv fliph editimage imageoptions"),predicate:function(t){return Tt(i,t).isSome()},position:"node",scope:"node"}),a=s,c=f,(u=t).on("NodeChange",function(t){var e=c.get(),n=Tt(u,t.element);e&&!n.exists(function(t){return e.src===t.src})&&(jt(a),u.editorUpload.uploadImagesAuto(),c.set(null)),n.each(c.set)})})}();