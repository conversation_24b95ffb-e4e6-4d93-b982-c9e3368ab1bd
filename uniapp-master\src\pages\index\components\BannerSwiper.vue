<template>
  <!-- 只有在有轮播图数据时才显示整个组件 -->
  <view class="relative" v-if="banners.length > 0">
    <swiper
      class="w-full h-40 rounded-lg overflow-hidden"
      circular
      :indicator-dots="true"
      :autoplay="true"
      :interval="3000"
      :duration="1000"
      indicator-active-color="#4F46E5"
    >
      <swiper-item v-for="item in banners" :key="item.id">
        <view class="w-full h-full" @tap="handleClick(item)">
          <image
            :src="item.image_url"
            class="w-full h-full"
            mode="aspectFill"
            @error="handleImageError(item)"
          />
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getCarousels } from '@/api/carousel'

interface Banner {
  id: number
  image_url: string
  link_type: 'mini_page' | 'mini_program' | 'external_url' | 'none'
  link_target?: string
  link_params?: Record<string, any>
}

const banners = ref<Banner[]>([])

// 获取轮播图数据
const fetchBanners = async () => {
  try {
    const res = await getCarousels()
    console.log('轮播图响应:', res)
    if (res.code === 0 || res.code === 10000) {
      if (Array.isArray(res.data)) {
        // 过滤掉无效的图片URL
        banners.value = res.data.filter(banner => banner.image_url)
        console.log('轮播图数据设置成功:', banners.value)
        // 打印每个banner的链接信息
        banners.value.forEach(banner => {
          console.log('Banner链接信息:', {
            id: banner.id,
            link_type: banner.link_type,
            link_target: banner.link_target,
            link_params: banner.link_params
          })
        })
      } else {
        console.error('轮播图数据格式错误:', res.data)
      }
    } else {
      console.error('获取轮播图失败:', res)
    }
  } catch (error) {
    console.error('获取轮播图请求异常:', error)
  }
}

// 处理图片加载错误
const handleImageError = (banner: Banner) => {
  console.error('图片加载失败:', banner.image_url)
  // 从轮播图列表中移除加载失败的图片
  banners.value = banners.value.filter(item => item.id !== banner.id)
}

// 处理轮播图点击
const handleClick = (banner: Banner) => {
  console.log('Banner clicked:', banner)
  
  if (!banner.link_type || banner.link_type === 'none') {
    console.log('Banner has no link type or is none type')
    return
  }

  switch (banner.link_type) {
    case 'mini_page':
      // 跳转到小程序内页
      if (banner.link_target) {
        console.log('Navigating to mini page:', banner.link_target)
        const params = banner.link_params ? '?' + Object.entries(banner.link_params)
          .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
          .join('&') : ''
        const fullUrl = banner.link_target + params
        console.log('Full URL:', fullUrl)
        uni.navigateTo({
          url: fullUrl,
          success: () => {
            console.log('页面跳转成功')
          },
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        console.log('No link target for mini page')
      }
      break
    case 'mini_program':
      // 跳转到其他小程序
      if (banner.link_target) {
        console.log('Navigating to mini program:', banner.link_target)
        const [appId, path] = banner.link_target.split(';')
        uni.navigateToMiniProgram({
          appId,
          path,
          success: () => {
            console.log('跳转成功')
          },
          fail: (err) => {
            console.error('跳转失败:', err)
            uni.showToast({
              title: '跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        console.log('No link target for mini program')
      }
      break
    case 'external_url':
      // 复制外部链接
      if (banner.link_target) {
        console.log('Copying external URL:', banner.link_target)
        uni.setClipboardData({
          data: banner.link_target,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          },
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            })
          }
        })
      } else {
        console.log('No link target for external URL')
      }
      break
  }
}

onMounted(() => {
  fetchBanners()
})
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'BannerSwiper'
})
</script>
