APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


# 小程序登录
WECHAT_MINI_PROGRAM_APPID=your_miniprogram_appid
WECHAT_MINI_PROGRAM_SECRET=your_miniprogram_secret
WECHAT_MINI_PROGRAM_TOKEN=可选

# 微信支付
WECHAT_PAYMENT_MCH_ID=你的_商户号
WECHAT_PAYMENT_KEY=你的_商户支付_KEY
WECHAT_PAYMENT_CERT_PATH=/full/path/to/apiclient_cert.pem
WECHAT_PAYMENT_KEY_PATH=/full/path/to/apiclient_key.pem
WECHAT_PAYMENT_NOTIFY_URL=https://your.domain.com/api/payment/notify

# e签宝
ESIGN_APP_ID=你的e签宝AppId
ESIGN_SECRET=你的e签宝Secret
ESIGN_HOST=https://openapi.esign.cn      # or 体验环境：https://openapiuat.esign.cn
ESIGN_CALLBACK_URL=https://你的域名/admin/esign/callback


