<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('water_bills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->decimal('water_usage', 10, 2);
            $table->string('bill_number')->unique();
            $table->date('bill_date');
            $table->date('due_date');
            $table->enum('status', ['unpaid', 'paid', 'overdue']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('water_bills');
    }
}; 