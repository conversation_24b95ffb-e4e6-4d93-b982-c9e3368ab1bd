<?php

namespace App\Admin\Controllers;

use App\Models\AdCarousel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Storage;

class AdCarouselController extends AdminController
{
    protected $title = '轮播图广告管理';

    protected function grid(): Grid
    {
        return Grid::make(new AdCarousel(), function (Grid $grid) {
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('image_url', '广告图')
             ->display(function (string $path) {
                 // Storage::disk('public')->url('images/xxx.png') => '/storage/images/xxx.png'
                 $url = Storage::disk('public')->url($path);
                 return "<img src=\"{$url}\" style=\"max-width:120px;max-height:60px\" />";
             })
             ->help('建议尺寸：640×320，格式 JPG/PNG，大小 ≤200KB');
            $grid->column('link_type', '跳转类型')->using(AdCarousel::$linkTypes)->badge([
                AdCarousel::LINK_NONE         => 'default',
                AdCarousel::LINK_MINI_PAGE    => 'info',
                AdCarousel::LINK_MINI_PROGRAM => 'warning',
                AdCarousel::LINK_EXTERNAL_URL => 'success',
            ]);
            $grid->column('link_target', '跳转目标')
                ->limit(30)
                ->display(function ($value) {
                    return $value
                        ? e($value)
                        : '<span class="text-muted">未设置跳转</span>';
                });

            $grid->column('sort_order', '排序')->sortable();
            $grid->column('status', '状态')->using(AdCarousel::$statuses)->badge([
                AdCarousel::STATUS_INACTIVE => 'danger',
                AdCarousel::STATUS_ACTIVE   => 'success',
            ]);
            $grid->column('start_at', '生效开始')->sortable();
            $grid->column('end_at',   '生效结束')->sortable();
            $grid->column('created_at','创建时间')->sortable();
            $grid->column('updated_at','更新时间')->sortable();

            $grid->filter(fn($filter) => [
                $filter->equal('status','状态')->select(AdCarousel::$statuses),
                $filter->equal('link_type','跳转类型')->select(AdCarousel::$linkTypes),
                $filter->between('start_at','生效时间')->datetime(),
                $filter->like('link_target','目标关键字'),
            ]);

            $grid->quickSearch('link_target')->placeholder('请输入跳转目标关键词');
            $grid->enableDialogCreate();
            $grid->disableBatchActions();
        });
    }

    protected function form(): Form
    {
        return Form::make(AdCarousel::class, function (Form $form) {
            $form->display('id', 'ID');

            // 图片上传
            $form->image('image_url', '广告图片')
                 ->disk('public') 
                 ->required()
                 ->uniqueName()
                 ->autoUpload()
                 ->removable()
                 ->help('请上传：建议 640×320，JPG/PNG，≤200KB');

            // 跳转类型：单选
            $form->radio('link_type', '跳转类型')
                 ->options(AdCarousel::$linkTypes)
                 ->default(AdCarousel::LINK_NONE)
                 ->required()
                 ->help('选择后下面会出现对应的“跳转目标”单选框')
                 // 动态显示不同的 link_target 单选
                 ->when(AdCarousel::LINK_MINI_PAGE, function (Form $form) {
                     $form->text('link_target', '跳转目标')
                          ->help('请选择小程序内部页面链接');
                 })
                 ->when(AdCarousel::LINK_MINI_PROGRAM, function (Form $form) {
                     $form->text('link_target', '跳转目标')
                          ->help('请选择目标小程序及页面 (appid;path)');
                 })
                 ->when(AdCarousel::LINK_EXTERNAL_URL, function (Form $form) {
                     $form->text('link_target', '跳转目标')
                          ->help('请选择外部 HTTPS 链接');
                 })
                 ->when(AdCarousel::LINK_NONE, function (Form $form) {
                     $form->text('link_target', '跳转目标')
                          ->help('请选择外部 HTTPS 链接')
                          ->hideInDialog();
                 });
            // 链接类型为 none 时，不渲染 link_target

            $form->number('sort_order', '排序值')
                 ->default(0)
                 ->help('数值越大越靠前');

            $form->radio('status', '状态')
                 ->options(AdCarousel::$statuses)
                 ->default(AdCarousel::STATUS_ACTIVE)
                 ->help('禁用后前端不展示');

            $form->datetime('start_at', '生效开始')
                 ->placeholder('留空立即生效');
            $form->datetime('end_at', '生效结束')
                 ->placeholder('留空长期有效');


            // 去掉多余按钮
            $form->footer(function ($footer) {
                $footer->disableViewCheck();
                $footer->disableEditingCheck();
                $footer->disableCreatingCheck();
            });
        });
    }



    protected function detail($id): Show
    {
        return Show::make($id, new AdCarousel(), function (Show $show) {
            $show->field('id');
            $show->field('image_url')->asImage(300,150);
            $show->field('link_type')->using(AdCarousel::$linkTypes);
            $show->field('link_target');
            $show->field('sort_order');
            $show->field('status')->using(AdCarousel::$statuses);
            $show->field('start_at');
            $show->field('end_at');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }
}
